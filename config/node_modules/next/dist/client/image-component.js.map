{"version": 3, "sources": ["../../src/client/image-component.tsx"], "names": ["Image", "configEnv", "process", "env", "__NEXT_IMAGE_OPTS", "window", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "placeholder", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "unoptimized", "sizesInput", "src", "p", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "current", "event", "Event", "Object", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "NODE_ENV", "origSrc", "URL", "searchParams", "get", "getAttribute", "widthViewportRatio", "getBoundingClientRect", "width", "innerWidth", "warnOnce", "position", "getComputedStyle", "valid", "includes", "map", "String", "join", "height", "heightModified", "toString", "widthModified", "getDynamicProps", "fetchPriority", "Boolean", "use", "fetchpriority", "ImageElement", "forwardRef", "forwardedRef", "srcSet", "sizes", "decoding", "className", "style", "loading", "fill", "setShowAltText", "onLoad", "onError", "rest", "data-nimg", "ref", "useCallback", "console", "error", "complete", "ImagePreload", "isAppRouter", "imgAttributes", "opts", "as", "imageSrcSet", "imageSizes", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "Head", "link", "rel", "href", "undefined", "props", "pagesRouter", "useContext", "RouterContext", "configContext", "ImageConfigContext", "config", "useMemo", "c", "imageConfigDefault", "allSizes", "deviceSizes", "sort", "a", "b", "qualities", "onLoadingComplete", "useRef", "useEffect", "blurComplete", "useState", "showAltText", "meta", "imgMeta", "getImgProps", "defaultLoader", "imgConf", "priority"], "mappings": "AAAA;;;;;+BA6WaA;;;eAAAA;;;;;;iEAlWN;mEACc;+DACJ;6BACW;6BAYO;iDACA;0BACV;4CACK;sEAGJ;AAE1B,4CAA4C;AAC5C,MAAMC,YAAYC,QAAQC,GAAG,CAACC,iBAAiB;AAE/C,IAAI,OAAOC,WAAW,aAAa;IAC/BC,WAAmBC,qBAAqB,GAAG;AAC/C;AAmBA,0EAA0E;AAC1E,iDAAiD;AACjD,SAASC,cACPC,GAA2B,EAC3BC,WAA6B,EAC7BC,SAAqD,EACrDC,oBAA2E,EAC3EC,eAAqC,EACrCC,WAAoB,EACpBC,UAA8B;IAE9B,MAAMC,MAAMP,uBAAAA,IAAKO,GAAG;IACpB,IAAI,CAACP,OAAOA,GAAG,CAAC,kBAAkB,KAAKO,KAAK;QAC1C;IACF;IACAP,GAAG,CAAC,kBAAkB,GAAGO;IACzB,MAAMC,IAAI,YAAYR,MAAMA,IAAIS,MAAM,KAAKC,QAAQC,OAAO;IAC1DH,EAAEI,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC;QACrB,IAAI,CAACb,IAAIc,aAAa,IAAI,CAACd,IAAIe,WAAW,EAAE;YAC1C,wCAAwC;YACxC,uBAAuB;YACvB,sCAAsC;YACtC,sBAAsB;YACtB,uBAAuB;YACvB;QACF;QACA,IAAId,gBAAgB,SAAS;YAC3BG,gBAAgB;QAClB;QACA,IAAIF,6BAAAA,UAAWc,OAAO,EAAE;YACtB,+CAA+C;YAC/C,0CAA0C;YAC1C,2CAA2C;YAC3C,MAAMC,QAAQ,IAAIC,MAAM;YACxBC,OAAOC,cAAc,CAACH,OAAO,UAAU;gBAAEI,UAAU;gBAAOC,OAAOtB;YAAI;YACrE,IAAIuB,YAAY;YAChB,IAAIC,UAAU;YACdtB,UAAUc,OAAO,CAAC;gBAChB,GAAGC,KAAK;gBACRQ,aAAaR;gBACbS,eAAe1B;gBACf2B,QAAQ3B;gBACR4B,oBAAoB,IAAML;gBAC1BM,sBAAsB,IAAML;gBAC5BM,SAAS,KAAO;gBAChBC,gBAAgB;oBACdR,YAAY;oBACZN,MAAMc,cAAc;gBACtB;gBACAC,iBAAiB;oBACfR,UAAU;oBACVP,MAAMe,eAAe;gBACvB;YACF;QACF;QACA,IAAI7B,wCAAAA,qBAAsBa,OAAO,EAAE;YACjCb,qBAAqBa,OAAO,CAAChB;QAC/B;QACA,IAAIP,QAAQC,GAAG,CAACuC,QAAQ,KAAK,cAAc;YACzC,MAAMC,UAAU,IAAIC,IAAI5B,KAAK,YAAY6B,YAAY,CAACC,GAAG,CAAC,UAAU9B;YACpE,IAAIP,IAAIsC,YAAY,CAAC,iBAAiB,QAAQ;gBAC5C,IAAI,CAACjC,eAAgB,CAAA,CAACC,cAAcA,eAAe,OAAM,GAAI;oBAC3D,IAAIiC,qBACFvC,IAAIwC,qBAAqB,GAAGC,KAAK,GAAG7C,OAAO8C,UAAU;oBACvD,IAAIH,qBAAqB,KAAK;wBAC5B,IAAIjC,eAAe,SAAS;4BAC1BqC,IAAAA,kBAAQ,EACN,AAAC,qBAAkBT,UAAQ;wBAE/B,OAAO;4BACLS,IAAAA,kBAAQ,EACN,AAAC,qBAAkBT,UAAQ;wBAE/B;oBACF;gBACF;gBACA,IAAIlC,IAAIc,aAAa,EAAE;oBACrB,MAAM,EAAE8B,QAAQ,EAAE,GAAGhD,OAAOiD,gBAAgB,CAAC7C,IAAIc,aAAa;oBAC9D,MAAMgC,QAAQ;wBAAC;wBAAY;wBAAS;qBAAW;oBAC/C,IAAI,CAACA,MAAMC,QAAQ,CAACH,WAAW;wBAC7BD,IAAAA,kBAAQ,EACN,AAAC,qBAAkBT,UAAQ,wEAAqEU,WAAS,wBAAqBE,MAC3HE,GAAG,CAACC,QACJC,IAAI,CAAC,OAAK;oBAEjB;gBACF;gBACA,IAAIlD,IAAImD,MAAM,KAAK,GAAG;oBACpBR,IAAAA,kBAAQ,EACN,AAAC,qBAAkBT,UAAQ;gBAE/B;YACF;YAEA,MAAMkB,iBACJpD,IAAImD,MAAM,CAACE,QAAQ,OAAOrD,IAAIsC,YAAY,CAAC;YAC7C,MAAMgB,gBAAgBtD,IAAIyC,KAAK,CAACY,QAAQ,OAAOrD,IAAIsC,YAAY,CAAC;YAChE,IACE,AAACc,kBAAkB,CAACE,iBACnB,CAACF,kBAAkBE,eACpB;gBACAX,IAAAA,kBAAQ,EACN,AAAC,qBAAkBT,UAAQ;YAE/B;QACF;IACF;AACF;AAEA,SAASqB,gBACPC,aAAsB;IAEtB,IAAIC,QAAQC,UAAG,GAAG;QAChB,kDAAkD;QAClD,iDAAiD;QACjD,mDAAmD;QACnD,OAAO;YAAEF;QAAc;IACzB;IACA,uDAAuD;IACvD,4CAA4C;IAC5C,OAAO;QAAEG,eAAeH;IAAc;AACxC;AAEA,MAAMI,6BAAeC,IAAAA,iBAAU,EAC7B,QAwBEC;QAvBA,EACEvD,GAAG,EACHwD,MAAM,EACNC,KAAK,EACLb,MAAM,EACNV,KAAK,EACLwB,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLX,aAAa,EACbvD,WAAW,EACXmE,OAAO,EACP/D,WAAW,EACXgE,IAAI,EACJnE,SAAS,EACTC,oBAAoB,EACpBC,eAAe,EACfkE,cAAc,EACdhE,UAAU,EACViE,MAAM,EACNC,OAAO,EACP,GAAGC,MACJ;IAGD,qBACE,qBAACzE;QACE,GAAGyE,IAAI;QACP,GAAGlB,gBAAgBC,cAAc;QAClC,qEAAqE;QACrE,wEAAwE;QACxE,qDAAqD;QACrDY,SAASA;QACT3B,OAAOA;QACPU,QAAQA;QACRc,UAAUA;QACVS,aAAWL,OAAO,SAAS;QAC3BH,WAAWA;QACXC,OAAOA;QACP,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,0EAA0E;QAC1E,2BAA2B;QAC3B,sDAAsD;QACtDH,OAAOA;QACPD,QAAQA;QACRxD,KAAKA;QACLoE,KAAKC,IAAAA,kBAAW,EACd,CAAC5E;YACC,IAAI8D,cAAc;gBAChB,IAAI,OAAOA,iBAAiB,YAAYA,aAAa9D;qBAChD,IAAI,OAAO8D,iBAAiB,UAAU;oBACzC,+EAA+E;oBAC/EA,aAAa9C,OAAO,GAAGhB;gBACzB;YACF;YACA,IAAI,CAACA,KAAK;gBACR;YACF;YACA,IAAIwE,SAAS;gBACX,2EAA2E;gBAC3E,iFAAiF;gBACjF,kFAAkF;gBAClF,0CAA0C;gBAC1CxE,IAAIO,GAAG,GAAGP,IAAIO,GAAG;YACnB;YACA,IAAId,QAAQC,GAAG,CAACuC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAAC1B,KAAK;oBACRsE,QAAQC,KAAK,CAAE,6CAA4C9E;gBAC7D;gBACA,IAAIA,IAAIsC,YAAY,CAAC,WAAW,MAAM;oBACpCuC,QAAQC,KAAK,CACV;gBAEL;YACF;YACA,IAAI9E,IAAI+E,QAAQ,EAAE;gBAChBhF,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;YAEJ;QACF,GACA;YACEC;YACAN;YACAC;YACAC;YACAC;YACAoE;YACAnE;YACAC;YACAwD;SACD;QAEHS,QAAQ,CAACtD;YACP,MAAMjB,MAAMiB,MAAMS,aAAa;YAC/B3B,cACEC,KACAC,aACAC,WACAC,sBACAC,iBACAC,aACAC;QAEJ;QACAkE,SAAS,CAACvD;YACR,qEAAqE;YACrEqD,eAAe;YACf,IAAIrE,gBAAgB,SAAS;gBAC3B,2EAA2E;gBAC3EG,gBAAgB;YAClB;YACA,IAAIoE,SAAS;gBACXA,QAAQvD;YACV;QACF;;AAGN;AAGF,SAAS+D,aAAa,KAMrB;IANqB,IAAA,EACpBC,WAAW,EACXC,aAAa,EAId,GANqB;IAOpB,MAAMC,OAAO;QACXC,IAAI;QACJC,aAAaH,cAAcnB,MAAM;QACjCuB,YAAYJ,cAAclB,KAAK;QAC/BuB,aAAaL,cAAcK,WAAW;QACtCC,gBAAgBN,cAAcM,cAAc;QAC5C,GAAGjC,gBAAgB2B,cAAc1B,aAAa,CAAC;IACjD;IAEA,IAAIyB,eAAeQ,iBAAQ,CAACC,OAAO,EAAE;QACnC,mDAAmD;QACnDD,iBAAQ,CAACC,OAAO,CACdR,cAAc3E,GAAG,EACjB,8DAA8D;QAC9D4E;QAEF,OAAO;IACT;IAEA,qBACE,qBAACQ,aAAI;kBACH,cAAA,qBAACC;YAOCC,KAAI;YACJ,sEAAsE;YACtE,qEAAqE;YACrE,sDAAsD;YACtD,EAAE;YACF,8EAA8E;YAC9EC,MAAMZ,cAAcnB,MAAM,GAAGgC,YAAYb,cAAc3E,GAAG;YACzD,GAAG4E,IAAI;WAZN,YACAD,cAAc3E,GAAG,GACjB2E,cAAcnB,MAAM,GACpBmB,cAAclB,KAAK;;AAa7B;AAOO,MAAMzE,sBAAQsE,IAAAA,iBAAU,EAC7B,CAACmC,OAAOlC;IACN,MAAMmC,cAAcC,IAAAA,iBAAU,EAACC,yCAAa;IAC5C,0DAA0D;IAC1D,MAAMlB,cAAc,CAACgB;IAErB,MAAMG,gBAAgBF,IAAAA,iBAAU,EAACG,mDAAkB;IACnD,MAAMC,SAASC,IAAAA,cAAO,EAAC;YAIHC;QAHlB,MAAMA,IAAIhH,aAAa4G,iBAAiBK,+BAAkB;QAC1D,MAAMC,WAAW;eAAIF,EAAEG,WAAW;eAAKH,EAAElB,UAAU;SAAC,CAACsB,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACxE,MAAMH,cAAcH,EAAEG,WAAW,CAACC,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QACrD,MAAMC,aAAYP,eAAAA,EAAEO,SAAS,qBAAXP,aAAaI,IAAI,CAAC,CAACC,GAAGC,IAAMD,IAAIC;QAClD,OAAO;YAAE,GAAGN,CAAC;YAAEE;YAAUC;YAAaI;QAAU;IAClD,GAAG;QAACX;KAAc;IAElB,MAAM,EAAE7B,MAAM,EAAEyC,iBAAiB,EAAE,GAAGhB;IACtC,MAAM9F,YAAY+G,IAAAA,aAAM,EAAC1C;IAEzB2C,IAAAA,gBAAS,EAAC;QACRhH,UAAUc,OAAO,GAAGuD;IACtB,GAAG;QAACA;KAAO;IAEX,MAAMpE,uBAAuB8G,IAAAA,aAAM,EAACD;IAEpCE,IAAAA,gBAAS,EAAC;QACR/G,qBAAqBa,OAAO,GAAGgG;IACjC,GAAG;QAACA;KAAkB;IAEtB,MAAM,CAACG,cAAc/G,gBAAgB,GAAGgH,IAAAA,eAAQ,EAAC;IACjD,MAAM,CAACC,aAAa/C,eAAe,GAAG8C,IAAAA,eAAQ,EAAC;IAE/C,MAAM,EAAEpB,OAAOd,aAAa,EAAEoC,MAAMC,OAAO,EAAE,GAAGC,IAAAA,wBAAW,EAACxB,OAAO;QACjEyB,eAAAA,oBAAa;QACbC,SAASpB;QACTa;QACAE;IACF;IAEA,qBACE;;0BAEI,qBAACzD;gBACE,GAAGsB,aAAa;gBACjB7E,aAAakH,QAAQlH,WAAW;gBAChCJ,aAAasH,QAAQtH,WAAW;gBAChCoE,MAAMkD,QAAQlD,IAAI;gBAClBnE,WAAWA;gBACXC,sBAAsBA;gBACtBC,iBAAiBA;gBACjBkE,gBAAgBA;gBAChBhE,YAAY0F,MAAMhC,KAAK;gBACvBW,KAAKb;;YAGRyD,QAAQI,QAAQ,iBACf,qBAAC3C;gBACCC,aAAaA;gBACbC,eAAeA;iBAEf;;;AAGV"}