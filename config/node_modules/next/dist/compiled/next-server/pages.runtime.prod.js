(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function i(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,o],...s]=i(e),{domain:a,expires:l,httponly:c,maxage:p,path:h,samesite:f,secure:m,partitioned:g,priority:v}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(o),domain:a,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:h,...f&&{sameSite:d.includes(t=(t=f).toLowerCase())?t:void 0},...m&&{secure:!0},...v&&{priority:u.includes(r=(r=v).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>c,ResponseCookies:()=>p,parseCookie:()=>i,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,i)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let a of n(s))o.call(e,a)||void 0===a||t(e,a,{get:()=>s[a],enumerable:!(i=r(s,a))||i.enumerable});return e})(t({},"__esModule",{value:!0}),s);var d=["strict","lax","none"],u=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of i(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let o=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(o)?o:function(e){if(!e)return[];var t,r,n,o,s,a=[],i=0;function l(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,s=!1;l();)if(","===(r=e.charAt(i))){for(n=i,i+=1,l(),o=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(s=!0,i=o,a.push(e.substring(t,n)),t=i):i=n+1}else i+=1;(!s||i>=e.length)&&a.push(e.substring(t,e.length))}return a}(o)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},s=t.split(n),a=(r||{}).decode||e,i=0;i<s.length;i++){var l=s[i],d=l.indexOf("=");if(!(d<0)){var u=l.substr(0,d).trim(),c=l.substr(++d,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==o[u]&&(o[u]=function(e,t){try{return t(e)}catch(t){return e}}(c,a))}}return o},t.serialize=function(e,t,n){var s=n||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var i=a(t);if(i&&!o.test(i))throw TypeError("argument val is invalid");var l=e+"="+i;if(null!=s.maxAge){var d=s.maxAge-0;if(isNaN(d)||!isFinite(d))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(d)}if(s.domain){if(!o.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!o.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},"./dist/compiled/react-is/cjs/react-is.production.min.js":(e,t)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),d=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case i:case a:case p:case h:return e;default:switch(e=e&&e.$$typeof){case u:case d:case c:case m:case f:case l:return e;default:return t}}case o:return t}}}r=Symbol.for("react.module.reference"),t.ContextConsumer=d,t.ContextProvider=l,t.Element=n,t.ForwardRef=c,t.Fragment=s,t.Lazy=m,t.Memo=f,t.Portal=o,t.Profiler=i,t.StrictMode=a,t.Suspense=p,t.SuspenseList=h,t.isAsyncMode=function(){return!1},t.isConcurrentMode=function(){return!1},t.isContextConsumer=function(e){return v(e)===d},t.isContextProvider=function(e){return v(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return v(e)===c},t.isFragment=function(e){return v(e)===s},t.isLazy=function(e){return v(e)===m},t.isMemo=function(e){return v(e)===f},t.isPortal=function(e){return v(e)===o},t.isProfiler=function(e){return v(e)===i},t.isStrictMode=function(e){return v(e)===a},t.isSuspense=function(e){return v(e)===p},t.isSuspenseList=function(e){return v(e)===h},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===i||e===a||e===p||e===h||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===f||e.$$typeof===l||e.$$typeof===d||e.$$typeof===c||e.$$typeof===r||void 0!==e.getModuleId)},t.typeOf=v},"./dist/compiled/react-is/index.js":(e,t,r)=>{"use strict";e.exports=r("./dist/compiled/react-is/cjs/react-is.production.min.js")},"./dist/compiled/strip-ansi/index.js":e=>{(()=>{"use strict";var t={511:e=>{e.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")},532:(e,t,r)=>{let n=r(511);e.exports=e=>"string"==typeof e?e.replace(n(),""):e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab=__dirname+"/";var o=n(532);e.exports=o})()},"./dist/esm/build/output/log.js":(e,t,r)=>{"use strict";var n;r.d(t,{ZK:()=>v});let{env:o,stdout:s}=(null==(n=globalThis)?void 0:n.process)??{},a=o&&!o.NO_COLOR&&(o.FORCE_COLOR||(null==s?void 0:s.isTTY)&&!o.CI&&"dumb"!==o.TERM),i=(e,t,r,n)=>{let o=e.substring(0,n)+r,s=e.substring(n+t.length),a=s.indexOf(t);return~a?o+i(s,t,r,a):o+s},l=(e,t,r=e)=>a?n=>{let o=""+n,s=o.indexOf(t,e.length);return~s?e+i(o,t,r,s)+t:e+o+t}:String,d=l("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m");l("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),l("\x1b[3m","\x1b[23m"),l("\x1b[4m","\x1b[24m"),l("\x1b[7m","\x1b[27m"),l("\x1b[8m","\x1b[28m"),l("\x1b[9m","\x1b[29m"),l("\x1b[30m","\x1b[39m");let u=l("\x1b[31m","\x1b[39m"),c=l("\x1b[32m","\x1b[39m"),p=l("\x1b[33m","\x1b[39m");l("\x1b[34m","\x1b[39m");let h=l("\x1b[35m","\x1b[39m");l("\x1b[38;2;173;127;168m","\x1b[39m"),l("\x1b[36m","\x1b[39m");let f=l("\x1b[37m","\x1b[39m");l("\x1b[90m","\x1b[39m"),l("\x1b[40m","\x1b[49m"),l("\x1b[41m","\x1b[49m"),l("\x1b[42m","\x1b[49m"),l("\x1b[43m","\x1b[49m"),l("\x1b[44m","\x1b[49m"),l("\x1b[45m","\x1b[49m"),l("\x1b[46m","\x1b[49m"),l("\x1b[47m","\x1b[49m");let m={wait:f(d("○")),error:u(d("⨯")),warn:p(d("⚠")),ready:"▲",info:f(d(" ")),event:c(d("✓")),trace:h(d("»"))},g={log:"log",warn:"warn",error:"error"};function v(...e){!function(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in g?g[e]:"log",n=m[e];0===t.length?console[r](""):console[r](" "+n,...t)}("warn",...e)}},"./dist/esm/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{BR:()=>s,Ei:()=>d,Eo:()=>p,Lx:()=>c,Qq:()=>o,Wo:()=>i,lk:()=>h,oL:()=>l,q6:()=>u,wh:()=>a,y3:()=>n});let n="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=31536e3,a="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",i="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",l="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",d="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",u="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",c="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",p="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",h="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",f={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...f,GROUP:{serverOnly:[f.reactServerComponents,f.actionBrowser,f.appMetadataRoute,f.appRouteHandler,f.instrument],clientOnly:[f.serverSideRendering,f.appPagesBrowser],nonClientServerTarget:[f.middleware,f.api],app:[f.reactServerComponents,f.actionBrowser,f.appMetadataRoute,f.appRouteHandler,f.serverSideRendering,f.appPagesBrowser,f.shared,f.instrument]}})},"./dist/esm/server/api-utils/index.js":(e,t,r)=>{"use strict";r.d(t,{Di:()=>l,Iq:()=>s,Lm:()=>u,QM:()=>i,dS:()=>a,gk:()=>c});var n=r("./dist/esm/server/web/spec-extension/adapters/headers.js"),o=r("./dist/esm/lib/constants.js");function s(e,t){let r=n.h.from(e.headers);return{isOnDemandRevalidate:r.get(o.y3)===t.previewModeId,revalidateOnlyGenerated:r.has(o.Qq)}}r("./lib/trace/tracer"),r("./dist/esm/server/lib/trace/constants.js");let a="__prerender_bypass",i="__next_preview_data",l=Symbol(i),d=Symbol(a);function u(e,t={}){if(d in e)return e;let{serialize:n}=r("./dist/compiled/cookie/index.js"),o=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof o?[o]:Array.isArray(o)?o:[],n(a,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0}),n(i,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0})]),Object.defineProperty(e,d,{value:!0,enumerable:!1}),e}function c({req:e},t,r){let n={configurable:!0,enumerable:!0},o={...n,writable:!0};Object.defineProperty(e,t,{...n,get:()=>{let n=r();return Object.defineProperty(e,t,{...o,value:n}),n},set:r=>{Object.defineProperty(e,t,{...o,value:r})}})}},"./dist/esm/server/api-utils/node/try-get-preview-data.js":(e,t,r)=>{"use strict";r.d(t,{R:()=>a});var n=r("./dist/esm/server/api-utils/index.js"),o=r("./dist/esm/server/web/spec-extension/cookies.js"),s=r("./dist/esm/server/web/spec-extension/adapters/headers.js");function a(e,t,a,i){var l,d;let u;if(a&&(0,n.Iq)(e,a).isOnDemandRevalidate)return!1;if(n.Di in e)return e[n.Di];let c=s.h.from(e.headers),p=new o.qC(c),h=null==(l=p.get(n.dS))?void 0:l.value,f=null==(d=p.get(n.QM))?void 0:d.value;if(h&&!f&&h===a.previewModeId){let t={};return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}if(!h&&!f)return!1;if(!h||!f||h!==a.previewModeId)return i||(0,n.Lm)(t),!1;try{u=r("next/dist/compiled/jsonwebtoken").verify(f,a.previewModeSigningKey)}catch{return(0,n.Lm)(t),!1}let{decryptWithSecret:m}=r("./dist/esm/server/crypto-utils.js"),g=m(Buffer.from(a.previewModeEncryptionKey),u.data);try{let t=JSON.parse(g);return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}catch{return!1}}},"./dist/esm/server/crypto-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{decryptWithSecret:()=>i,encryptWithSecret:()=>a});let n=require("crypto");var o=r.n(n);let s="aes-256-gcm";function a(e,t){let r=o().randomBytes(16),n=o().randomBytes(64),a=o().pbkdf2Sync(e,n,1e5,32,"sha512"),i=o().createCipheriv(s,a,r),l=Buffer.concat([i.update(t,"utf8"),i.final()]),d=i.getAuthTag();return Buffer.concat([n,r,d,l]).toString("hex")}function i(e,t){let r=Buffer.from(t,"hex"),n=r.slice(0,64),a=r.slice(64,80),i=r.slice(80,96),l=r.slice(96),d=o().pbkdf2Sync(e,n,1e5,32,"sha512"),u=o().createDecipheriv(s,d,a);return u.setAuthTag(i),u.update(l)+u.final("utf8")}},"./dist/esm/server/lib/trace/constants.js":(e,t,r)=>{"use strict";var n,o,s,a,i,l,d,u,c,p,h,f;r.d(t,{Xy:()=>a,k0:()=>d,xj:()=>l}),function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(n||(n={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(o||(o={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(s||(s={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(a||(a={})),(i||(i={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(l||(l={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(d||(d={})),(u||(u={})).executeRoute="Router.executeRoute",(c||(c={})).runHandler="Node.runHandler",(p||(p={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(h||(h={})),(f||(f={})).execute="Middleware.execute"},"./dist/esm/server/optimize-amp.js":(e,t,r)=>{"use strict";async function n(e,t){let n;try{n=r("next/dist/compiled/@ampproject/toolbox-optimizer")}catch(t){return e}return n.create(t).transformHtml(e,t)}r.d(t,{Z:()=>n})},"./dist/esm/server/post-process.js":(e,t,r)=>{"use strict";r.d(t,{X:()=>u});var n,o,s,a=r("./dist/esm/shared/lib/constants.js");function i(e){return null!=e}let l=[];async function d(e,t,n){if(!l[0])return e;let{parse:o}=r("next/dist/compiled/node-html-parser"),s=o(e),a=e;async function i(e){let r=e.inspect(s,t);a=await e.mutate(a,r,t)}for(let e=0;e<l.length;e++){let t=l[e];(!t.condition||t.condition(n))&&await i(l[e].middleware)}return a}async function u(e,t,n,{inAmpMode:o,hybridAmp:s}){for(let a of[o?async t=>{let o=r("./dist/esm/server/optimize-amp.js").Z;return t=await o(t,n.ampOptimizerConfig),!n.ampSkipValidation&&n.ampValidator&&await n.ampValidator(t,e),t}:null,(0,n.optimizeFonts)?async e=>await d(e,{getFontDefinition:e=>{var t;return n.fontManifest&&(null==(t=n.fontManifest.find(t=>!!t&&t.url===e))?void 0:t.content)||""}},{optimizeFonts:n.optimizeFonts}):null,(0,n.optimizeCss)?async e=>{let t=new(r("critters"))({ssrMode:!0,reduceInlineStyles:!1,path:n.distDir,publicPath:`${n.assetPrefix}/_next/`,preload:"media",fonts:!1,...n.optimizeCss});return await t.process(e)}:null,o||s?e=>e.replace(/&amp;amp=1/g,"&amp=1"):null].filter(i))a&&(t=await a(t));return t}n="Inline-Fonts",o=new class{inspect(e,t){if(!t.getFontDefinition)return;let r=[];return e.querySelectorAll("link").filter(e=>"stylesheet"===e.getAttribute("rel")&&e.hasAttribute("data-href")&&a.C7.some(({url:t})=>{let r=e.getAttribute("data-href");return!!r&&r.startsWith(t)})).forEach(e=>{let t=e.getAttribute("data-href"),n=e.getAttribute("nonce");t&&r.push([t,n])}),r}constructor(){this.mutate=async(e,t,r)=>{let n=e,o=new Set;if(!r.getFontDefinition)return e;t.forEach(e=>{let[t,s]=e,i=`<link rel="stylesheet" href="${t}"/>`;if(n.indexOf(`<style data-href="${t}">`)>-1||n.indexOf(i)>-1)return;let l=r.getFontDefinition?r.getFontDefinition(t):null;if(l){let e=s?` nonce="${s}"`:"",r="";l.includes("ascent-override")&&(r=' data-size-adjust="true"'),n=n.replace("</head>",`<style data-href="${t}"${e}${r}>${l}</style></head>`);let i=t.replace(/&/g,"&amp;").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),d=RegExp(`<link[^>]*data-href="${i}"[^>]*/>`);n=n.replace(d,"");let u=a.C7.find(e=>t.startsWith(e.url));u&&o.add(u.preconnect)}else n=n.replace("</head>",`${i}</head>`)});let s="";return o.forEach(e=>{s+=`<link rel="preconnect" href="${e}" crossorigin />`}),n=n.replace('<meta name="next-font-preconnect"/>',s)}}},s=e=>e.optimizeFonts||process.env.__NEXT_OPTIMIZE_FONTS,l.push({name:n,middleware:o,condition:s||null})},"./dist/esm/server/web/spec-extension/adapters/headers.js":(e,t,r)=>{"use strict";r.d(t,{h:()=>s});var n=r("./dist/esm/server/web/spec-extension/adapters/reflect.js");class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.g.get(t,r,o);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return n.g.get(t,a,o)},set(t,r,o,s){if("symbol"==typeof r)return n.g.set(t,r,o,s);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return n.g.set(t,i??r,o,s)},has(t,r){if("symbol"==typeof r)return n.g.has(t,r);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==s&&n.g.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.g.deleteProperty(t,r);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===s||n.g.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},"./dist/esm/server/web/spec-extension/adapters/reflect.js":(e,t,r)=>{"use strict";r.d(t,{g:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},"./dist/esm/server/web/spec-extension/cookies.js":(e,t,r)=>{"use strict";r.d(t,{qC:()=>n.RequestCookies});var n=r("./dist/compiled/@edge-runtime/cookies/index.js")},"./dist/esm/shared/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{C7:()=>i,Er:()=>l,NO:()=>s,uY:()=>a,wU:()=>o}),r("./dist/esm/shared/lib/modern-browserslist-target.js");let n={client:"client",server:"server",edgeServer:"edge-server"};n.client,n.server,n.edgeServer;let o="__NEXT_BUILTIN_DOCUMENT__";Symbol("polyfills");let s="__N_SSG",a="__N_SSP",i=[{url:"https://fonts.googleapis.com/",preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],l=["/500"]},"./dist/esm/shared/lib/modern-browserslist-target.js":e=>{e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},"./lib/trace/tracer":e=>{"use strict";e.exports=require("next/dist/server/lib/trace/tracer")},critters:e=>{"use strict";e.exports=require("critters")},"next/dist/compiled/@ampproject/toolbox-optimizer":e=>{"use strict";e.exports=require("next/dist/compiled/@ampproject/toolbox-optimizer")},"next/dist/compiled/jsonwebtoken":e=>{"use strict";e.exports=require("next/dist/compiled/jsonwebtoken")},"next/dist/compiled/node-html-parser":e=>{"use strict";e.exports=require("next/dist/compiled/node-html-parser")},path:e=>{"use strict";e.exports=require("path")}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";let e,t,o;r.r(n),r.d(n,{PagesRouteModule:()=>e7,default:()=>te,renderToHTML:()=>e8,vendored:()=>e5});var s,a={};r.r(a),r.d(a,{AmpStateContext:()=>L});var i={};r.r(i),r.d(i,{HeadManagerContext:()=>O});var l={};r.r(l),r.d(l,{LoadableContext:()=>A});var d={};r.r(d),r.d(d,{default:()=>H});var u={};r.r(u),r.d(u,{RouterContext:()=>B});var c={};r.r(c),r.d(c,{HtmlContext:()=>Z,useHtmlContext:()=>Q});var p={};r.r(p),r.d(p,{ImageConfigContext:()=>eO});var h={};r.r(h),r.d(h,{PathParamsContext:()=>eq,PathnameContext:()=>eD,SearchParamsContext:()=>eM});var f={};r.r(f),r.d(f,{AppRouterContext:()=>eW,GlobalLayoutRouterContext:()=>eJ,LayoutRouterContext:()=>eG,MissingSlotContext:()=>eX,TemplateContext:()=>eV});var m={};r.r(m),r.d(m,{ServerInsertedHTMLContext:()=>e9,useServerInsertedHTML:()=>e6});var g={};r.r(g),r.d(g,{AmpContext:()=>a,AppRouterContext:()=>f,HeadManagerContext:()=>i,HooksClientContext:()=>h,HtmlContext:()=>c,ImageConfigContext:()=>p,Loadable:()=>d,LoadableContext:()=>l,RouterContext:()=>u,ServerInsertedHtml:()=>m});class v{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}let y=require("react/jsx-runtime");var x=r("./dist/esm/server/api-utils/index.js");let b=require("react");var w=r.n(b);let S=require("react-dom/server.browser");var P=r.n(S);let _=require("styled-jsx");var C=r("./dist/esm/lib/constants.js"),R=r("./dist/esm/shared/lib/constants.js");function j(e){return Object.prototype.toString.call(e)}function T(e){if("[object Object]"!==j(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}let E=/^[A-Za-z_$][A-Za-z0-9_$]*$/;class $ extends Error{constructor(e,t,r,n){super(r?`Error serializing \`${r}\` returned from \`${t}\` in "${e}".
Reason: ${n}`:`Error serializing props returned from \`${t}\` in "${e}".
Reason: ${n}`)}}function N(e,t,r){if(!T(r))throw new $(e,t,"",`Props must be returned as a plain object from ${t}: \`{ props: { ... } }\` (received: \`${j(r)}\`).`);function n(r,n,o){if(r.has(n))throw new $(e,t,o,`Circular references cannot be expressed in JSON (references: \`${r.get(n)||"(self)"}\`).`);r.set(n,o)}return function r(o,s,a){let i=typeof s;if(null===s||"boolean"===i||"number"===i||"string"===i)return!0;if("undefined"===i)throw new $(e,t,a,"`undefined` cannot be serialized as JSON. Please use `null` or omit this value.");if(T(s)){if(n(o,s,a),Object.entries(s).every(([e,t])=>{let n=E.test(e)?`${a}.${e}`:`${a}[${JSON.stringify(e)}]`,s=new Map(o);return r(s,e,n)&&r(s,t,n)}))return!0;throw new $(e,t,a,"invariant: Unknown error encountered in Object.")}if(Array.isArray(s)){if(n(o,s,a),s.every((e,t)=>r(new Map(o),e,`${a}[${t}]`)))return!0;throw new $(e,t,a,"invariant: Unknown error encountered in Array.")}throw new $(e,t,a,"`"+i+"`"+("object"===i?` ("${Object.prototype.toString.call(s)}")`:"")+" cannot be serialized as JSON. Please only return JSON serializable data types.")}(new Map,r,"")}let L=w().createContext({}),O=w().createContext({}),A=w().createContext(null),I=[],k=[];function M(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}class D{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function q(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function o(){if(!n){let t=new D(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}function s(e,t){!function(){o();let e=w().useContext(A);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let s=w().useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return w().useImperativeHandle(t,()=>({retry:n.retry}),[]),w().useMemo(()=>{var t;return s.loading||s.error?w().createElement(r.loading,{isLoading:s.loading,pastDelay:s.pastDelay,timedOut:s.timedOut,error:s.error,retry:n.retry}):s.loaded?w().createElement((t=s.loaded)&&t.default?t.default:t,e):null},[e,s])}return I.push(o),s.preload=()=>o(),s.displayName="LoadableComponent",w().forwardRef(s)}(M,e)}function F(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then(()=>{if(e.length)return F(e,t)})}q.preloadAll=()=>new Promise((e,t)=>{F(I).then(e,t)}),q.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let r=()=>t();F(k,e).then(r,r)}));let H=q,B=w().createContext(null);function z(e){return e.startsWith("/")?e:"/"+e}let U=["(..)(..)","(.)","(..)","(...)"],W=/\/\[[^/]+?\](?=\/|$)/;function G(e){return void 0!==e.split("/").find(e=>U.find(t=>e.startsWith(t)))&&(e=function(e){let t,r,n;for(let o of e.split("/"))if(r=U.find(e=>o.startsWith(e))){[t,n]=e.split(r,2);break}if(!t||!r||!n)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=z(t.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")),r){case"(.)":n="/"===t?`/${n}`:t+"/"+n;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);n=t.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);n=o.slice(0,-2).concat(n).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:n}}(e).interceptedRoute),W.test(e)}function J(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function V(e){return e.finished||e.headersSent}async function X(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await X(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&V(r))return n;if(!n)throw Error('"'+J(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class Y extends Error{}let Z=(0,b.createContext)(void 0);function Q(){let e=(0,b.useContext)(Z);if(!e)throw Error("<Html> should not be imported outside of pages/_document.\nRead more: https://nextjs.org/docs/messages/no-document-import-in-page");return e}let K=Symbol.for("NextInternalRequestMeta");function ee(e,t){let r=e[K]||{};return"string"==typeof t?r[t]:r}!function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(s||(s={}));let et=new Set([301,302,303,307,308]);function er(e){return e.statusCode||(e.permanent?s.PermanentRedirect:s.TemporaryRedirect)}var en=r("./lib/trace/tracer"),eo=r("./dist/esm/server/lib/trace/constants.js");class es{constructor(){let e,t;this.promise=new Promise((r,n)=>{e=r,t=n}),this.resolve=e,this.reject=t}}let ea=e=>{setImmediate(e)},ei={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])}};function el(e,t){if(0===t.length)return 0;if(0===e.length||t.length>e.length)return -1;for(let r=0;r<=e.length-t.length;r++){let n=!0;for(let o=0;o<t.length;o++)if(e[r+o]!==t[o]){n=!1;break}if(n)return r}return -1}function ed(){}let eu=new TextEncoder;function ec(...e){if(0===e.length)throw Error("Invariant: chainStreams requires at least one stream");if(1===e.length)return e[0];let{readable:t,writable:r}=new TransformStream,n=e[0].pipeTo(r,{preventClose:!0}),o=1;for(;o<e.length-1;o++){let t=e[o];n=n.then(()=>t.pipeTo(r,{preventClose:!0}))}let s=e[o];return(n=n.then(()=>s.pipeTo(r))).catch(ed),t}function ep(e){return new ReadableStream({start(t){t.enqueue(eu.encode(e)),t.close()}})}async function eh(e){let t=new TextDecoder("utf-8",{fatal:!0}),r="";for await(let n of e)r+=t.decode(n,{stream:!0});return r+t.decode()}async function ef(e,{suffix:t,inlinedDataStream:r,isStaticGeneration:n,getServerInsertedHTML:o,serverInsertedHTMLToHead:s,validateRootLayout:a}){let i,l,d,u,c;let p="</body></html>",h=t?t.split(p,1)[0]:null;return n&&"allReady"in e&&await e.allReady,function(e,t){let r=e;for(let e of t)e&&(r=r.pipeThrough(e));return r}(e,[function(){let e,t=[],r=0,n=n=>{if(e)return;let o=new es;e=o,ea(()=>{try{let e=new Uint8Array(r),o=0;for(let r=0;r<t.length;r++){let n=t[r];e.set(n,o),o+=n.byteLength}t.length=0,r=0,n.enqueue(e)}catch{}finally{e=void 0,o.resolve()}})};return new TransformStream({transform(e,o){t.push(e),r+=e.byteLength,n(o)},flush(){if(e)return e.promise}})}(),o&&!s?new TransformStream({transform:async(e,t)=>{let r=await o();r&&t.enqueue(eu.encode(r)),t.enqueue(e)}}):null,null!=h&&h.length>0?function(e){let t,r=!1,n=r=>{let n=new es;t=n,ea(()=>{try{r.enqueue(eu.encode(e))}catch{}finally{t=void 0,n.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),r||(r=!0,n(t))},flush(n){if(t)return t.promise;r||n.enqueue(eu.encode(e))}})}(h):null,r?function(e){let t=null,r=!1;async function n(n){if(t)return;let o=e.getReader();await new Promise(e=>ea(e));try{for(;;){let{done:e,value:t}=await o.read();if(e){r=!0;return}n.enqueue(t)}}catch(e){n.error(e)}}return new TransformStream({transform(e,r){r.enqueue(e),t||(t=n(r))},flush(e){if(!r)return t||n(e)}})}(r):null,a?(i=!1,l=!1,new TransformStream({async transform(e,t){!i&&el(e,ei.OPENING.HTML)>-1&&(i=!0),!l&&el(e,ei.OPENING.BODY)>-1&&(l=!0),t.enqueue(e)},flush(e){let t=[];i||t.push("html"),l||t.push("body"),t.length&&e.enqueue(eu.encode(`<script>self.__next_root_layout_missing_tags=${JSON.stringify(t)}</script>`))}})):null,function(e){let t=!1,r=eu.encode(e);return new TransformStream({transform(n,o){if(t)return o.enqueue(n);let s=el(n,r);if(s>-1){if(t=!0,n.length===e.length)return;let r=n.slice(0,s);if(o.enqueue(r),n.length>e.length+s){let t=n.slice(s+e.length);o.enqueue(t)}}else o.enqueue(n)},flush(e){e.enqueue(r)}})}(p),o&&s?(d=!1,u=!1,c=!1,new TransformStream({async transform(e,t){if(c=!0,u){t.enqueue(e);return}let r=await o();if(d){if(r){let e=eu.encode(r);t.enqueue(e)}t.enqueue(e),u=!0}else{let n=el(e,ei.CLOSED.HEAD);if(-1!==n){if(r){let o=eu.encode(r),s=new Uint8Array(e.length+o.length);s.set(e.slice(0,n)),s.set(o,n),s.set(e.slice(n),n+o.length),t.enqueue(s)}else t.enqueue(e);u=!0,d=!0}}d?ea(()=>{u=!1}):t.enqueue(e)},async flush(e){if(c){let t=await o();t&&e.enqueue(eu.encode(t))}}})):null])}function em(e){return e.replace(/\/$/,"")||"/"}function eg(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function ev(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=eg(e);return""+t+r+n+o}function ey(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=eg(e);return""+r+t+n+o}function ex(e,t){if("string"!=typeof e)return!1;let{pathname:r}=eg(e);return r===t||r.startsWith(t+"/")}function eb(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let ew=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function eS(e,t){return new URL(String(e).replace(ew,"localhost"),t&&String(t).replace(ew,"localhost"))}let eP=Symbol("NextURLInternal");class e_{constructor(e,t,r){let n,o;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,o=r||{}):o=r||t||{},this[eP]={url:eS(e,n??o.base),options:o,basePath:""},this.analyze()}analyze(){var e,t,r,n,o;let s=function(e,t){var r,n;let{basePath:o,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},i={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};o&&ex(i.pathname,o)&&(i.pathname=function(e,t){if(!ex(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(i.pathname,o),i.basePath=o);let l=i.pathname;if(i.pathname.startsWith("/_next/data/")&&i.pathname.endsWith(".json")){let e=i.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];i.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(i.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(i.pathname):eb(i.pathname,s.locales);i.locale=e.detectedLocale,i.pathname=null!=(n=e.pathname)?n:i.pathname,!e.detectedLocale&&i.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):eb(l,s.locales)).detectedLocale&&(i.locale=e.detectedLocale)}return i}(this[eP].url.pathname,{nextConfig:this[eP].options.nextConfig,parseData:!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,i18nProvider:this[eP].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[eP].url,this[eP].options.headers);this[eP].domainLocale=this[eP].options.i18nProvider?this[eP].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var n,o;if(t===(null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(o=s.locales)?void 0:o.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[eP].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,a);let i=(null==(r=this[eP].domainLocale)?void 0:r.defaultLocale)||(null==(o=this[eP].options.nextConfig)?void 0:null==(n=o.i18n)?void 0:n.defaultLocale);this[eP].url.pathname=s.pathname,this[eP].defaultLocale=i,this[eP].basePath=s.basePath??"",this[eP].buildId=s.buildId,this[eP].locale=s.locale??i,this[eP].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let o=e.toLowerCase();return!n&&(ex(o,"/api")||ex(o,"/"+t.toLowerCase()))?e:ev(e,"/"+t)}((e={basePath:this[eP].basePath,buildId:this[eP].buildId,defaultLocale:this[eP].options.forceLocale?void 0:this[eP].defaultLocale,locale:this[eP].locale,pathname:this[eP].url.pathname,trailingSlash:this[eP].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=em(t)),e.buildId&&(t=ey(ev(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=ev(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:ey(t,"/"):em(t)}formatSearch(){return this[eP].url.search}get buildId(){return this[eP].buildId}set buildId(e){this[eP].buildId=e}get locale(){return this[eP].locale??""}set locale(e){var t,r;if(!this[eP].locale||!(null==(r=this[eP].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[eP].locale=e}get defaultLocale(){return this[eP].defaultLocale}get domainLocale(){return this[eP].domainLocale}get searchParams(){return this[eP].url.searchParams}get host(){return this[eP].url.host}set host(e){this[eP].url.host=e}get hostname(){return this[eP].url.hostname}set hostname(e){this[eP].url.hostname=e}get port(){return this[eP].url.port}set port(e){this[eP].url.port=e}get protocol(){return this[eP].url.protocol}set protocol(e){this[eP].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[eP].url=eS(e),this.analyze()}get origin(){return this[eP].url.origin}get pathname(){return this[eP].url.pathname}set pathname(e){this[eP].url.pathname=e}get hash(){return this[eP].url.hash}set hash(e){this[eP].url.hash=e}get search(){return this[eP].url.search}set search(e){this[eP].url.search=e}get password(){return this[eP].url.password}set password(e){this[eP].url.password=e}get username(){return this[eP].url.username}set username(e){this[eP].url.username=e}get basePath(){return this[eP].basePath}set basePath(e){this[eP].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new e_(String(this),this[eP].options)}}r("./dist/esm/server/web/spec-extension/cookies.js"),Symbol("internal request"),Request,Symbol.for("edge-runtime.inspect.custom");let eC="ResponseAborted";class eR extends Error{constructor(...e){super(...e),this.name=eC}}let ej=0,eT=0,eE=0;function e$(e){return(null==e?void 0:e.name)==="AbortError"||(null==e?void 0:e.name)===eC}async function eN(e,t,r){try{let{errored:n,destroyed:o}=t;if(n||o)return;let s=function(e){let t=new AbortController;return e.once("close",()=>{e.writableFinished||t.abort(new eR)}),t}(t),a=function(e,t){let r=!1,n=new es;function o(){n.resolve()}e.on("drain",o),e.once("close",()=>{e.off("drain",o),n.resolve()});let s=new es;return e.once("finish",()=>{s.resolve()}),new WritableStream({write:async t=>{if(!r){if(r=!0,"performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){let e=function(e={}){let t=0===ej?void 0:{clientComponentLoadStart:ej,clientComponentLoadTimes:eT,clientComponentLoadCount:eE};return e.reset&&(ej=0,eT=0,eE=0),t}();e&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:e.clientComponentLoadStart,end:e.clientComponentLoadStart+e.clientComponentLoadTimes})}e.flushHeaders(),(0,en.getTracer)().trace(eo.Xy.startResponse,{spanName:"start response"},()=>void 0)}try{let r=e.write(t);"flush"in e&&"function"==typeof e.flush&&e.flush(),r||(await n.promise,n=new es)}catch(t){throw e.end(),Error("failed to write chunk to response",{cause:t})}},abort:t=>{e.writableFinished||e.destroy(t)},close:async()=>{if(t&&await t,!e.writableFinished)return e.end(),s.promise}})}(t,r);await e.pipeTo(a,{signal:s.signal})}catch(e){if(e$(e))return;throw Error("failed to pipe response",{cause:e})}}class eL{static fromStatic(e){return new eL(e,{metadata:{}})}constructor(e,{contentType:t,waitUntil:r,metadata:n}){this.response=e,this.contentType=t,this.metadata=n,this.waitUntil=r}assignMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedString(e=!1){if(null===this.response)throw Error("Invariant: null responses cannot be unchunked");if("string"!=typeof this.response){if(!e)throw Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js");return eh(this.readable)}return this.response}get readable(){if(null===this.response)throw Error("Invariant: null responses cannot be streamed");if("string"==typeof this.response)throw Error("Invariant: static responses cannot be streamed");return Array.isArray(this.response)?ec(...this.response):this.response}chain(e){let t;if(null===this.response)throw Error("Invariant: response is null. This is a bug in Next.js");(t="string"==typeof this.response?[ep(this.response)]:Array.isArray(this.response)?this.response:[this.response]).push(e),this.response=t}async pipeTo(e){try{await this.readable.pipeTo(e,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await e.close()}catch(t){if(e$(t)){await e.abort(t);return}throw t}}async pipeToNodeResponse(e){await eN(this.readable,e,this.waitUntil)}}let eO=w().createContext({deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1});var eA=r("./dist/compiled/strip-ansi/index.js"),eI=r.n(eA);let ek=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],eM=(0,b.createContext)(null),eD=(0,b.createContext)(null),eq=(0,b.createContext)(null),eF=/[|\\{}()[\]^$+*?.-]/,eH=/[|\\{}()[\]^$+*?.-]/g;function eB(e){return eF.test(e)?e.replace(eH,"\\$&"):e}function ez(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function eU(e){let{children:t,router:r,...n}=e,o=(0,b.useRef)(n.isAutoExport),s=(0,b.useMemo)(()=>{let e;let t=o.current;if(t&&(o.current=!1),G(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return(0,y.jsx)(eD.Provider,{value:s,children:t})}let eW=w().createContext(null),eG=w().createContext(null),eJ=w().createContext(null),eV=w().createContext(null),eX=w().createContext(new Set),eY=Symbol.for("NextjsError"),eZ="<!DOCTYPE html>";function eQ(){throw Error('No router instance found. you should only use "next/router" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance')}async function eK(e){let t=await P().renderToReadableStream(e);return await t.allReady,eh(t)}e=r("./dist/esm/server/api-utils/node/try-get-preview-data.js").R,t=r("./dist/esm/build/output/log.js").ZK,o=r("./dist/esm/server/post-process.js").X;class e0{constructor(e,t,r,{isFallback:n},o,s,a,i,l,d,u,c){this.route=e.replace(/\/$/,"")||"/",this.pathname=e,this.query=t,this.asPath=r,this.isFallback=n,this.basePath=s,this.locale=a,this.locales=i,this.defaultLocale=l,this.isReady=o,this.domainLocales=d,this.isPreview=!!u,this.isLocaleDomain=!!c}push(){eQ()}replace(){eQ()}reload(){eQ()}back(){eQ()}forward(){eQ()}prefetch(){eQ()}beforePopState(){eQ()}}function e1(e,t,r){return(0,y.jsx)(e,{Component:t,...r})}let e4=(e,t)=>{let r=`invalid-${e.toLocaleLowerCase()}-value`;return`Additional keys were returned from \`${e}\`. Properties intended for your component must be nested under the \`props\` key, e.g.:

	return { props: { title: 'My Title', content: '...' } }

Keys that need to be moved: ${t.join(", ")}.
Read more: https://nextjs.org/docs/messages/${r}`};function e2(e,t,r){let{destination:n,permanent:o,statusCode:s,basePath:a}=e,i=[],l=void 0!==s,d=void 0!==o;d&&l?i.push("`permanent` and `statusCode` can not both be provided"):d&&"boolean"!=typeof o?i.push("`permanent` must be `true` or `false`"):l&&!et.has(s)&&i.push(`\`statusCode\` must undefined or one of ${[...et].join(", ")}`);let u=typeof n;"string"!==u&&i.push(`\`destination\` should be string but received ${u}`);let c=typeof a;if("undefined"!==c&&"boolean"!==c&&i.push(`\`basePath\` should be undefined or a false, received ${c}`),i.length>0)throw Error(`Invalid redirect object returned from ${r} for ${t.url}
`+i.join(" and ")+"\nSee more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp")}async function e3(n,s,a,i,l,d){var u,c,p;let h,f,m,g;(0,x.gk)({req:n},"cookies",(c=n.headers,function(){let{cookie:e}=c;if(!e)return{};let{parse:t}=r("./dist/compiled/cookie/index.js");return t(Array.isArray(e)?e.join("; "):e)}));let v={};if(v.assetQueryString=l.dev&&l.assetQueryString||"",l.dev&&!v.assetQueryString){let e=(n.headers["user-agent"]||"").toLowerCase();e.includes("safari")&&!e.includes("chrome")&&(v.assetQueryString=`?ts=${Date.now()}`)}l.deploymentId&&(v.assetQueryString+=`${v.assetQueryString?"&":"?"}dpl=${l.deploymentId}`),i=Object.assign({},i);let{err:b,dev:w=!1,ampPath:S="",pageConfig:j={},buildManifest:T,reactLoadableManifest:E,ErrorDebug:$,getStaticProps:I,getStaticPaths:k,getServerSideProps:M,isNextDataRequest:D,params:q,previewProps:F,basePath:W,images:Q,runtime:K,isExperimentalCompile:et,swrDelta:es}=l,{App:ea}=d,ei=v.assetQueryString,el=d.Document,ed=l.Component,eu=!!i.__nextFallback,eg=i.__nextNotFoundSrcPage;!function(e){for(let t of ek)delete e[t]}(i);let ev=!!I,ey=ev&&l.nextExport,ex=ea.getInitialProps===ea.origGetInitialProps,eb=!!(null==ed?void 0:ed.getInitialProps),ew=null==ed?void 0:ed.unstable_scriptLoader,eS=G(a),eP="/_error"===a&&ed.getInitialProps===ed.origGetInitialProps;l.nextExport&&eb&&!eP&&t(`Detected getInitialProps on page '${a}' while running export. It's recommended to use getStaticProps which has a more correct behavior for static exporting.
Read more: https://nextjs.org/docs/messages/get-initial-props-export`);let e_=!eb&&ex&&!ev&&!M;if(e_&&!w&&et&&(s.setHeader("Cache-Control",function({revalidate:e,swrDelta:t}){let r=t?`stale-while-revalidate=${t}`:"stale-while-revalidate";return 0===e?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof e?`s-maxage=${e}, ${r}`:`s-maxage=${C.BR}, ${r}`}({revalidate:!1,swrDelta:es})),e_=!1),eb&&ev)throw Error(C.wh+` ${a}`);if(eb&&M)throw Error(C.Wo+` ${a}`);if(M&&ev)throw Error(C.oL+` ${a}`);if(M&&"export"===l.nextConfigOutput)throw Error('getServerSideProps cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if(k&&!eS)throw Error(`getStaticPaths is only allowed for dynamic SSG pages and was found on '${a}'.
Read more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`);if(k&&!ev)throw Error(`getStaticPaths was added without a getStaticProps in ${a}. Without getStaticProps, getStaticPaths does nothing`);if(ev&&eS&&!k)throw Error(`getStaticPaths is required for dynamic SSG pages and is missing for '${a}'.
Read more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`);let eC=l.resolvedAsPath||n.url;if(w){let{isValidElementType:e}=r("./dist/compiled/react-is/index.js");if(!e(ed))throw Error(`The default export is not a React Component in page: "${a}"`);if(!e(ea))throw Error('The default export is not a React Component in page: "/_app"');if(!e(el))throw Error('The default export is not a React Component in page: "/_document"');if((e_||eu)&&(i={...i.amp?{amp:i.amp}:{}},eC=`${a}${n.url.endsWith("/")&&"/"!==a&&!eS?"/":""}`,n.url=a),"/404"===a&&(eb||M))throw Error(`\`pages/404\` ${C.Ei}`);if(R.Er.includes(a)&&(eb||M))throw Error(`\`pages${a}\` ${C.Ei}`)}for(let e of["getStaticProps","getServerSideProps","getStaticPaths"])if(null==ed?void 0:ed[e])throw Error(`page ${a} ${e} ${C.lk}`);await H.preloadAll(),(ev||M)&&!eu&&F&&(m=!1!==(h=e(n,s,F,!!l.multiZoneDraftMode)));let eR=new e0(a,i,eC,{isFallback:eu},!!(M||eb||!ex&&!ev||et),W,l.locale,l.locales,l.defaultLocale,l.domainLocales,m,ee(n,"isLocaleDomain")),ej={back(){eR.back()},forward(){eR.forward()},refresh(){eR.reload()},fastRefresh(){},push(e,t){let{scroll:r}=void 0===t?{}:t;eR.push(e,void 0,{scroll:r})},replace(e,t){let{scroll:r}=void 0===t?{}:t;eR.replace(e,void 0,{scroll:r})},prefetch(e){eR.prefetch(e)}},eT={},eE=(0,_.createStyleRegistry)(),e$={ampFirst:!0===j.amp,hasQuery:!!i.amp,hybrid:"hybrid"===j.amp},eN=function(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}(e$),eA=function(e){void 0===e&&(e=!1);let t=[(0,y.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,y.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}(eN),eD=[],eF={};ew&&(eF.beforeInteractive=[].concat(ew()).filter(e=>"beforeInteractive"===e.props.strategy).map(e=>e.props));let eH=({children:e})=>(0,y.jsx)(eW.Provider,{value:ej,children:(0,y.jsx)(eM.Provider,{value:eR.isReady&&eR.query?new URL(eR.asPath,"http://n").searchParams:new URLSearchParams,children:(0,y.jsx)(eU,{router:eR,isAutoExport:e_,children:(0,y.jsx)(eq.Provider,{value:function(e){if(!e.isReady||!e.query)return null;let t={};for(let r of Object.keys(function(e){let{parameterizedRoute:t,groups:r}=function(e){let t=em(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=U.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:s,repeat:a}=ez(o[1]);return r[e]={pos:n++,repeat:a,optional:s},"/"+eB(t)+"([^/]+?)"}if(!o)return"/"+eB(e);{let{key:e,repeat:t,optional:s}=ez(o[1]);return r[e]={pos:n++,repeat:t,optional:s},t?s?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}(e.pathname).groups))t[r]=e.query[r];return t}(eR),children:(0,y.jsx)(B.Provider,{value:eR,children:(0,y.jsx)(L.Provider,{value:e$,children:(0,y.jsx)(O.Provider,{value:{updateHead:e=>{eA=e},updateScripts:e=>{eT=e},scripts:eF,mountedInstances:new Set},children:(0,y.jsx)(A.Provider,{value:e=>eD.push(e),children:(0,y.jsx)(_.StyleRegistry,{registry:eE,children:(0,y.jsx)(eO.Provider,{value:Q,children:e})})})})})})})})})}),eG=()=>null,eJ=({children:e})=>(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(eG,{}),(0,y.jsx)(eH,{children:(0,y.jsxs)(y.Fragment,{children:[w?(0,y.jsxs)(y.Fragment,{children:[e,(0,y.jsx)(eG,{})]}):e,(0,y.jsx)(eG,{})]})})]}),eV={err:b,req:e_?void 0:n,res:e_?void 0:s,pathname:a,query:i,asPath:eC,locale:l.locale,locales:l.locales,defaultLocale:l.defaultLocale,AppTree:e=>(0,y.jsx)(eJ,{children:e1(ea,ed,{...e,router:eR})}),defaultGetInitialProps:async(e,t={})=>{let{html:r,head:n}=await e.renderPage({enhanceApp:e=>t=>(0,y.jsx)(e,{...t})}),o=eE.styles({nonce:t.nonce});return eE.flush(),{html:r,head:n,styles:o}}},eX=!ev&&(l.nextExport||w&&(e_||eu)),eQ=()=>{let e=eE.styles();return eE.flush(),(0,y.jsx)(y.Fragment,{children:e})};if(f=await X(ea,{AppTree:eV.AppTree,Component:ed,router:eR,ctx:eV}),(ev||M)&&m&&(f.__N_PREVIEW=!0),ev&&(f[R.NO]=!0),ev&&!eu){let e,t;try{e=await (0,en.getTracer)().trace(eo.xj.getStaticProps,{spanName:`getStaticProps ${a}`,attributes:{"next.route":a}},()=>I({...eS?{params:i}:void 0,...m?{draftMode:!0,preview:!0,previewData:h}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale,revalidateReason:l.isOnDemandRevalidate?"on-demand":ey?"build":"stale"}))}catch(e){throw e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(C.q6);let r=Object.keys(e).filter(e=>"revalidate"!==e&&"props"!==e&&"redirect"!==e&&"notFound"!==e);if(r.includes("unstable_revalidate"))throw Error(C.Eo);if(r.length)throw Error(e4("getStaticProps",r));if("notFound"in e&&e.notFound){if("/404"===a)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');v.isNotFound=!0}if("redirect"in e&&e.redirect&&"object"==typeof e.redirect){if(e2(e.redirect,n,"getStaticProps"),ey)throw Error(`\`redirect\` can not be returned from getStaticProps during prerendering (${n.url})
See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`);e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:er(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0}if((w||ey)&&!v.isNotFound&&!N(a,"getStaticProps",e.props))throw Error("invariant: getStaticProps did not return valid props. Please report this.");if("revalidate"in e){if(e.revalidate&&"export"===l.nextConfigOutput)throw Error('ISR cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if("number"==typeof e.revalidate){if(Number.isInteger(e.revalidate)){if(e.revalidate<=0)throw Error(`A page's revalidate option can not be less than or equal to zero for ${n.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.

To never revalidate, you can set revalidate to \`false\` (only ran once at build-time).
To revalidate as soon as possible, you can set the value to \`1\`.`);e.revalidate>31536e3&&console.warn(`Warning: A page's revalidate option was set to more than a year for ${n.url}. This may have been done in error.
To only run getStaticProps at build-time and not revalidate at runtime, you can set \`revalidate\` to \`false\`!`),t=e.revalidate}else throw Error(`A page's revalidate option must be seconds expressed as a natural number for ${n.url}. Mixed numbers, such as '${e.revalidate}', cannot be used.
Try changing the value to '${Math.ceil(e.revalidate)}' or using \`Math.ceil()\` if you're computing the value.`)}else if(!0===e.revalidate)t=1;else if(!1===e.revalidate||void 0===e.revalidate)t=!1;else throw Error(`A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(e.revalidate)}' for ${n.url}`)}else t=!1;if(f.pageProps=Object.assign({},f.pageProps,"props"in e?e.props:void 0),v.revalidate=t,v.pageData=f,v.isNotFound)return new eL(null,{metadata:v})}if(M&&(f[R.uY]=!0),M&&!eu){let e;let t=!1;try{e=await (0,en.getTracer)().trace(eo.xj.getServerSideProps,{spanName:`getServerSideProps ${a}`,attributes:{"next.route":a}},async()=>M({req:n,res:s,query:i,resolvedUrl:l.resolvedUrl,...eS?{params:q}:void 0,...!1!==h?{draftMode:!0,preview:!0,previewData:h}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale})),v.revalidate=0}catch(e){throw"object"==typeof e&&null!==e&&"name"in e&&"message"in e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(C.Lx);e.props instanceof Promise&&(t=!0);let r=Object.keys(e).filter(e=>"props"!==e&&"redirect"!==e&&"notFound"!==e);if(e.unstable_notFound)throw Error(`unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${a}`);if(e.unstable_redirect)throw Error(`unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${a}`);if(r.length)throw Error(e4("getServerSideProps",r));if("notFound"in e&&e.notFound){if("/404"===a)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');return v.isNotFound=!0,new eL(null,{metadata:v})}if("redirect"in e&&"object"==typeof e.redirect&&(e2(e.redirect,n,"getServerSideProps"),e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:er(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0),t&&(e.props=await e.props),(w||ey)&&!N(a,"getServerSideProps",e.props))throw Error("invariant: getServerSideProps did not return valid props. Please report this.");f.pageProps=Object.assign({},f.pageProps,e.props),v.pageData=f}if(D&&!ev||v.isRedirect)return new eL(JSON.stringify(f),{metadata:v});if(eu&&(f.pageProps={}),V(s)&&!ev)return new eL(null,{metadata:v});let e3=T;if(e_&&eS){let e;let t=(e=(function(e){let t=/^\/index(\/|$)/.test(e)&&!G(e)?"/index"+e:"/"===e?"/index":z(e);{let{posix:e}=r("path"),n=e.normalize(t);if(n!==t)throw new Y("Requested and resolved page mismatch: "+t+" "+n)}return t})(a).replace(/\\/g,"/")).startsWith("/index/")&&!G(e)?e.slice(6):"/index"!==e?e:"/";t in e3.pages&&(e3={...e3,pages:{...e3.pages,[t]:[...e3.pages[t],...e3.lowPriorityFiles.filter(e=>e.includes("_buildManifest"))]},lowPriorityFiles:e3.lowPriorityFiles.filter(e=>!e.includes("_buildManifest"))})}let e8=({children:e})=>eN?e:(0,y.jsx)("div",{id:"__next",children:e}),e9=async()=>{let e,t,r;async function n(e){let t=async(t={})=>{if(eV.err&&$)return e&&e(ea,ed),{html:await eK((0,y.jsx)(e8,{children:(0,y.jsx)($,{error:eV.err})})),head:eA};if(w&&(f.router||f.Component))throw Error("'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props");let{App:r,Component:n}="function"==typeof t?{App:ea,Component:t(ed)}:{App:t.enhanceApp?t.enhanceApp(ea):ea,Component:t.enhanceComponent?t.enhanceComponent(ed):ed};return e?e(r,n).then(async e=>(await e.allReady,{html:await eh(e),head:eA})):{html:await eK((0,y.jsx)(e8,{children:(0,y.jsx)(eJ,{children:e1(r,n,{...f,router:eR})})})),head:eA}},r={...eV,renderPage:t},n=await X(el,r);if(V(s)&&!ev)return null;if(!n||"string"!=typeof n.html)throw Error(`"${J(el)}.getInitialProps()" should resolve to an object with a "html" prop set with a valid html string`);return{docProps:n,documentCtx:r}}el[R.wU];let o=(e,t)=>{let r=e||ea,n=t||ed;return eV.err&&$?(0,y.jsx)(e8,{children:(0,y.jsx)($,{error:eV.err})}):(0,y.jsx)(e8,{children:(0,y.jsx)(eJ,{children:e1(r,n,{...f,router:eR})})})},a=async(e,t)=>{let r=o(e,t);return await function({ReactDOMServer:e,element:t,streamOptions:r}){return(0,en.getTracer)().trace(eo.k0.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}({ReactDOMServer:P(),element:r})},i=(0,en.getTracer)().wrap(eo.xj.createBodyResult,(e,t)=>ef(e,{suffix:t,inlinedDataStream:void 0,isStaticGeneration:!0,getServerInsertedHTML:()=>eK(eQ()),serverInsertedHTMLToHead:!1,validateRootLayout:void 0})),l=!!el.getInitialProps;if(l){if(null===(t=await n(a)))return null;let{docProps:r}=t;e=e=>i(ep(r.html+e))}else{let r=await a(ea,ed);e=e=>i(r,e),t={}}let{docProps:d}=t||{};return l?(r=d.styles,eA=d.head):(r=eE.styles(),eE.flush()),{bodyResult:e,documentElement:e=>(0,y.jsx)(el,{...e,...d}),head:eA,headTags:[],styles:r}};null==(u=(0,en.getTracer)().getRootSpanAttributes())||u.set("next.route",l.page);let e6=await (0,en.getTracer)().trace(eo.xj.renderDocument,{spanName:`render route (pages) ${l.page}`,attributes:{"next.route":l.page}},async()=>e9());if(!e6)return new eL(null,{metadata:v});let e7=new Set,e5=new Set;for(let e of eD){let t=E[e];t&&(e7.add(t.id),t.files.forEach(e=>{e5.add(e)}))}let te=e$.hybrid,{assetPrefix:tt,buildId:tr,customServer:tn,defaultLocale:to,disableOptimizedLoading:ts,domainLocales:ta,locale:ti,locales:tl,runtimeConfig:td}=l,tu={__NEXT_DATA__:{props:f,page:a,query:i,buildId:tr,assetPrefix:""===tt?void 0:tt,runtimeConfig:td,nextExport:!0===eX||void 0,autoExport:!0===e_||void 0,isFallback:eu,isExperimentalCompile:et,dynamicIds:0===e7.size?void 0:Array.from(e7),err:l.err?(p=l.err,w?(g="server",g=p[eY]||"server",{name:p.name,source:g,message:eI()(p.message),stack:p.stack,digest:p.digest}):{name:"Internal Server Error.",message:"500 - Internal Server Error.",statusCode:500}):void 0,gsp:!!I||void 0,gssp:!!M||void 0,customServer:tn,gip:!!eb||void 0,appGip:!ex||void 0,locale:ti,locales:tl,defaultLocale:to,domainLocales:ta,isPreview:!0===m||void 0,notFoundSrcPage:eg&&w?eg:void 0},strictNextHead:l.strictNextHead,buildManifest:e3,docComponentsRendered:{},dangerousAsPath:eR.asPath,canonicalBase:!l.ampPath&&ee(n,"didStripLocale")?`${l.canonicalBase||""}/${l.locale}`:l.canonicalBase,ampPath:S,inAmpMode:eN,isDevelopment:!!w,hybridAmp:te,dynamicImports:Array.from(e5),assetPrefix:tt,unstable_runtimeJS:j.unstable_runtimeJS,unstable_JsPreload:j.unstable_JsPreload,assetQueryString:ei,scriptLoader:eT,locale:ti,disableOptimizedLoading:ts,head:e6.head,headTags:e6.headTags,styles:e6.styles,crossOrigin:l.crossOrigin,optimizeCss:l.optimizeCss,optimizeFonts:l.optimizeFonts,nextConfigOutput:l.nextConfigOutput,nextScriptWorkers:l.nextScriptWorkers,runtime:K,largePageDataBytes:l.largePageDataBytes,nextFontManifest:l.nextFontManifest},tc=(0,y.jsx)(L.Provider,{value:e$,children:(0,y.jsx)(Z.Provider,{value:tu,children:e6.documentElement(tu)})}),tp=await (0,en.getTracer)().trace(eo.xj.renderToString,async()=>eK(tc)),[th,tf]=tp.split("<next-js-internal-body-render-target></next-js-internal-body-render-target>",2),tm="";tp.startsWith(eZ)||(tm+=eZ),tm+=th,eN&&(tm+="<!-- __NEXT_DATA__ -->");let tg=await eh(ec(ep(tm),await e6.bodyResult(tf)));return new eL(await o(a,tg,l,{inAmpMode:eN,hybridAmp:te}),{metadata:v})}let e8=(e,t,r,n,o)=>e3(e,t,r,n,o,o),e9=w().createContext(null);function e6(e){let t=(0,b.useContext)(e9);t&&t(e)}class e7 extends v{constructor(e){super(e),this.components=e.components}render(e,t,r){return e3(e,t,r.page,r.query,r.renderOpts,{App:this.components.App,Document:this.components.Document})}}let e5={contexts:g},te=e7})(),module.exports=n})();
//# sourceMappingURL=pages.runtime.prod.js.map