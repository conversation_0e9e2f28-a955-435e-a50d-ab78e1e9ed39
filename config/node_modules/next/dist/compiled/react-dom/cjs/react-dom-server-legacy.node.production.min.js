/*
 React
 react-dom-server-legacy.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var da=require("next/dist/compiled/react"),ea=require("react-dom"),ka=require("stream"),la=Symbol.for("react.element"),ma=Symbol.for("react.portal"),wa=Symbol.for("react.fragment"),xa=Symbol.for("react.strict_mode"),ya=Symbol.for("react.profiler"),za=Symbol.for("react.provider"),Aa=Symbol.for("react.consumer"),Ha=Symbol.for("react.context"),Ia=Symbol.for("react.forward_ref"),Ja=Symbol.for("react.suspense"),Ka=Symbol.for("react.suspense_list"),La=Symbol.for("react.memo"),Ya=Symbol.for("react.lazy"),eb=Symbol.for("react.scope"),
fb=Symbol.for("react.debug_trace_mode"),gb=Symbol.for("react.offscreen"),hb=Symbol.for("react.legacy_hidden"),ib=Symbol.for("react.cache"),jb=Symbol.iterator,kb=Array.isArray;
function lb(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=***********(f&65535)+((***********(f>>>16)&65535)<<16)&**********;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&**********;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&**********;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=***********(f&65535)+((***********(f>>>16)&65535)<<16)&**********,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&**********}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&**********;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&**********;return(e^e>>>16)>>>0}
var t=Object.assign,A=Object.prototype.hasOwnProperty,mb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),nb={},ob={};
function pb(a){if(A.call(ob,a))return!0;if(A.call(nb,a))return!1;if(mb.test(a))return ob[a]=!0;nb[a]=!0;return!1}
var qb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),rb=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),sb=/["'&<>]/;
function B(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=sb.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Gb=/([A-Z])/g,Hb=/^ms-/,Ib=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Jb={pending:!1,data:null,method:null,action:null},Kb=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,Sb={prefetchDNS:Lb,preconnect:Mb,preload:Nb,preloadModule:Ob,preinitStyle:Pb,preinitScript:Qb,preinitModuleScript:Rb},C=[],Tb=/(<\/|<)(s)(cript)/gi;function Ub(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function Vb(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function I(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function Wb(a){return I("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function Xb(a,b,c){switch(b){case "noscript":return I(2,null,a.tagScope|1);case "select":return I(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return I(3,null,a.tagScope);case "picture":return I(2,null,a.tagScope|2);case "math":return I(4,null,a.tagScope);case "foreignObject":return I(2,null,a.tagScope);case "table":return I(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return I(6,null,a.tagScope);case "colgroup":return I(8,null,a.tagScope);case "tr":return I(7,null,a.tagScope)}return 5<=
a.insertionMode?I(2,null,a.tagScope):0===a.insertionMode?"html"===b?I(1,null,a.tagScope):I(2,null,a.tagScope):1===a.insertionMode?I(2,null,a.tagScope):a}var Yb=new Map;
function Zb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(A.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=B(d);e=B((""+e).trim())}else f=Yb.get(d),void 0===f&&(f=B(d.replace(Gb,"-$1").toLowerCase().replace(Hb,"-ms-")),Yb.set(d,f)),e="number"===typeof e?0===e||qb.has(d)?""+e:e+"px":
B((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function $b(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function J(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',B(c),'"')}function ac(a){var b=a.nextFormID++;return a.idPrefix+b}var mc=B("javascript:throw new Error('React form unexpectedly submitted.')");
function nc(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");J(this,"name",b);J(this,"value",a);this.push("/>")}
function oc(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=ac(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',mc,'"'),g=f=e=d=h=null,pc(b,c)));null!=h&&K(a,"name",h);null!=d&&K(a,"formAction",d);null!=e&&K(a,"formEncType",e);null!=f&&K(a,"formMethod",f);null!=g&&K(a,"formTarget",g);return k}
function K(a,b,c){switch(b){case "className":J(a,"class",c);break;case "tabIndex":J(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":J(a,b,c);break;case "style":Zb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',B(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;
case "autoFocus":case "multiple":case "muted":$b(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',B(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',B(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',B(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',B(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',B(c),'"');break;case "xlinkActuate":J(a,"xlink:actuate",
c);break;case "xlinkArcrole":J(a,"xlink:arcrole",c);break;case "xlinkRole":J(a,"xlink:role",c);break;case "xlinkShow":J(a,"xlink:show",c);break;case "xlinkTitle":J(a,"xlink:title",c);break;case "xlinkType":J(a,"xlink:type",c);break;case "xmlBase":J(a,"xml:base",c);break;case "xmlLang":J(a,"xml:lang",c);break;case "xmlSpace":J(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=rb.get(b)||b,pb(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',B(c),'"')}}}function O(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function qc(a){var b="";da.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function pc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'React form unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.ownerDocument||c,(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,d,b))}});',"\x3c/script>"))}
function P(a,b){a.push(Q("link"));for(var c in b)if(A.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:K(a,c,d)}}a.push("/>");return null}
function rc(a,b,c){a.push(Q(c));for(var d in b)if(A.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:K(a,d,e)}}a.push("/>");return null}
function sc(a,b){a.push(Q("title"));var c=null,d=null,e;for(e in b)if(A.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(B(""+b));O(a,d,c);a.push(tc("title"));return null}
function uc(a,b){a.push(Q("script"));var c=null,d=null,e;for(e in b)if(A.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");O(a,d,c);"string"===typeof c&&a.push(B(c));a.push(tc("script"));return null}
function vc(a,b,c){a.push(Q(c));var d=c=null,e;for(e in b)if(A.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");O(a,d,c);return"string"===typeof c?(a.push(B(c)),null):c}var wc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,xc=new Map;function Q(a){var b=xc.get(a);if(void 0===b){if(!wc.test(a))throw Error("Invalid tag: "+a);b="<"+a;xc.set(a,b)}return b}
function yc(a,b,c,d,e,f,g,h,k){switch(b){case "div":case "span":case "svg":case "path":break;case "a":break;case "g":case "p":case "li":break;case "select":a.push(Q("select"));var m=null,n=null,l;for(l in c)if(A.call(c,l)){var u=c[l];if(null!=u)switch(l){case "children":m=u;break;case "dangerouslySetInnerHTML":n=u;break;case "defaultValue":case "value":break;default:K(a,l,u)}}a.push(">");O(a,n,m);return m;case "option":var q=g.selectedValue;a.push(Q("option"));var z=null,v=null,y=null,x=null,p;for(p in c)if(A.call(c,
p)){var D=c[p];if(null!=D)switch(p){case "children":z=D;break;case "selected":y=D;break;case "dangerouslySetInnerHTML":x=D;break;case "value":v=D;default:K(a,p,D)}}if(null!=q){var E=null!==v?""+v:qc(z);if(kb(q))for(var r=0;r<q.length;r++){if(""+q[r]===E){a.push(' selected=""');break}}else""+q===E&&a.push(' selected=""')}else y&&a.push(' selected=""');a.push(">");O(a,x,z);return z;case "textarea":a.push(Q("textarea"));var w=null,F=null,U=null,L;for(L in c)if(A.call(c,L)){var G=c[L];if(null!=G)switch(L){case "children":U=
G;break;case "value":w=G;break;case "defaultValue":F=G;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:K(a,L,G)}}null===w&&null!==F&&(w=F);a.push(">");if(null!=U){if(null!=w)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(kb(U)){if(1<U.length)throw Error("<textarea> can only have at most one child.");w=""+U[0]}w=""+U}"string"===typeof w&&"\n"===w[0]&&a.push("\n");null!==w&&a.push(B(""+w));
return null;case "input":a.push(Q("input"));var na=null,fa=null,Z=null,tb=null,ub=null,Ma=null,Na=null,Oa=null,Pa=null,oa;for(oa in c)if(A.call(c,oa)){var M=c[oa];if(null!=M)switch(oa){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":na=M;break;case "formAction":fa=M;break;case "formEncType":Z=M;break;case "formMethod":tb=M;break;case "formTarget":ub=M;break;case "defaultChecked":Pa=
M;break;case "defaultValue":Na=M;break;case "checked":Oa=M;break;case "value":Ma=M;break;default:K(a,oa,M)}}var aa=oc(a,d,e,fa,Z,tb,ub,na);null!==Oa?$b(a,"checked",Oa):null!==Pa&&$b(a,"checked",Pa);null!==Ma?K(a,"value",Ma):null!==Na&&K(a,"value",Na);a.push("/>");null!==aa&&aa.forEach(nc,a);return null;case "button":a.push(Q("button"));var ba=null,pa=null,Qa=null,qa=null,id=null,jd=null,kd=null,Ra;for(Ra in c)if(A.call(c,Ra)){var ca=c[Ra];if(null!=ca)switch(Ra){case "children":ba=ca;break;case "dangerouslySetInnerHTML":pa=
ca;break;case "name":Qa=ca;break;case "formAction":qa=ca;break;case "formEncType":id=ca;break;case "formMethod":jd=ca;break;case "formTarget":kd=ca;break;default:K(a,Ra,ca)}}var ld=oc(a,d,e,qa,id,jd,kd,Qa);a.push(">");null!==ld&&ld.forEach(nc,a);O(a,pa,ba);if("string"===typeof ba){a.push(B(ba));var md=null}else md=ba;return md;case "form":a.push(Q("form"));var Sa=null,nd=null,ha=null,Ta=null,Ua=null,Va=null,Wa;for(Wa in c)if(A.call(c,Wa)){var ia=c[Wa];if(null!=ia)switch(Wa){case "children":Sa=ia;
break;case "dangerouslySetInnerHTML":nd=ia;break;case "action":ha=ia;break;case "encType":Ta=ia;break;case "method":Ua=ia;break;case "target":Va=ia;break;default:K(a,Wa,ia)}}var bc=null,cc=null;if("function"===typeof ha)if("function"===typeof ha.$$FORM_ACTION){var Ye=ac(d),Ba=ha.$$FORM_ACTION(Ye);ha=Ba.action||"";Ta=Ba.encType;Ua=Ba.method;Va=Ba.target;bc=Ba.data;cc=Ba.name}else a.push(" ","action",'="',mc,'"'),Va=Ua=Ta=ha=null,pc(d,e);null!=ha&&K(a,"action",ha);null!=Ta&&K(a,"encType",Ta);null!=
Ua&&K(a,"method",Ua);null!=Va&&K(a,"target",Va);a.push(">");null!==cc&&(a.push('<input type="hidden"'),J(a,"name",cc),a.push("/>"),null!==bc&&bc.forEach(nc,a));O(a,nd,Sa);if("string"===typeof Sa){a.push(B(Sa));var od=null}else od=Sa;return od;case "menuitem":a.push(Q("menuitem"));for(var vb in c)if(A.call(c,vb)){var pd=c[vb];if(null!=pd)switch(vb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:K(a,vb,pd)}}a.push(">");
return null;case "title":if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var dc=sc(a,c);else k?dc=null:(sc(e.hoistableChunks,c),dc=void 0);return dc;case "link":var Ze=c.rel,ja=c.href,wb=c.precedence;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof Ze||"string"!==typeof ja||""===ja){P(a,c);var Xa=null}else if("stylesheet"===c.rel)if("string"!==typeof wb||null!=c.disabled||c.onLoad||c.onError)Xa=P(a,c);else{var Ca=e.styles.get(wb),xb=d.styleResources.hasOwnProperty(ja)?
d.styleResources[ja]:void 0;if(null!==xb){d.styleResources[ja]=null;Ca||(Ca={precedence:B(wb),rules:[],hrefs:[],sheets:new Map},e.styles.set(wb,Ca));var yb={state:0,props:t({},c,{"data-precedence":c.precedence,precedence:null})};if(xb){2===xb.length&&zc(yb.props,xb);var ec=e.preloads.stylesheets.get(ja);ec&&0<ec.length?ec.length=0:yb.state=1}Ca.sheets.set(ja,yb);f&&f.stylesheets.add(yb)}else if(Ca){var qd=Ca.sheets.get(ja);qd&&f&&f.stylesheets.add(qd)}h&&a.push("\x3c!-- --\x3e");Xa=null}else c.onLoad||
c.onError?Xa=P(a,c):(h&&a.push("\x3c!-- --\x3e"),Xa=k?null:P(e.hoistableChunks,c));return Xa;case "script":var fc=c.async;if("string"!==typeof c.src||!c.src||!fc||"function"===typeof fc||"symbol"===typeof fc||c.onLoad||c.onError||3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var rd=uc(a,c);else{var zb=c.src;if("module"===c.type){var Ab=d.moduleScriptResources;var sd=e.preloads.moduleScripts}else Ab=d.scriptResources,sd=e.preloads.scripts;var Bb=Ab.hasOwnProperty(zb)?Ab[zb]:void 0;if(null!==
Bb){Ab[zb]=null;var gc=c;if(Bb){2===Bb.length&&(gc=t({},c),zc(gc,Bb));var td=sd.get(zb);td&&(td.length=0)}var ud=[];e.scripts.add(ud);uc(ud,gc)}h&&a.push("\x3c!-- --\x3e");rd=null}return rd;case "style":var Cb=c.precedence,ra=c.href;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof Cb||"string"!==typeof ra||""===ra){a.push(Q("style"));var Da=null,vd=null,Za;for(Za in c)if(A.call(c,Za)){var Db=c[Za];if(null!=Db)switch(Za){case "children":Da=Db;break;case "dangerouslySetInnerHTML":vd=
Db;break;default:K(a,Za,Db)}}a.push(">");var $a=Array.isArray(Da)?2>Da.length?Da[0]:null:Da;"function"!==typeof $a&&"symbol"!==typeof $a&&null!==$a&&void 0!==$a&&a.push(B(""+$a));O(a,vd,Da);a.push(tc("style"));var wd=null}else{var sa=e.styles.get(Cb);if(null!==(d.styleResources.hasOwnProperty(ra)?d.styleResources[ra]:void 0)){d.styleResources[ra]=null;sa?sa.hrefs.push(B(ra)):(sa={precedence:B(Cb),rules:[],hrefs:[B(ra)],sheets:new Map},e.styles.set(Cb,sa));var xd=sa.rules,Ea=null,yd=null,Eb;for(Eb in c)if(A.call(c,
Eb)){var hc=c[Eb];if(null!=hc)switch(Eb){case "children":Ea=hc;break;case "dangerouslySetInnerHTML":yd=hc}}var ab=Array.isArray(Ea)?2>Ea.length?Ea[0]:null:Ea;"function"!==typeof ab&&"symbol"!==typeof ab&&null!==ab&&void 0!==ab&&xd.push(B(""+ab));O(xd,yd,Ea)}sa&&f&&f.styles.add(sa);h&&a.push("\x3c!-- --\x3e");wd=void 0}return wd;case "meta":if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var zd=rc(a,c,"meta");else h&&a.push("\x3c!-- --\x3e"),zd=k?null:"string"===typeof c.charSet?rc(e.charsetChunks,
c,"meta"):"viewport"===c.name?rc(e.viewportChunks,c,"meta"):rc(e.hoistableChunks,c,"meta");return zd;case "listing":case "pre":a.push(Q(b));var bb=null,cb=null,db;for(db in c)if(A.call(c,db)){var Fb=c[db];if(null!=Fb)switch(db){case "children":bb=Fb;break;case "dangerouslySetInnerHTML":cb=Fb;break;default:K(a,db,Fb)}}a.push(">");if(null!=cb){if(null!=bb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof cb||!("__html"in cb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");
var ta=cb.__html;null!==ta&&void 0!==ta&&("string"===typeof ta&&0<ta.length&&"\n"===ta[0]?a.push("\n",ta):a.push(""+ta))}"string"===typeof bb&&"\n"===bb[0]&&a.push("\n");return bb;case "img":var N=c.src,H=c.srcSet;if(!("lazy"===c.loading||!N&&!H||"string"!==typeof N&&null!=N||"string"!==typeof H&&null!=H)&&"low"!==c.fetchPriority&&!1===!!(g.tagScope&2)&&("string"!==typeof N||":"!==N[4]||"d"!==N[0]&&"D"!==N[0]||"a"!==N[1]&&"A"!==N[1]||"t"!==N[2]&&"T"!==N[2]||"a"!==N[3]&&"A"!==N[3])&&("string"!==typeof H||
":"!==H[4]||"d"!==H[0]&&"D"!==H[0]||"a"!==H[1]&&"A"!==H[1]||"t"!==H[2]&&"T"!==H[2]||"a"!==H[3]&&"A"!==H[3])){var Ad="string"===typeof c.sizes?c.sizes:void 0,Fa=H?H+"\n"+(Ad||""):N,ic=e.preloads.images,ua=ic.get(Fa);if(ua){if("high"===c.fetchPriority||10>e.highImagePreloads.size)ic.delete(Fa),e.highImagePreloads.add(ua)}else if(!d.imageResources.hasOwnProperty(Fa)){d.imageResources[Fa]=C;var jc=c.crossOrigin;var Bd="string"===typeof jc?"use-credentials"===jc?jc:"":void 0;var X=e.headers,kc;X&&0<X.remainingCapacity&&
("high"===c.fetchPriority||500>X.highImagePreloads.length)&&(kc=Ac(N,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:Bd,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(X.remainingCapacity-=kc.length))?(e.resets.image[Fa]=C,X.highImagePreloads&&(X.highImagePreloads+=", "),X.highImagePreloads+=kc):(ua=[],P(ua,{rel:"preload",as:"image",href:H?void 0:N,imageSrcSet:H,imageSizes:Ad,crossOrigin:Bd,integrity:c.integrity,type:c.type,
fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(ua):(e.bulkPreloads.add(ua),ic.set(Fa,ua)))}}return rc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return rc(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;
case "head":if(2>g.insertionMode&&null===e.headChunks){e.headChunks=[];var Cd=vc(e.headChunks,c,"head")}else Cd=vc(a,c,"head");return Cd;case "html":if(0===g.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var Dd=vc(e.htmlChunks,c,"html")}else Dd=vc(a,c,"html");return Dd;default:if(-1!==b.indexOf("-")){a.push(Q(b));var lc=null,Ed=null,Ga;for(Ga in c)if(A.call(c,Ga)){var va=c[Ga];if(null!=va){var $e=Ga;switch(Ga){case "children":lc=va;break;case "dangerouslySetInnerHTML":Ed=va;break;case "style":Zb(a,
va);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;default:pb(Ga)&&"function"!==typeof va&&"symbol"!==typeof va&&a.push(" ",$e,'="',B(va),'"')}}}a.push(">");O(a,Ed,lc);return lc}}return vc(a,c,b)}var Bc=new Map;function tc(a){var b=Bc.get(a);void 0===b&&(b="</"+a+">",Bc.set(a,b));return b}function Cc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}
function Dc(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Ec(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function Fc(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var Gc=/[<\u2028\u2029]/g;
function Hc(a){return JSON.stringify(a).replace(Gc,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Ic=/[&><\u2028\u2029]/g;
function Jc(a){return JSON.stringify(a).replace(Ic,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Kc=!1,Lc=!0;
function Mc(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);Lc=this.push("</style>");Kc=!0;b.length=0;c.length=0}}function Nc(a){return 2!==a.state?Kc=!0:!1}function Oc(a,b,c){Kc=!1;Lc=!0;b.styles.forEach(Mc,a);b.stylesheets.forEach(Nc);Kc&&(c.stylesToHoist=!0);return Lc}
function R(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var Pc=[];function Qc(a){P(Pc,a.props);for(var b=0;b<Pc.length;b++)this.push(Pc[b]);Pc.length=0;a.state=2}
function Rc(a){var b=0<a.sheets.size;a.sheets.forEach(Qc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function Sc(a){if(0===a.state){a.state=1;var b=a.props;P(Pc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<Pc.length;a++)this.push(Pc[a]);Pc.length=0}}function Tc(a){a.sheets.forEach(Sc,this);a.sheets.clear()}
function Uc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=Jc(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=Jc(""+d.props.href);a.push(g);e=""+e;a.push(",");e=Jc(e);a.push(e);for(var h in f)if(A.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!pb(h))break a;g=""+g}e.push(",");k=Jc(k);e.push(k);e.push(",");
g=Jc(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function Vc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=B(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=B(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=B(JSON.stringify(e));a.push(e);for(var h in f)if(A.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!pb(h))break a;g=""+g}e.push(",");k=B(JSON.stringify(k));
e.push(k);e.push(",");g=B(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}function Wc(){return{styles:new Set,stylesheets:new Set}}
function Lb(a){var b=S?S:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(Xc,Yc)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],P(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Zc(b)}}}
function Mb(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(Xc,Yc)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace($c,ad);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],P(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Zc(c)}}}
function Nb(a,b,c){var d=S?S:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var m=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(m))return;e.imageResources[m]=C;e=f.headers;var n;e&&0<e.remainingCapacity&&"high"===k&&(n=Ac(a,b,c),2<=(e.remainingCapacity-=n.length))?(f.resets.image[m]=C,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=n):(e=[],P(e,t({rel:"preload",href:g?
void 0:a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(m,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];P(g,t({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?C:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
P(g,t({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?C:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=C;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(m=Ac(a,b,c),2<=(e.remainingCapacity-=m.length)))f.resets.font[a]=C,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=m;else switch(e=[],
a=t({rel:"preload",href:a,as:b},c),P(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Zc(d)}}}
function Ob(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?C:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=C}P(f,t({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Zc(c)}}}
function Pb(a,b,c){var d=S?S:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:B(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:t({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&zc(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Zc(d))}}}
function Qb(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=t({src:a,async:!0},b),f&&(2===f.length&&zc(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),uc(a,b),Zc(c))}}}
function Rb(a,b){var c=S?S:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=t({src:a,type:"module",async:!0},b),f&&(2===f.length&&zc(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),uc(a,b),Zc(c))}}}function zc(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Ac(a,b,c){a=(""+a).replace(Xc,Yc);b=(""+b).replace($c,ad);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)A.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace($c,ad)+'"'));return b}var Xc=/[<>\r\n]/g;
function Yc(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var $c=/["';,\r\n]/g;
function ad(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function bd(a){this.styles.add(a)}function cd(a){this.stylesheets.add(a)}
function dd(a,b){var c=a.idPrefix,d=[],e=a.bootstrapScriptContent,f=a.bootstrapScripts,g=a.bootstrapModules;void 0!==e&&d.push("<script>",(""+e).replace(Tb,Ub),"\x3c/script>");e=c+"P:";var h=c+"S:";c+="B:";var k=new Set,m=new Set,n=new Set,l=new Map,u=new Set,q=new Set,z=new Set,v={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};if(void 0!==f)for(var y=0;y<f.length;y++){var x=f[y],p,D=void 0,E=void 0,r={rel:"preload",as:"script",fetchPriority:"low",nonce:void 0};"string"===
typeof x?r.href=p=x:(r.href=p=x.src,r.integrity=E="string"===typeof x.integrity?x.integrity:void 0,r.crossOrigin=D="string"===typeof x||null==x.crossOrigin?void 0:"use-credentials"===x.crossOrigin?"use-credentials":"");x=a;var w=p;x.scriptResources[w]=null;x.moduleScriptResources[w]=null;x=[];P(x,r);u.add(x);d.push('<script src="',B(p));"string"===typeof E&&d.push('" integrity="',B(E));"string"===typeof D&&d.push('" crossorigin="',B(D));d.push('" async="">\x3c/script>')}if(void 0!==g)for(f=0;f<g.length;f++)r=
g[f],D=p=void 0,E={rel:"modulepreload",fetchPriority:"low",nonce:void 0},"string"===typeof r?E.href=y=r:(E.href=y=r.src,E.integrity=D="string"===typeof r.integrity?r.integrity:void 0,E.crossOrigin=p="string"===typeof r||null==r.crossOrigin?void 0:"use-credentials"===r.crossOrigin?"use-credentials":""),r=a,x=y,r.scriptResources[x]=null,r.moduleScriptResources[x]=null,r=[],P(r,E),u.add(r),d.push('<script type="module" src="',B(y)),"string"===typeof D&&d.push('" integrity="',B(D)),"string"===typeof p&&
d.push('" crossorigin="',B(p)),d.push('" async="">\x3c/script>');return{placeholderPrefix:e,segmentPrefix:h,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:d,importMapChunks:[],onHeaders:void 0,headers:null,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],viewportChunks:[],hoistableChunks:[],preconnects:k,fontPreloads:m,highImagePreloads:n,styles:l,bootstrapScripts:u,
scripts:q,bulkPreloads:z,preloads:v,stylesToHoist:!1,generateStaticMarkup:b}}function ed(a,b,c,d){if(c.generateStaticMarkup)return a.push(B(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(B(b)),a=!0);return a}var fd=Symbol.for("react.client.reference");
function gd(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===fd?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case wa:return"Fragment";case ma:return"Portal";case ya:return"Profiler";case xa:return"StrictMode";case Ja:return"Suspense";case Ka:return"SuspenseList";case ib:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case za:return(a._context.displayName||"Context")+".Provider";case Ha:return(a.displayName||"Context")+".Consumer";case Ia:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case La:return b=a.displayName||null,null!==b?b:gd(a.type)||"Memo";case Ya:b=a._payload;a=a._init;try{return gd(a(b))}catch(c){}}return null}var hd={};function Fd(a,b){a=a.contextTypes;if(!a)return hd;var c={},d;for(d in a)c[d]=b[d];return c}var Gd=null;
function Hd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Hd(a,c)}b.context._currentValue2=b.value}}function Id(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&Id(a)}
function Jd(a){var b=a.parent;null!==b&&Jd(b);a.context._currentValue2=a.value}function Kd(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Hd(a,b):Kd(a,b)}
function Ld(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Hd(a,c):Ld(a,c);b.context._currentValue2=b.value}function Md(a){var b=Gd;b!==a&&(null===b?Jd(a):null===a?Id(b):b.depth===a.depth?Hd(b,a):b.depth>a.depth?Kd(b,a):Ld(b,a),Gd=a)}
var Nd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Od(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Nd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:t({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Nd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=t({},f,h)):t(f,h))}a.state=f}else f.queue=null}
var Pd={id:1,overflow:""};function Qd(a,b,c){var d=a.id;a=a.overflow;var e=32-Rd(d)-1;d&=~(1<<e);c+=1;var f=32-Rd(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Rd(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Rd=Math.clz32?Math.clz32:Sd,Td=Math.log,Ud=Math.LN2;function Sd(a){a>>>=0;return 0===a?32:31-(Td(a)/Ud|0)|0}var Vd=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Wd(){}function Xd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Wd,Wd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Yd=b;throw Vd;}}var Yd=null;
function Zd(){if(null===Yd)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Yd;Yd=null;return a}function $d(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var ae="function"===typeof Object.is?Object.is:$d,be=null,ce=null,de=null,ee=null,fe=null,T=null,ge=!1,he=!1,ie=0,je=0,ke=-1,le=0,me=null,ne=null,oe=0;
function pe(){if(null===be)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return be}
function qe(){if(0<oe)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function re(){null===T?null===fe?(ge=!1,fe=T=qe()):(ge=!0,T=fe):null===T.next?(ge=!1,T=T.next=qe()):(ge=!0,T=T.next);return T}function se(){var a=me;me=null;return a}function te(){ee=de=ce=be=null;he=!1;fe=null;oe=0;T=ne=null}function ue(a,b){return"function"===typeof b?b(a):b}
function ve(a,b,c){be=pe();T=re();if(ge){var d=T.queue;b=d.dispatch;if(null!==ne&&(c=ne.get(d),void 0!==c)){ne.delete(d);d=T.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);T.memoizedState=d;return[d,b]}return[T.memoizedState,b]}a=a===ue?"function"===typeof b?b():b:void 0!==c?c(b):b;T.memoizedState=a;a=T.queue={last:null,dispatch:null};a=a.dispatch=we.bind(null,be,a);return[T.memoizedState,a]}
function xe(a,b){be=pe();T=re();b=void 0===b?null:b;if(null!==T){var c=T.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!ae(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();T.memoizedState=[a,b];return a}
function we(a,b,c){if(25<=oe)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===be)if(he=!0,a={action:c,next:null},null===ne&&(ne=new Map),c=ne.get(b),void 0===c)ne.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function ye(){throw Error("startTransition cannot be called during server rendering.");}function ze(){throw Error("Cannot update optimistic state while rendering.");}
function Ae(a){var b=le;le+=1;null===me&&(me=[]);return Xd(me,a,b)}function Be(){throw Error("Cache cannot be refreshed during server rendering.");}function Ce(){}
var Ee={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Ae(a);if(a.$$typeof===Ha)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){pe();return a._currentValue2},useMemo:xe,useReducer:ve,useRef:function(a){be=pe();T=re();var b=T.memoizedState;return null===b?(a={current:a},T.memoizedState=a):b},useState:function(a){return ve(ue,a)},useInsertionEffect:Ce,
useLayoutEffect:Ce,useCallback:function(a,b){return xe(function(){return a},b)},useImperativeHandle:Ce,useEffect:Ce,useDebugValue:Ce,useDeferredValue:function(a){pe();return a},useTransition:function(){pe();return[!1,ye]},useId:function(){var a=ce.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Rd(a)-1)).toString(32)+b;var c=De;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=ie++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));
return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return Be},useHostTransitionStatus:function(){pe();return Jb},useOptimistic:function(a){pe();return[a,ze]},useFormState:function(a,b,c){pe();var d=je++,e=de;if("function"===typeof a.$$FORM_ACTION){var f=null,g=ee;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===
typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+lb(JSON.stringify([g,null,d]),0),k===f&&(ke=d,b=e[0]))}var m=a.bind(null,b);a=function(l){m(l)};"function"===typeof m.$$FORM_ACTION&&(a.$$FORM_ACTION=function(l){l=m.$$FORM_ACTION(l);void 0!==c&&(c+="",l.action=c);var u=l.data;u&&(null===f&&(f=void 0!==c?"p"+c:"k"+lb(JSON.stringify([g,null,d]),0)),u.append("$ACTION_KEY",f));return l});return[b,a]}var n=a.bind(null,b);return[b,function(l){n(l)}]}},De=null,Fe={getCacheSignal:function(){throw Error("Not implemented.");
},getCacheForType:function(){throw Error("Not implemented.");}},Ge;function He(a){if(void 0===Ge)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);Ge=b&&b[1]||""}return"\n"+Ge+a}var Ie=!1;
function Je(a,b){if(!a||Ie)return"";Ie=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var l=function(){throw Error();};Object.defineProperty(l.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(l,[])}catch(q){var u=q}Reflect.construct(a,[],l)}else{try{l.call()}catch(q){u=q}a.call(l.prototype)}}else{try{throw Error();}catch(q){u=q}(l=a())&&"function"===typeof l.catch&&
l.catch(function(){})}}catch(q){if(q&&u&&"string"===typeof q.stack)return[q.stack,u.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var k=g.split("\n"),m=h.split("\n");for(e=d=0;d<k.length&&!k[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<m.length&&!m[e].includes("DetermineComponentFrameRoot");)e++;if(d===k.length||e===m.length)for(d=k.length-1,e=m.length-1;1<=d&&0<=e&&k[d]!==m[e];)e--;for(;1<=d&&0<=e;d--,e--)if(k[d]!==m[e]){if(1!==d||1!==e){do if(d--,e--,0>e||k[d]!==m[e]){var n="\n"+k[d].replace(" at new "," at ");a.displayName&&n.includes("<anonymous>")&&(n=n.replace("<anonymous>",a.displayName));return n}while(1<=d&&0<=e)}break}}}finally{Ie=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?He(c):""}
var Ke=Ib.ReactCurrentDispatcher,Le=Ib.ReactCurrentCache;function Me(a){console.error(a);return null}function Ne(){}
function Oe(a,b,c,d,e,f,g,h,k,m,n,l){Kb.current=Sb;var u=[],q=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:q,pingedTasks:u,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?Me:f,onPostpone:void 0===n?Ne:n,onAllReady:void 0===g?
Ne:g,onShellReady:void 0===h?Ne:h,onShellError:void 0===k?Ne:k,onFatalError:void 0===m?Ne:m,formState:void 0===l?null:l};c=Pe(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Qe(b,null,a,-1,null,c,null,q,null,d,hd,null,Pd,null,!1);u.push(a);return b}var S=null;function Re(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,Se(a))}
function Te(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,contentState:Wc(),fallbackState:Wc(),trackedContentKeyPath:null,trackedFallbackNode:null}}
function Qe(a,b,c,d,e,f,g,h,k,m,n,l,u,q,z){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var v={replay:null,node:c,childIndex:d,ping:function(){return Re(a,v)},blockedBoundary:e,blockedSegment:f,hoistableState:g,abortSet:h,keyPath:k,formatContext:m,legacyContext:n,context:l,treeContext:u,componentStack:q,thenableState:b,isFallback:z};h.add(v);return v}
function Ue(a,b,c,d,e,f,g,h,k,m,n,l,u,q,z){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var v={replay:c,node:d,childIndex:e,ping:function(){return Re(a,v)},blockedBoundary:f,blockedSegment:null,hoistableState:g,abortSet:h,keyPath:k,formatContext:m,legacyContext:n,context:l,treeContext:u,componentStack:q,thenableState:b,isFallback:z};h.add(v);return v}
function Pe(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function Ve(a,b){return{tag:0,parent:a.componentStack,type:b}}
function We(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=He(b.type,null);break;case 1:a+=Je(b.type,!1);break;case 2:a+=Je(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function V(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function Xe(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}function af(a,b,c,d,e,f){var g=b.thenableState;b.thenableState=null;be={};ce=b;de=a;ee=c;je=ie=0;ke=-1;le=0;me=g;for(a=d(e,f);he;)he=!1,je=ie=0,ke=-1,le=0,oe+=1,T=null,a=d(e,f);te();return a}
function bf(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((gd(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=t({},c,d)}b.legacyContext=e;W(a,b,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,W(a,b,f,-1),b.keyPath=e}
function cf(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var m=0;m<f;m++)m===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Qd(c,1,0),Y(a,b,d,-1),b.treeContext=c):h?Y(a,b,d,-1):W(a,b,d,-1);b.keyPath=f}function df(a,b){if(a&&a.defaultProps){b=t({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function ef(a,b,c,d,e,f){if("function"===typeof d)if(d.prototype&&d.prototype.isReactComponent){f=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:d};var g=Fd(d,b.legacyContext);var h=d.contextType;h=new d(e,"object"===typeof h&&null!==h?h._currentValue2:g);Od(h,d,e,g);bf(a,b,c,h,d);b.componentStack=f}else{f=Fd(d,b.legacyContext);g=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d};h=af(a,b,c,d,e,f);var k=0!==ie,m=je,n=ke;"object"===typeof h&&null!==h&&"function"===
typeof h.render&&void 0===h.$$typeof?(Od(h,d,e,f),bf(a,b,c,h,d)):cf(a,b,c,h,k,m,n);b.componentStack=g}else if("string"===typeof d){f=b.componentStack;b.componentStack=Ve(b,d);g=b.blockedSegment;if(null===g)g=e.children,h=b.formatContext,k=b.keyPath,b.formatContext=Xb(h,d,e),b.keyPath=c,Y(a,b,g,-1),b.formatContext=h,b.keyPath=k;else{k=yc(g.chunks,d,e,a.resumableState,a.renderState,b.hoistableState,b.formatContext,g.lastPushedText,b.isFallback);g.lastPushedText=!1;h=b.formatContext;m=b.keyPath;b.formatContext=
Xb(h,d,e);b.keyPath=c;Y(a,b,k,-1);b.formatContext=h;b.keyPath=m;a:{c=g.chunks;a=a.resumableState;switch(d){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(tc(d))}g.lastPushedText=!1}b.componentStack=
f}else{switch(d){case hb:case fb:case xa:case ya:case wa:d=b.keyPath;b.keyPath=c;W(a,b,e.children,-1);b.keyPath=d;return;case gb:"hidden"!==e.mode&&(d=b.keyPath,b.keyPath=c,W(a,b,e.children,-1),b.keyPath=d);return;case Ka:d=b.componentStack;b.componentStack=Ve(b,"SuspenseList");f=b.keyPath;b.keyPath=c;W(a,b,e.children,-1);b.keyPath=f;b.componentStack=d;return;case eb:throw Error("ReactDOMServer does not yet support scope components.");case Ja:a:if(null!==b.replay){d=b.keyPath;b.keyPath=c;c=e.children;
try{Y(a,b,c,-1)}finally{b.keyPath=d}}else{var l=b.componentStack;d=b.componentStack=Ve(b,"Suspense");var u=b.keyPath;f=b.blockedBoundary;var q=b.hoistableState,z=b.blockedSegment;g=e.fallback;var v=e.children;e=new Set;h=Te(a,e);null!==a.trackedPostpones&&(h.trackedContentKeyPath=c);k=Pe(a,z.chunks.length,h,b.formatContext,!1,!1);z.children.push(k);z.lastPushedText=!1;var y=Pe(a,0,null,b.formatContext,!1,!1);y.parentFlushed=!0;b.blockedBoundary=h;b.hoistableState=h.contentState;b.blockedSegment=y;
b.keyPath=c;try{if(Y(a,b,v,-1),a.renderState.generateStaticMarkup||y.lastPushedText&&y.textEmbedded&&y.chunks.push("\x3c!-- --\x3e"),y.status=1,ff(h,y),0===h.pendingTasks&&0===h.status){h.status=1;b.componentStack=l;break a}}catch(x){y.status=4,h.status=4,m=We(a,b.componentStack),n=V(a,x,m),h.errorDigest=n,gf(a,h)}finally{b.blockedBoundary=f,b.hoistableState=q,b.blockedSegment=z,b.keyPath=u,b.componentStack=l}m=[c[0],"Suspense Fallback",c[2]];n=a.trackedPostpones;null!==n&&(l=[m[1],m[2],[],null],
n.workingMap.set(m,l),5===h.status?n.workingMap.get(c)[4]=l:h.trackedFallbackNode=l);b=Qe(a,null,g,-1,f,k,h.fallbackState,e,m,b.formatContext,b.legacyContext,b.context,b.treeContext,d,!0);a.pingedTasks.push(b)}return}if("object"===typeof d&&null!==d)switch(d.$$typeof){case Ia:g=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d.render};e=af(a,b,c,d.render,e,f);cf(a,b,c,e,0!==ie,je,ke);b.componentStack=g;return;case La:d=d.type;e=df(d,e);ef(a,b,c,d,e,f);return;case za:g=e.children;
f=b.keyPath;d=d._context;e=e.value;h=d._currentValue2;d._currentValue2=e;k=Gd;Gd=e={parent:k,depth:null===k?0:k.depth+1,context:d,parentValue:h,value:e};b.context=e;b.keyPath=c;W(a,b,g,-1);a=Gd;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");a.context._currentValue2=a.parentValue;a=Gd=a.parent;b.context=a;b.keyPath=f;return;case Ha:e=e.children;e=e(d._currentValue2);d=b.keyPath;b.keyPath=c;W(a,b,e,-1);b.keyPath=d;return;case Aa:case Ya:f=b.componentStack;
b.componentStack=Ve(b,"Lazy");g=d._init;d=g(d._payload);e=df(d,e);ef(a,b,c,d,e,void 0);b.componentStack=f;return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==d?d:typeof d)+"."));}}
function hf(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Pe(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Y(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(ff(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function W(a,b,c,d){if(null!==b.replay&&"number"===typeof b.replay.slots)hf(a,b,b.replay.slots,c,d);else if(b.node=c,b.childIndex=d,null!==c){if("object"===typeof c){switch(c.$$typeof){case la:var e=c.type,f=c.key,g=c.props;var h=c.ref;var k=gd(e),m=null==f?-1===d?0:d:f;f=[b.keyPath,k,m];if(null!==b.replay)a:{var n=b.replay;d=n.nodes;for(c=0;c<d.length;c++){var l=d[c];if(m===l[1]){if(4===l.length){if(null!==k&&k!==l[0])throw Error("Expected the resume to render <"+l[0]+"> in this slot but instead it rendered <"+
k+">. The tree doesn't match so React will fallback to client rendering.");var u=l[2];k=l[3];m=b.node;b.replay={nodes:u,slots:k,pendingTasks:1};try{ef(a,b,f,e,g,h);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(w){if("object"===typeof w&&null!==w&&(w===Vd||"function"===typeof w.then))throw b.node===m&&(b.replay=n),w;
b.replay.pendingTasks--;g=We(a,b.componentStack);f=a;a=b.blockedBoundary;e=w;g=V(f,e,g);jf(f,a,u,k,e,g)}b.replay=n}else{if(e!==Ja)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(gd(e)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{n=void 0;e=l[5];h=l[2];k=l[3];m=null===l[4]?[]:l[4][2];l=null===l[4]?null:l[4][3];var q=b.componentStack,z=b.componentStack=Ve(b,"Suspense"),v=b.keyPath,y=b.replay,x=b.blockedBoundary,
p=b.hoistableState,D=g.children;g=g.fallback;var E=new Set,r=Te(a,E);r.parentFlushed=!0;r.rootSegmentID=e;b.blockedBoundary=r;b.hoistableState=r.contentState;b.replay={nodes:h,slots:k,pendingTasks:1};try{Y(a,b,D,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===r.pendingTasks&&0===r.status){r.status=1;a.completedBoundaries.push(r);
break b}}catch(w){r.status=4,u=We(a,b.componentStack),n=V(a,w,u),r.errorDigest=n,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(r)}finally{b.blockedBoundary=x,b.hoistableState=p,b.replay=y,b.keyPath=v,b.componentStack=q}b=Ue(a,null,{nodes:m,slots:l,pendingTasks:0},g,-1,x,r.fallbackState,E,[f[0],"Suspense Fallback",f[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,z,!0);a.pingedTasks.push(b)}}d.splice(c,1);break a}}}else ef(a,b,f,e,g,h);return;case ma:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case Ya:g=b.componentStack;b.componentStack=Ve(b,"Lazy");f=c._init;c=f(c._payload);b.componentStack=g;W(a,b,c,d);return}if(kb(c)){kf(a,b,c,d);return}null===c||"object"!==typeof c?g=null:(g=jb&&c[jb]||c["@@iterator"],g="function"===typeof g?g:null);if(g&&(g=g.call(c))){c=g.next();if(!c.done){f=[];do f.push(c.value),c=g.next();while(!c.done);kf(a,b,f,d)}return}if("function"===typeof c.then)return b.thenableState=null,W(a,b,Ae(c),d);if(c.$$typeof===Ha)return W(a,b,c._currentValue2,d);d=Object.prototype.toString.call(c);
throw Error("Objects are not valid as a React child (found: "+("[object Object]"===d?"object with keys {"+Object.keys(c).join(", ")+"}":d)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof c?(d=b.blockedSegment,null!==d&&(d.lastPushedText=ed(d.chunks,c,a.renderState,d.lastPushedText))):"number"===typeof c&&(d=b.blockedSegment,null!==d&&(d.lastPushedText=ed(d.chunks,""+c,a.renderState,d.lastPushedText)))}}
function kf(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{kf(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(l){if("object"===typeof l&&
null!==l&&(l===Vd||"function"===typeof l.then))throw l;b.replay.pendingTasks--;c=We(a,b.componentStack);var m=b.blockedBoundary,n=l;c=V(a,n,c);jf(a,m,d,k,n,c)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)k=c[d],b.treeContext=Qd(f,g,d),m=h[d],"number"===typeof m?(hf(a,b,m,k,d),delete h[d]):Y(a,b,k,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Qd(f,g,h),
Y(a,b,d,h);b.treeContext=f;b.keyPath=e}function gf(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function Y(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,m=b.componentStack,n=b.blockedSegment;if(null===n)try{return W(a,b,c,d)}catch(q){if(te(),c=q===Vd?Zd():q,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=se();a=Ue(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.hoistableState,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,
a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=m;Md(g);return}}else{var l=n.children.length,u=n.chunks.length;try{return W(a,b,c,d)}catch(q){if(te(),n.children.length=l,n.chunks.length=u,c=q===Vd?Zd():q,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=se();n=b.blockedSegment;l=Pe(a,n.chunks.length,null,b.formatContext,n.lastPushedText,!0);n.children.push(l);n.lastPushedText=!1;a=Qe(a,d,b.node,b.childIndex,b.blockedBoundary,l,b.hoistableState,
b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=m;Md(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Md(g);throw c;}function lf(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,mf(this,b,a))}
function jf(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)jf(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,m=f,n=Te(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=m;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var l in d)delete d[l]}}
function nf(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){V(b,c,d);Xe(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=V(b,c,d),jf(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&of(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=We(b,a.componentStack),a=V(b,c,a),d.errorDigest=a,gf(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return nf(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&pf(b)}
function qf(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),m=k.next();0<e.remainingCapacity&&!m.done;m=k.next()){var n=m.value,l=n.props,u=l.href,q=n.props,z=Ac(q.href,"style",{crossOrigin:q.crossOrigin,integrity:q.integrity,
nonce:q.nonce,type:q.type,fetchPriority:q.fetchPriority,referrerPolicy:q.referrerPolicy,media:q.media});if(2<=(e.remainingCapacity-=z.length))c.resets.style[u]=C,f&&(f+=", "),f+=z,c.resets.style[u]="string"===typeof l.crossOrigin||"string"===typeof l.integrity?[l.crossOrigin,l.integrity]:C;else break b}}f?d({Link:f}):d({})}}}catch(v){V(a,v,{})}}function of(a){null===a.trackedPostpones&&qf(a,!0);a.onShellError=Ne;a=a.onShellReady;a()}
function pf(a){qf(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function ff(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&ff(a,c)}else a.completedSegments.push(b)}
function mf(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&of(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&ff(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(lf,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(ff(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&pf(a)}
function Se(a){if(2!==a.status){var b=Gd,c=Ke.current;Ke.current=Ee;var d=Le.current;Le.current=Fe;var e=S;S=a;var f=De;De=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],m=a,n=k.blockedSegment;if(null===n){var l=m;if(0!==k.replay.pendingTasks){Md(k.context);try{W(l,k,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);mf(l,k.blockedBoundary,null)}catch(G){te();var u=G===Vd?Zd():G;if("object"===typeof u&&null!==u&&"function"===typeof u.then){var q=k.ping;u.then(q,q);k.thenableState=se()}else{k.replay.pendingTasks--;k.abortSet.delete(k);var z=We(l,k.componentStack);m=void 0;var v=l,y=k.blockedBoundary,x=u,p=k.replay.nodes,D=k.replay.slots;m=V(v,x,z);jf(v,y,p,D,x,m);l.pendingRootTasks--;0===l.pendingRootTasks&&of(l);l.allPendingTasks--;0===l.allPendingTasks&&pf(l)}}finally{}}}else if(l=
void 0,v=n,0===v.status){Md(k.context);var E=v.children.length,r=v.chunks.length;try{W(m,k,k.node,k.childIndex),m.renderState.generateStaticMarkup||v.lastPushedText&&v.textEmbedded&&v.chunks.push("\x3c!-- --\x3e"),k.abortSet.delete(k),v.status=1,mf(m,k.blockedBoundary,v)}catch(G){te();v.children.length=E;v.chunks.length=r;var w=G===Vd?Zd():G;if("object"===typeof w&&null!==w&&"function"===typeof w.then){var F=k.ping;w.then(F,F);k.thenableState=se()}else{var U=We(m,k.componentStack);k.abortSet.delete(k);
v.status=4;var L=k.blockedBoundary;l=V(m,w,U);null===L?Xe(m,w):(L.pendingTasks--,4!==L.status&&(L.status=4,L.errorDigest=l,gf(m,L),L.parentFlushed&&m.clientRenderedBoundaries.push(L)));m.allPendingTasks--;0===m.allPendingTasks&&pf(m)}}finally{}}}g.splice(0,h);null!==a.destination&&rf(a,a.destination)}catch(G){V(a,G,{}),Xe(a,G)}finally{De=f,Ke.current=c,Le.current=d,c===Ee&&Md(b),S=e}}}
function sf(a,b,c,d){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:return d=c.id,c.lastPushedText=!1,c.textEmbedded=!1,a=a.renderState,b.push('<template id="'),b.push(a.placeholderPrefix),a=d.toString(16),b.push(a),b.push('"></template>');case 1:c.status=2;var e=!0,f=c.chunks,g=0;c=c.children;for(var h=0;h<c.length;h++){for(e=c[h];g<e.index;g++)b.push(f[g]);e=tf(a,b,e,d)}for(;g<f.length-1;g++)b.push(f[g]);g<f.length&&(e=b.push(f[g]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function tf(a,b,c,d){var e=c.boundary;if(null===e)return sf(a,b,c,d);e.parentFlushed=!0;if(4===e.status)return a.renderState.generateStaticMarkup||(e=e.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),e&&(b.push(' data-dgst="'),e=B(e),b.push(e),b.push('"')),b.push("></template>")),sf(a,b,c,d),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==e.status)return 0===e.status&&(e.rootSegmentID=a.nextSegmentId++),0<e.completedSegments.length&&a.partialBoundaries.push(e),Dc(b,
a.renderState,e.rootSegmentID),d&&(e=e.fallbackState,e.styles.forEach(bd,d),e.stylesheets.forEach(cd,d)),sf(a,b,c,d),b.push("\x3c!--/$--\x3e");if(e.byteSize>a.progressiveChunkSize)return e.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(e),Dc(b,a.renderState,e.rootSegmentID),sf(a,b,c,d),b.push("\x3c!--/$--\x3e");d&&(c=e.contentState,c.styles.forEach(bd,d),c.stylesheets.forEach(cd,d));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=e.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
tf(a,b,c[0],d);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function uf(a,b,c,d){Ec(b,a.renderState,c.parentFormatContext,c.id);tf(a,b,c,d);return Fc(b,c.parentFormatContext)}
function vf(a,b,c){for(var d=c.completedSegments,e=0;e<d.length;e++)wf(a,b,c,d[e]);d.length=0;Oc(b,c.contentState,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.contentState;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),Uc(b,c)):(b.push('" data-sty="'),Vc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Cc(b,a)&&d}
function wf(a,b,c,d){if(2===d.status)return!0;var e=c.contentState,f=d.id;if(-1===f){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return uf(a,b,d,e)}if(f===c.rootSegmentID)return uf(a,b,d,e);uf(a,b,d,e);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);f=f.toString(16);b.push(f);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(f);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function rf(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var m=e.htmlChunks,n=e.headChunks,l;if(m){for(l=0;l<m.length;l++)b.push(m[l]);if(n)for(l=0;l<n.length;l++)b.push(n[l]);else{var u=Q("head");b.push(u);
b.push(">")}}else if(n)for(l=0;l<n.length;l++)b.push(n[l]);var q=e.charsetChunks;for(l=0;l<q.length;l++)b.push(q[l]);q.length=0;e.preconnects.forEach(R,b);e.preconnects.clear();var z=e.viewportChunks;for(l=0;l<z.length;l++)b.push(z[l]);z.length=0;e.fontPreloads.forEach(R,b);e.fontPreloads.clear();e.highImagePreloads.forEach(R,b);e.highImagePreloads.clear();e.styles.forEach(Rc,b);var v=e.importMapChunks;for(l=0;l<v.length;l++)b.push(v[l]);v.length=0;e.bootstrapScripts.forEach(R,b);e.scripts.forEach(R,
b);e.scripts.clear();e.bulkPreloads.forEach(R,b);e.bulkPreloads.clear();var y=e.hoistableChunks;for(l=0;l<y.length;l++)b.push(y[l]);y.length=0;if(m&&null===n){var x=tc("head");b.push(x)}tf(a,b,d,null);a.completedRootSegment=null;Cc(b,a.renderState)}else return;var p=a.renderState;d=0;var D=p.viewportChunks;for(d=0;d<D.length;d++)b.push(D[d]);D.length=0;p.preconnects.forEach(R,b);p.preconnects.clear();p.fontPreloads.forEach(R,b);p.fontPreloads.clear();p.highImagePreloads.forEach(R,b);p.highImagePreloads.clear();
p.styles.forEach(Tc,b);p.scripts.forEach(R,b);p.scripts.clear();p.bulkPreloads.forEach(R,b);p.bulkPreloads.clear();var E=p.hoistableChunks;for(d=0;d<E.length;d++)b.push(E[d]);E.length=0;var r=a.clientRenderedBoundaries;for(c=0;c<r.length;c++){var w=r[c];p=b;var F=a.resumableState,U=a.renderState,L=w.rootSegmentID,G=w.errorDigest,na=w.errorMessage,fa=w.errorComponentStack,Z=0===F.streamingFormat;Z?(p.push(U.startInlineScript),0===(F.instructions&4)?(F.instructions|=4,p.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):
p.push('$RX("')):p.push('<template data-rxi="" data-bid="');p.push(U.boundaryPrefix);var tb=L.toString(16);p.push(tb);Z&&p.push('"');if(G||na||fa)if(Z){p.push(",");var ub=Hc(G||"");p.push(ub)}else{p.push('" data-dgst="');var Ma=B(G||"");p.push(Ma)}if(na||fa)if(Z){p.push(",");var Na=Hc(na||"");p.push(Na)}else{p.push('" data-msg="');var Oa=B(na||"");p.push(Oa)}if(fa)if(Z){p.push(",");var Pa=Hc(fa);p.push(Pa)}else{p.push('" data-stck="');var oa=B(fa);p.push(oa)}if(Z?!p.push(")\x3c/script>"):!p.push('"></template>')){a.destination=
null;c++;r.splice(0,c);return}}r.splice(0,c);var M=a.completedBoundaries;for(c=0;c<M.length;c++)if(!vf(a,b,M[c])){a.destination=null;c++;M.splice(0,c);return}M.splice(0,c);var aa=a.partialBoundaries;for(c=0;c<aa.length;c++){var ba=aa[c];a:{r=a;w=b;var pa=ba.completedSegments;for(F=0;F<pa.length;F++)if(!wf(r,w,ba,pa[F])){F++;pa.splice(0,F);var Qa=!1;break a}pa.splice(0,F);Qa=Oc(w,ba.contentState,r.renderState)}if(!Qa){a.destination=null;c++;aa.splice(0,c);return}}aa.splice(0,c);var qa=a.completedBoundaries;
for(c=0;c<qa.length;c++)if(!vf(a,b,qa[c])){a.destination=null;c++;qa.splice(0,c);return}qa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(aa=tc("body"),b.push(aa)),c.hasHtml&&(c=tc("html"),b.push(c)),b.push(null),a.destination=null)}}function xf(a){a.flushScheduled=null!==a.destination;Se(a);null===a.trackedPostpones&&qf(a,0===a.pendingRootTasks)}
function Zc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?rf(a,b):a.flushScheduled=!1}}function yf(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{rf(a,b)}catch(c){V(a,c,{}),Xe(a,c)}}}
function zf(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return nf(e,a,d)});c.clear()}null!==a.destination&&rf(a,a.destination)}catch(e){V(a,e,{}),Xe(a,e)}}function Af(){}
function Bf(a,b,c,d){var e=!1,f=null,g="",h=!1;b=Vb(b?b.identifierPrefix:void 0,void 0);a=Oe(a,b,dd(b,c),Wb(),Infinity,Af,void 0,function(){h=!0},void 0,void 0,void 0);xf(a);zf(a,d);yf(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");return g}
function Cf(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var Df=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}Cf(b,a);var c=b.prototype;c._destroy=function(d,e){zf(this.request);e(d)};c._read=function(){this.startedFlowing&&yf(this.request,this)};return b}(ka.Readable);function Ef(){}
function Ff(a,b){var c=new Df;b=Vb(b?b.identifierPrefix:void 0,void 0);var d=Oe(a,b,dd(b,!1),Wb(),Infinity,Ef,function(){c.startedFlowing=!0;yf(d,c)},void 0,void 0,void 0);c.request=d;xf(d);return c}exports.renderToNodeStream=function(a,b){return Ff(a,b)};exports.renderToStaticMarkup=function(a,b){return Bf(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return Ff(a,b)};exports.renderToString=function(a,b){return Bf(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-178c267a4e-20241218";

//# sourceMappingURL=react-dom-server-legacy.node.production.min.js.map
