/*
 React
 react-server-dom-webpack-server.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var ba=require("util");require("crypto");var ca=require("async_hooks"),da=require("react"),ea=require("react-dom"),l=null,m=0,q=!0;function r(a,b){a=a.write(b);q=q&&a}
function u(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(r(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),r(a,fa.encode(b));else{var c=l;0<m&&(c=l.subarray(m));c=fa.encodeInto(b,c);var d=c.read;m+=c.written;d<b.length&&(r(a,l.subarray(0,m)),l=new Uint8Array(2048),m=fa.encodeInto(b.slice(d),l).written);2048===m&&(r(a,l),l=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(r(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),r(a,b)):(c=l.length-m,c<b.byteLength&&
(0===c?r(a,l):(l.set(b.subarray(0,c),m),m+=c,r(a,l),b=b.subarray(c)),l=new Uint8Array(2048),m=0),l.set(b,m),m+=b.byteLength,2048===m&&(r(a,l),l=new Uint8Array(2048),m=0)));return q}var fa=new ba.TextEncoder,v=Symbol.for("react.client.reference"),w=Symbol.for("react.server.reference");function x(a,b,c){return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:b},$$async:{value:c}})}var ha=Function.prototype.bind,ia=Array.prototype.slice;
function ja(){var a=ha.apply(this,arguments);if(this.$$typeof===w){var b=ia.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:w},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ja}})}return a}
var ka=Promise.prototype,la={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ma(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=x(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var d=x({},a.$$id,!0),e=new Proxy(d,na);a.status="fulfilled";a.value=e;return a.then=x(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");d=a[b];d||(d=x(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(d,"name",{value:b}),d=a[b]=new Proxy(d,la));return d}
var na={get:function(a,b){return ma(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:ma(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ka},set:function(){throw Error("Cannot assign to a client module from a server module.");}},va={prefetchDNS:oa,preconnect:pa,preload:qa,preloadModule:ra,preinitStyle:sa,preinitScript:ta,preinitModuleScript:ua};
function oa(a){if("string"===typeof a&&a){var b=y();if(b){var c=b.hints,d="D|"+a;c.has(d)||(c.add(d),z(b,"D",a))}}}function pa(a,b){if("string"===typeof a){var c=y();if(c){var d=c.hints,e="C|"+(null==b?"null":b)+"|"+a;d.has(e)||(d.add(e),"string"===typeof b?z(c,"C",[a,b]):z(c,"C",a))}}}
function qa(a,b,c){if("string"===typeof a){var d=y();if(d){var e=d.hints,f="L";if("image"===b&&c){var g=c.imageSrcSet,k=c.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(c=A(c))?z(d,"L",[a,b,c]):z(d,"L",[a,b]))}}}function ra(a,b){if("string"===typeof a){var c=y();if(c){var d=c.hints,e="m|"+a;if(!d.has(e))return d.add(e),(b=A(b))?z(c,"m",[a,b]):z(c,"m",a)}}}
function sa(a,b,c){if("string"===typeof a){var d=y();if(d){var e=d.hints,f="S|"+a;if(!e.has(f))return e.add(f),(c=A(c))?z(d,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?z(d,"S",[a,b]):z(d,"S",a)}}}function ta(a,b){if("string"===typeof a){var c=y();if(c){var d=c.hints,e="X|"+a;if(!d.has(e))return d.add(e),(b=A(b))?z(c,"X",[a,b]):z(c,"X",a)}}}function ua(a,b){if("string"===typeof a){var c=y();if(c){var d=c.hints,e="M|"+a;if(!d.has(e))return d.add(e),(b=A(b))?z(c,"M",[a,b]):z(c,"M",a)}}}
function A(a){if(null==a)return null;var b=!1,c={},d;for(d in a)null!=a[d]&&(b=!0,c[d]=a[d]);return b?c:null}var wa=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,xa=new ca.AsyncLocalStorage,B=Symbol.for("react.element"),ya=Symbol.for("react.fragment"),za=Symbol.for("react.context"),Aa=Symbol.for("react.forward_ref"),Ba=Symbol.for("react.suspense"),Ca=Symbol.for("react.suspense_list"),Da=Symbol.for("react.memo"),D=Symbol.for("react.lazy"),Ea=Symbol.for("react.memo_cache_sentinel");
Symbol.for("react.postpone");var Fa=Symbol.iterator,Ga=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");function Ha(){}
function Ia(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Ha,Ha),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}E=b;throw Ga;}}var E=null;
function Ja(){if(null===E)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=E;E=null;return a}var F=null,Ka=0,G=null;function La(){var a=G||[];G=null;return a}
var Qa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:H,useTransition:H,readContext:Ma,useContext:Ma,useReducer:H,useRef:H,useState:H,useInsertionEffect:H,useLayoutEffect:H,useImperativeHandle:H,useEffect:H,useId:Na,useSyncExternalStore:H,useCacheRefresh:function(){return Oa},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Ea;return b},use:Pa};
function H(){throw Error("This Hook is not supported in Server Components.");}function Oa(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ma(){throw Error("Cannot read a Client Context from a Server Component.");}function Na(){if(null===F)throw Error("useId can only be used while React is rendering");var a=F.identifierCount++;return":"+F.identifierPrefix+"S"+a.toString(32)+":"}
function Pa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ka;Ka+=1;null===G&&(G=[]);return Ia(G,a,b)}a.$$typeof===za&&Ma()}if(a.$$typeof===v){if(null!=a.value&&a.value.$$typeof===za)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Ra(){return(new AbortController).signal}
function Sa(){var a=y();return a?a.cache:new Map}var Ta={getCacheSignal:function(){var a=Sa(),b=a.get(Ra);void 0===b&&(b=Ra(),a.set(Ra,b));return b},getCacheForType:function(a){var b=Sa(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Ua=Array.isArray,Va=Object.getPrototypeOf;function Wa(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function Xa(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Ua(a))return"[...]";if(null!==a&&a.$$typeof===Ya)return"client";a=Wa(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===Ya?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function I(a){if("string"===typeof a)return a;switch(a){case Ba:return"Suspense";case Ca:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Aa:return I(a.render);case Da:return I(a.type);case D:var b=a._payload;a=a._init;try{return I(a(b))}catch(c){}}return""}var Ya=Symbol.for("react.client.reference");
function J(a,b){var c=Wa(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var d=0;if(Ua(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?J(g):Xa(g);""+f===b?(c=e.length,d=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===B)e="<"+I(a.type)+"/>";else{if(a.$$typeof===Ya)return"client";e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h=
"object"===typeof h&&null!==h?J(h):Xa(h);k===b?(c=e.length,d=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<c&&0<d?(a=" ".repeat(c)+"^".repeat(d),"\n  "+e+"\n  "+a):"\n  "+e}var Za=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$a=da.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!$a)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var ab=Object.prototype,K=JSON.stringify,bb=$a.ReactCurrentCache,cb=Za.ReactCurrentDispatcher;function db(a){console.error(a)}function eb(){}
function fb(a,b,c,d,e){if(null!==bb.current&&bb.current!==Ta)throw Error("Currently React only supports one RSC renderer at a time.");wa.current=va;bb.current=Ta;var f=new Set,g=[],k=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:f,pingedTasks:g,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:d||"",identifierCount:1,taintCleanupQueue:[],onError:void 0===c?db:c,onPostpone:void 0===e?eb:e};a=L(b,a,null,!1,f);g.push(a);return b}var M=null;function y(){if(M)return M;var a=xa.getStore();return a?a:null}
function gb(a,b,c){var d=L(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return d.model=c.value,hb(a,d),d.id;case "rejected":return b=N(a,c.reason),O(a,d.id,b),d.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(e){"pending"===c.status&&(c.status="fulfilled",c.value=e)},function(e){"pending"===c.status&&(c.status="rejected",c.reason=e)}))}c.then(function(e){d.model=e;hb(a,d)},function(e){d.status=4;e=N(a,e);O(a,d.id,e);a.abortableTasks.delete(d);
null!==a.destination&&P(a,a.destination)});return d.id}function z(a,b,c){c=K(c);var d=a.nextChunkId++;b="H"+b;b=d.toString(16)+":"+b;a.completedHintChunks.push(b+c+"\n");ib(a)}function jb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function kb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:D,_payload:a,_init:jb}}
function lb(a,b,c,d,e){var f=b.thenableState;b.thenableState=null;Ka=0;G=f;d=d(e,void 0);if("object"===typeof d&&null!==d&&"function"===typeof d.then){e=d;if("fulfilled"===e.status)return e.value;d=kb(d)}e=b.keyPath;f=b.implicitSlot;null!==c?b.keyPath=null===e?c:e+","+c:null===e&&(b.implicitSlot=!0);a=Q(a,b,R,"",d);b.keyPath=e;b.implicitSlot=f;return a}
function mb(a,b,c,d,e,f){if(null!==e&&void 0!==e)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===v?[B,c,d,f]:lb(a,b,d,c,f);if("string"===typeof c)return[B,c,d,f];if("symbol"===typeof c)return c===ya&&null===d?(d=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=Q(a,b,R,"",f.children),b.implicitSlot=d,a):[B,c,d,f];if(null!=c&&"object"===typeof c){if(c.$$typeof===v)return[B,c,d,f];switch(c.$$typeof){case D:var g=
c._init;c=g(c._payload);return mb(a,b,c,d,e,f);case Aa:return lb(a,b,d,c.render,f);case Da:return mb(a,b,c.type,d,e,f)}}throw Error("Unsupported Server Component type: "+Xa(c));}function hb(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return nb(a)}))}
function L(a,b,c,d,e){a.pendingChunks++;var f=a.nextChunkId++;"object"===typeof b&&null!==b&&a.writtenObjects.set(b,f);var g={id:f,status:0,model:b,keyPath:c,implicitSlot:d,ping:function(){return hb(a,g)},toJSON:function(k,h){var p=g.keyPath,t=g.implicitSlot;try{var n=Q(a,g,this,k,h)}catch(aa){if(k=aa===Ga?Ja():aa,h=g.model,h="object"===typeof h&&null!==h&&(h.$$typeof===B||h.$$typeof===D),"object"===typeof k&&null!==k&&"function"===typeof k.then){n=L(a,g.model,g.keyPath,g.implicitSlot,a.abortableTasks);
var C=n.ping;k.then(C,C);n.thenableState=La();g.keyPath=p;g.implicitSlot=t;n=h?"$L"+n.id.toString(16):S(n.id)}else if(g.keyPath=p,g.implicitSlot=t,h)a.pendingChunks++,p=a.nextChunkId++,t=N(a,k),O(a,p,t),n="$L"+p.toString(16);else throw k;}return n},thenableState:null};e.add(g);return g}function S(a){return"$"+a.toString(16)}function ob(a,b,c){a=K(c);return b.toString(16)+":"+a+"\n"}
function pb(a,b,c,d){var e=d.$$async?d.$$id+"#async":d.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===B&&"1"===c?"$L"+g.toString(16):S(g);try{var k=a.bundlerConfig,h=d.$$id;g="";var p=k[h];if(p)g=p.name;else{var t=h.lastIndexOf("#");-1!==t&&(g=h.slice(t+1),p=k[h.slice(0,t)]);if(!p)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var n=!0===d.$$async?[p.id,p.chunks,g,1]:[p.id,p.chunks,
g];a.pendingChunks++;var C=a.nextChunkId++,aa=K(n),Lb=C.toString(16)+":I"+aa+"\n";a.completedImportChunks.push(Lb);f.set(e,C);return b[0]===B&&"1"===c?"$L"+C.toString(16):S(C)}catch(Mb){return a.pendingChunks++,b=a.nextChunkId++,c=N(a,Mb),O(a,b,c),S(b)}}function T(a,b){b=L(a,b,null,!1,a.abortableTasks);qb(a,b);return b.id}var U=!1;
function Q(a,b,c,d,e){b.model=e;if(e===B)return"$";if(null===e)return null;if("object"===typeof e){switch(e.$$typeof){case B:c=a.writtenObjects;d=c.get(e);if(void 0!==d)if(U===e)U=null;else return-1===d?(a=T(a,e),S(a)):S(d);else c.set(e,-1);return mb(a,b,e.type,e.key,e.ref,e.props);case D:return b.thenableState=null,c=e._init,e=c(e._payload),Q(a,b,R,"",e)}if(e.$$typeof===v)return pb(a,c,d,e);c=a.writtenObjects;d=c.get(e);if("function"===typeof e.then){if(void 0!==d)if(U===e)U=null;else return"$@"+
d.toString(16);a=gb(a,b,e);c.set(e,a);return"$@"+a.toString(16)}if(void 0!==d)if(U===e)U=null;else return-1===d?(a=T(a,e),S(a)):S(d);else c.set(e,-1);if(Ua(e))return e;if(e instanceof Map){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b][0],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));return"$Q"+T(a,e).toString(16)}if(e instanceof Set){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));
return"$W"+T(a,e).toString(16)}null===e||"object"!==typeof e?a=null:(a=Fa&&e[Fa]||e["@@iterator"],a="function"===typeof a?a:null);if(a)return a=Array.from(e),a;a=Va(e);if(a!==ab&&(null===a||null!==Va(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return e}if("string"===typeof e){if("Z"===e[e.length-1]&&c[d]instanceof Date)return"$D"+e;if(1024<=e.length)return a.pendingChunks+=2,b=
a.nextChunkId++,c="string"===typeof e?Buffer.byteLength(e,"utf8"):e.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",a.completedRegularChunks.push(c,e),S(b);a="$"===e[0]?"$"+e:e;return a}if("boolean"===typeof e)return e;if("number"===typeof e)return Number.isFinite(e)?0===e&&-Infinity===1/e?"$-0":e:Infinity===e?"$Infinity":-Infinity===e?"$-Infinity":"$NaN";if("undefined"===typeof e)return"$undefined";if("function"===typeof e){if(e.$$typeof===v)return pb(a,c,d,e);if(e.$$typeof===w)return b=a.writtenServerReferences,
c=b.get(e),void 0!==c?a="$F"+c.toString(16):(c=e.$$bound,c={id:e.$$id,bound:c?Promise.resolve(c):null},a=T(a,c),b.set(e,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+J(c,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+
J(c,d));}if("symbol"===typeof e){b=a.writtenSymbols;var f=b.get(e);if(void 0!==f)return S(f);f=e.description;if(Symbol.for(f)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(e.description+") cannot be found among global symbols.")+J(c,d));a.pendingChunks++;c=a.nextChunkId++;d=ob(a,c,"$S"+f);a.completedImportChunks.push(d);b.set(e,c);return S(c)}if("bigint"===typeof e)return"$n"+e.toString(10);throw Error("Type "+typeof e+
" is not supported in Client Component props."+J(c,d));}function N(a,b){var c=M;M=null;try{var d=xa.run(void 0,a.onError,b)}finally{M=c}if(null!=d&&"string"!==typeof d)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof d+'" instead');return d||""}
function rb(a,b){null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}function O(a,b,c){c={digest:c};b=b.toString(16)+":E"+K(c)+"\n";a.completedErrorChunks.push(b)}var R={};
function qb(a,b){if(0===b.status)try{U=b.model;var c=Q(a,b,R,"",b.model);U=c;b.keyPath=null;b.implicitSlot=!1;var d="object"===typeof c&&null!==c?K(c,b.toJSON):K(c),e=b.id.toString(16)+":"+d+"\n";a.completedRegularChunks.push(e);a.abortableTasks.delete(b);b.status=1}catch(h){var f=h===Ga?Ja():h;if("object"===typeof f&&null!==f&&"function"===typeof f.then){var g=b.ping;f.then(g,g);b.thenableState=La()}else{a.abortableTasks.delete(b);b.status=4;var k=N(a,f);O(a,b.id,k)}}finally{}}
function nb(a){var b=cb.current;cb.current=Qa;var c=M;F=M=a;try{var d=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<d.length;e++)qb(a,d[e]);null!==a.destination&&P(a,a.destination)}catch(f){N(a,f),rb(a,f)}finally{cb.current=b,F=null,M=c}}
function P(a,b){l=new Uint8Array(2048);m=0;q=!0;try{for(var c=a.completedImportChunks,d=0;d<c.length;d++)if(a.pendingChunks--,!u(b,c[d])){a.destination=null;d++;break}c.splice(0,d);var e=a.completedHintChunks;for(d=0;d<e.length;d++)if(!u(b,e[d])){a.destination=null;d++;break}e.splice(0,d);var f=a.completedRegularChunks;for(d=0;d<f.length;d++)if(a.pendingChunks--,!u(b,f[d])){a.destination=null;d++;break}f.splice(0,d);var g=a.completedErrorChunks;for(d=0;d<g.length;d++)if(a.pendingChunks--,!u(b,g[d])){a.destination=
null;d++;break}g.splice(0,d)}finally{a.flushScheduled=!1,l&&0<m&&b.write(l.subarray(0,m)),l=null,m=0,q=!0}"function"===typeof b.flush&&b.flush();0===a.pendingChunks&&b.end()}function sb(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return xa.run(a,nb,a)})}function ib(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return P(a,b)})}}
function tb(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{P(a,b)}catch(c){N(a,c),rb(a,c)}}}
function ub(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var d=a.nextChunkId++,e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=N(a,e);O(a,d,f,e);c.forEach(function(g){g.status=3;var k=S(d);g=ob(a,g.id,k);a.completedErrorChunks.push(g)});c.clear()}null!==a.destination&&P(a,a.destination)}catch(g){N(a,g),rb(a,g)}}
function vb(a,b){var c="",d=a[b];if(d)c=d.name;else{var e=b.lastIndexOf("#");-1!==e&&(c=b.slice(e+1),d=a[b.slice(0,e)]);if(!d)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[d.id,d.chunks,c]}var wb=new Map;
function xb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function yb(){}
function zb(a){for(var b=a[1],c=[],d=0;d<b.length;){var e=b[d++];b[d++];var f=wb.get(e);if(void 0===f){f=__webpack_chunk_load__(e);c.push(f);var g=wb.set.bind(wb,e,null);f.then(g,yb);wb.set(e,f)}else null!==f&&c.push(f)}return 4===a.length?0===c.length?xb(a[0]):Promise.all(c).then(function(){return xb(a[0])}):0<c.length?Promise.all(c):null}
function V(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function W(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}W.prototype=Object.create(Promise.prototype);
W.prototype.then=function(a,b){switch(this.status){case "resolved_model":Ab(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Bb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function Cb(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&Bb(c,b)}}function Db(a,b,c,d,e,f){var g=vb(a._bundlerConfig,b);a=zb(g);if(c)c=Promise.all([c,a]).then(function(k){k=k[0];var h=V(g);return h.bind.apply(h,[null].concat(k))});else if(a)c=Promise.resolve(a).then(function(){return V(g)});else return V(g);c.then(Eb(d,e,f),Fb(d));return null}var X=null,Y=null;
function Ab(a){var b=X,c=Y;X=a;Y=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=c}}function Gb(a,b){a._closed=!0;a._closedReason=b;a._chunks.forEach(function(c){"pending"===c.status&&Cb(c,b)})}
function Z(a,b){var c=a._chunks,d=c.get(b);d||(d=a._formData.get(a._prefix+b),d=null!=d?new W("resolved_model",d,null,a):a._closed?new W("rejected",null,a._closedReason,a):new W("pending",null,null,a),c.set(b,d));return d}function Eb(a,b,c){if(Y){var d=Y;d.deps++}else d=Y={deps:1,value:null};return function(e){b[c]=e;d.deps--;0===d.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=d.value,null!==e&&Bb(e,d.value))}}function Fb(a){return function(b){return Cb(a,b)}}
function Hb(a,b){a=Z(a,b);"resolved_model"===a.status&&Ab(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Ib(a,b,c,d){if("$"===d[0])switch(d[1]){case "$":return d.slice(1);case "@":return b=parseInt(d.slice(2),16),Z(a,b);case "S":return Symbol.for(d.slice(2));case "F":return d=parseInt(d.slice(2),16),d=Hb(a,d),Db(a,d.id,d.bound,X,b,c);case "Q":return b=parseInt(d.slice(2),16),a=Hb(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=Hb(a,b),new Set(a);case "K":b=d.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=Z(a,d);switch(a.status){case "resolved_model":Ab(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=X,a.then(Eb(d,b,c),Fb(d)),null;default:throw a.reason;}}return d}
function Jb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,d=new Map,e={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:d,_fromJSON:function(f,g){return"string"===typeof g?Ib(e,this,f,g):g},_closed:!1,_closedReason:null};return e}
function Kb(a,b,c){a._formData.append(b,c);var d=a._prefix;if(b.startsWith(d)&&(a=a._chunks,b=+b.slice(d.length),(b=a.get(b))&&"pending"===b.status&&(d=b.value,a=b.reason,b.status="resolved_model",b.value=c,null!==d)))switch(Ab(b),b.status){case "fulfilled":Bb(d,b.value);break;case "pending":case "blocked":b.value=d;b.reason=a;break;case "rejected":a&&Bb(a,b.reason)}}function Nb(a){Gb(a,Error("Connection closed."))}
function Ob(a,b,c){var d=vb(a,b);a=zb(d);return c?Promise.all([c,a]).then(function(e){e=e[0];var f=V(d);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return V(d)}):Promise.resolve(V(d))}function Pb(a,b,c){a=Jb(b,c,a);Nb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}function Qb(a,b){return function(){return tb(b,a)}}function Rb(a,b){return function(){a.destination=null;ub(a,Error(b))}}
exports.createClientModuleProxy=function(a){a=x({},a,!1);return new Proxy(a,na)};exports.decodeAction=function(a,b){var c=new FormData,d=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Pb(a,b,e),d=Ob(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),d=Ob(b,e,null)):c.append(f,e)});return null===d?null:d.then(function(e){return e.bind(null,c)})};
exports.decodeFormState=function(a,b,c){var d=b.get("$ACTION_KEY");if("string"!==typeof d)return Promise.resolve(null);var e=null;b.forEach(function(g,k){k.startsWith("$ACTION_REF_")&&(g="$ACTION_"+k.slice(12)+":",e=Pb(b,c,g))});if(null===e)return Promise.resolve(null);var f=e.id;return Promise.resolve(e.bound).then(function(g){return null===g?null:[a,d,f,g.length-1]})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Jb(b,"",a);b=Z(a,0);Nb(a);return b};
exports.decodeReplyFromBusboy=function(a,b){var c=Jb(b,""),d=0,e=[];a.on("field",function(f,g){0<d?e.push(f,g):Kb(c,f,g)});a.on("file",function(f,g,k){var h=k.filename,p=k.mimeType;if("base64"===k.encoding.toLowerCase())throw Error("React doesn't accept base64 encoded file uploads because we don't expect form data passed from a browser to ever encode data that way. If that's the wrong assumption, we can easily fix it.");d++;var t=[];g.on("data",function(n){t.push(n)});g.on("end",function(){var n=
new Blob(t,{type:p});c._formData.append(f,n,h);d--;if(0===d){for(n=0;n<e.length;n+=2)Kb(c,e[n],e[n+1]);e.length=0}})});a.on("finish",function(){Nb(c)});a.on("error",function(f){Gb(c,f)});return Z(c,0)};exports.registerClientReference=function(a,b,c){return x(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:w},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:ja,configurable:!0}})};
exports.renderToPipeableStream=function(a,b,c){var d=fb(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0),e=!1;sb(d);return{pipe:function(f){if(e)throw Error("React currently only supports piping to one writable stream.");e=!0;tb(d,f);f.on("drain",Qb(f,d));f.on("error",Rb(d,"The destination stream errored while writing data."));f.on("close",Rb(d,"The destination stream closed early."));return f},abort:function(f){ub(d,f)}}};

//# sourceMappingURL=react-server-dom-webpack-server.node.production.min.js.map
