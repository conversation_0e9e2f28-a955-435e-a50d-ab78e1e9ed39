{"version": 3, "sources": ["../../src/client/link.tsx"], "names": ["React", "resolveHref", "isLocalURL", "formatUrl", "isAbsoluteUrl", "addLocale", "RouterContext", "AppRouterContext", "useIntersection", "getDomainLocale", "addBasePath", "PrefetchKind", "prefetched", "Set", "prefetch", "router", "href", "as", "options", "appOptions", "isAppRouter", "window", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "doPrefetch", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "startTransition", "formatStringOrUrl", "urlObjOrString", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "pagesRouter", "useContext", "appRouter", "prefetchEnabled", "appPrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "hasWarned", "useRef", "current", "console", "warn", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "resolvedAs", "previousHref", "previousAs", "child", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "rootMargin", "setRef", "useCallback", "el", "useEffect", "kind", "childProps", "defaultPrevented", "priority", "__NEXT_LINK_NO_TOUCH_START", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "locales", "domainLocales", "defaultLocale", "cloneElement"], "mappings": "AAAA;;AAOA,OAAOA,WAAW,QAAO;AAEzB,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,UAAU,QAAQ,0CAAyC;AACpE,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,aAAa,QAAQ,sBAAqB;AACnD,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,aAAa,QAAQ,8CAA6C;AAC3E,SAASC,gBAAgB,QAAQ,kDAAiD;AAKlF,SAASC,eAAe,QAAQ,qBAAoB;AACpD,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,YAAY,QAAQ,mDAAkD;AAkG/E,MAAMC,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVC,OAAwB,EACxBC,UAAoC,EACpCC,WAAoB;IAEpB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,gJAAgJ;IAChJ,IAAI,CAACD,eAAe,CAAClB,WAAWc,OAAO;QACrC;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQI,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAOL,QAAQK,MAAM,KAAK,cACtBL,QAAQK,MAAM,GAEhB,YAAYR,SACVA,OAAOQ,MAAM,GACbC;QAEN,MAAMC,gBAAgBT,OAAO,MAAMC,KAAK,MAAMM;QAE9C,kEAAkE;QAClE,IAAIX,WAAWc,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/Bb,WAAWe,GAAG,CAACF;IACjB;IAEA,MAAMG,aAAa;QACjB,IAAIR,aAAa;YACf,sDAAsD;YACtD,wFAAwF;YACxF,OAAO,AAACL,OAA6BD,QAAQ,CAACE,MAAMG;QACtD,OAAO;YACL,OAAO,AAACJ,OAAsBD,QAAQ,CAACE,MAAMC,IAAIC;QACnD;IACF;IAEA,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDU,aAAaC,KAAK,CAAC,CAACC;QAClB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBhC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACV+B,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB3B,MAAuB,EACvBH,WAAqB;IAErB,MAAM,EAAE+B,QAAQ,EAAE,GAAGJ,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMe,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACED,oBACClB,CAAAA,gBAAgBa,MACf,gJAAgJ;IAC/I,CAAC3B,eAAe,CAAClB,WAAWc,KAAK,GACpC;QACA,8CAA8C;QAC9C;IACF;IAEA+B,EAAEO,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBnC,QAAQ;YAC9BA,MAAM,CAACiC,UAAU,YAAY,OAAO,CAAChC,MAAMC,IAAI;gBAC7CgC;gBACA1B;gBACA2B,QAAQM;YACV;QACF,OAAO;YACLzC,MAAM,CAACiC,UAAU,YAAY,OAAO,CAAC/B,MAAMD,MAAM;gBAC/CkC,QAAQM;YACV;QACF;IACF;IAEA,IAAIpC,aAAa;QACfpB,MAAMyD,eAAe,CAACF;IACxB,OAAO;QACLA;IACF;AACF;AAOA,SAASG,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOxD,UAAUwD;AACnB;AAEA;;;;;;;CAOC,GACD,MAAMC,qBAAO5D,MAAM6D,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJjD,MAAMkD,QAAQ,EACdjD,IAAIkD,MAAM,EACVF,UAAUG,YAAY,EACtBtD,UAAUuD,eAAe,IAAI,EAC7BC,QAAQ,EACRtB,OAAO,EACPC,OAAO,EACPC,MAAM,EACN3B,MAAM,EACNgD,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,KAACa;sBAAGb;;IACjB;IAEA,MAAMc,cAAc/E,MAAMgF,UAAU,CAAC1E;IACrC,MAAM2E,YAAYjF,MAAMgF,UAAU,CAACzE;IACnC,MAAMQ,SAASgE,sBAAAA,cAAeE;IAE9B,0DAA0D;IAC1D,MAAM7D,cAAc,CAAC2D;IAErB,MAAMG,kBAAkBb,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMc,kBACJd,iBAAiB,OAAO1D,aAAayE,IAAI,GAAGzE,aAAa0E,IAAI;IAE/D,IAAItD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAASqD,gBAAgBC,IAIxB;YACC,OAAO,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOtE,WAAW,cACf,qEACA,EAAC;QAEX;QAEA,sCAAsC;QACtC,MAAMuE,qBAAsD;YAC1D5E,MAAM;QACR;QACA,MAAM6E,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACE1B,KAAK,CAAC0B,IAAI,IAAI,QACb,OAAO1B,KAAK,CAAC0B,IAAI,KAAK,YAAY,OAAO1B,KAAK,CAAC0B,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ5B,KAAK,CAAC0B,IAAI,KAAK,OAAO,SAAS,OAAO1B,KAAK,CAAC0B,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DjF,IAAI;YACJ+B,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTqB,UAAU;YACVxD,UAAU;YACVS,QAAQ;YACRgD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMuB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOrC,KAAK,CAAC0B,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAI1B,KAAK,CAAC0B,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAI1B,KAAK,CAAC0B,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;QAEA,4FAA4F;QAC5F,sDAAsD;QACtD,MAAMY,YAAYrG,MAAMsG,MAAM,CAAC;QAC/B,IAAIvC,MAAMjD,QAAQ,IAAI,CAACuF,UAAUE,OAAO,IAAI,CAACnF,aAAa;YACxDiF,UAAUE,OAAO,GAAG;YACpBC,QAAQC,IAAI,CACV;QAEJ;IACF;IAEA,IAAI1E,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIb,eAAe,CAAC+C,QAAQ;YAC1B,IAAInD;YACJ,IAAI,OAAOkD,aAAa,UAAU;gBAChClD,OAAOkD;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAASwC,QAAQ,KAAK,UAC7B;gBACA1F,OAAOkD,SAASwC,QAAQ;YAC1B;YAEA,IAAI1F,MAAM;gBACR,MAAM2F,oBAAoB3F,KACvB4F,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,IAAInB,MACR,AAAC,mBAAiBxE,OAAK;gBAE3B;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGjB,MAAMiH,OAAO,CAAC;QACjC,IAAI,CAAClC,aAAa;YAChB,MAAMmC,eAAexD,kBAAkBQ;YACvC,OAAO;gBACLlD,MAAMkG;gBACNjG,IAAIkD,SAAST,kBAAkBS,UAAU+C;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAGlH,YACjC8E,aACAb,UACA;QAGF,OAAO;YACLlD,MAAMkG;YACNjG,IAAIkD,SACAlE,YAAY8E,aAAaZ,UACzBgD,cAAcD;QACpB;IACF,GAAG;QAACnC;QAAab;QAAUC;KAAO;IAElC,MAAMiD,eAAepH,MAAMsG,MAAM,CAAStF;IAC1C,MAAMqG,aAAarH,MAAMsG,MAAM,CAASrF;IAExC,oFAAoF;IACpF,IAAIqG;IACJ,IAAI1C,gBAAgB;QAClB,IAAI7C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIsC,SAAS;gBACXiC,QAAQC,IAAI,CACV,AAAC,oDAAoDvC,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpB+B,QAAQC,IAAI,CACV,AAAC,yDAAyDvC,WAAS;YAEvE;YACA,IAAI;gBACFoD,QAAQtH,MAAMuH,QAAQ,CAACC,IAAI,CAACvD;YAC9B,EAAE,OAAOnC,KAAK;gBACZ,IAAI,CAACmC,UAAU;oBACb,MAAM,IAAIuB,MACR,AAAC,uDAAuDtB,WAAS;gBAErE;gBACA,MAAM,IAAIsB,MACR,AAAC,6DAA6DtB,WAAS,8FACpE,CAAA,OAAO7C,WAAW,cACf,sEACA,EAAC;YAEX;QACF,OAAO;YACLiG,QAAQtH,MAAMuH,QAAQ,CAACC,IAAI,CAACvD;QAC9B;IACF,OAAO;QACL,IAAIlC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACgC,4BAAD,AAACA,SAAkBwD,IAAI,MAAK,KAAK;gBACnC,MAAM,IAAIjC,MACR;YAEJ;QACF;IACF;IAEA,MAAMkC,WAAgB9C,iBAClB0C,SAAS,OAAOA,UAAU,YAAYA,MAAMK,GAAG,GAC/C3D;IAEJ,MAAM,CAAC4D,oBAAoBC,WAAWC,aAAa,GAAGtH,gBAAgB;QACpEuH,YAAY;IACd;IAEA,MAAMC,SAAShI,MAAMiI,WAAW,CAC9B,CAACC;QACC,4EAA4E;QAC5E,IAAIb,WAAWd,OAAO,KAAKtF,MAAMmG,aAAab,OAAO,KAAKvF,MAAM;YAC9D8G;YACAT,WAAWd,OAAO,GAAGtF;YACrBmG,aAAab,OAAO,GAAGvF;QACzB;QAEA4G,mBAAmBM;QACnB,IAAIR,UAAU;YACZ,IAAI,OAAOA,aAAa,YAAYA,SAASQ;iBACxC,IAAI,OAAOR,aAAa,UAAU;gBACrCA,SAASnB,OAAO,GAAG2B;YACrB;QACF;IACF,GACA;QAACjH;QAAIyG;QAAU1G;QAAM8G;QAAcF;KAAmB;IAGxD,2DAA2D;IAC3D5H,MAAMmI,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAIpG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAAClB,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAAC8G,aAAa,CAAC3C,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpBpE,SACEC,QACAC,MACAC,IACA;YAAEM;QAAO,GACT;YACE6G,MAAMjD;QACR,GACA/D;IAEJ,GAAG;QACDH;QACAD;QACA6G;QACAtG;QACA2D;QACAH,+BAAAA,YAAaxD,MAAM;QACnBR;QACAK;QACA+D;KACD;IAED,MAAMkD,aAMF;QACFV,KAAKK;QACLzD,SAAQxB,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,IAAIyC,MACP;gBAEL;YACF;YAEA,IAAI,CAACZ,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQxB;YACV;YAEA,IACE6B,kBACA0C,MAAMvD,KAAK,IACX,OAAOuD,MAAMvD,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACA+C,MAAMvD,KAAK,CAACQ,OAAO,CAACxB;YACtB;YAEA,IAAI,CAAChC,QAAQ;gBACX;YACF;YAEA,IAAIgC,EAAEuF,gBAAgB,EAAE;gBACtB;YACF;YAEAxF,YACEC,GACAhC,QACAC,MACAC,IACA+B,SACAC,SACAC,QACA3B,QACAH;QAEJ;QACAoD,cAAazB,CAAC;YACZ,IAAI,CAAC6B,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB1B;YACnB;YAEA,IACE6B,kBACA0C,MAAMvD,KAAK,IACX,OAAOuD,MAAMvD,KAAK,CAACS,YAAY,KAAK,YACpC;gBACA8C,MAAMvD,KAAK,CAACS,YAAY,CAACzB;YAC3B;YAEA,IAAI,CAAChC,QAAQ;gBACX;YACF;YAEA,IACE,AAAC,CAAA,CAACmE,mBAAmBnD,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAY,KAC1Db,aACA;gBACA;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEM;gBACAgH,UAAU;gBACV,gGAAgG;gBAChGjH,uBAAuB;YACzB,GACA;gBACE8G,MAAMjD;YACR,GACA/D;QAEJ;QACAsD,cAAc3C,QAAQC,GAAG,CAACwG,0BAA0B,GAChDhH,YACA,SAASkD,aAAa3B,CAAC;YACrB,IAAI,CAAC6B,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB5B;YACnB;YAEA,IACE6B,kBACA0C,MAAMvD,KAAK,IACX,OAAOuD,MAAMvD,KAAK,CAACW,YAAY,KAAK,YACpC;gBACA4C,MAAMvD,KAAK,CAACW,YAAY,CAAC3B;YAC3B;YAEA,IAAI,CAAChC,QAAQ;gBACX;YACF;YAEA,IAAI,CAACmE,mBAAmB9D,aAAa;gBACnC;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEM;gBACAgH,UAAU;gBACV,gGAAgG;gBAChGjH,uBAAuB;YACzB,GACA;gBACE8G,MAAMjD;YACR,GACA/D;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAIhB,cAAca,KAAK;QACrBoH,WAAWrH,IAAI,GAAGC;IACpB,OAAO,IACL,CAAC2D,kBACDN,YACCgD,MAAMG,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUH,MAAMvD,KAAK,AAAD,GAC7C;QACA,MAAM0E,YACJ,OAAOlH,WAAW,cAAcA,SAASwD,+BAAAA,YAAaxD,MAAM;QAE9D,uEAAuE;QACvE,uEAAuE;QACvE,MAAMmH,eACJ3D,CAAAA,+BAAAA,YAAa4D,cAAc,KAC3BlI,gBACEQ,IACAwH,WACA1D,+BAAAA,YAAa6D,OAAO,EACpB7D,+BAAAA,YAAa8D,aAAa;QAG9BR,WAAWrH,IAAI,GACb0H,gBACAhI,YAAYL,UAAUY,IAAIwH,WAAW1D,+BAAAA,YAAa+D,aAAa;IACnE;IAEA,OAAOlE,+BACL5E,MAAM+I,YAAY,CAACzB,OAAOe,4BAE1B,KAACvD;QAAG,GAAGD,SAAS;QAAG,GAAGwD,UAAU;kBAC7BpE;;AAGP;AAGF,eAAeL,KAAI"}