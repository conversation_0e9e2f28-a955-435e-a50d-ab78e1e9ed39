{"version": 3, "sources": ["../../../src/server/async-storage/static-generation-async-storage-wrapper.ts"], "names": ["createPrerenderState", "StaticGenerationAsyncStorageWrapper", "wrap", "storage", "urlPathname", "renderOpts", "requestEndedState", "callback", "isStaticGeneration", "supportsDynamicResponse", "isDraftMode", "isServerAction", "prerenderState", "experimental", "ppr", "isDebugPPRSkeleton", "store", "pagePath", "originalPathname", "incrementalCache", "globalThis", "__incrementalCache", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "run"], "mappings": "AAMA,SAASA,oBAAoB,QAAQ,4CAA2C;AA0ChF,OAAO,MAAMC,sCAGT;IACFC,MACEC,OAAiD,EACjD,EAAEC,WAAW,EAAEC,UAAU,EAAEC,iBAAiB,EAA2B,EACvEC,QAAkD;QAElD;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMC,qBACJ,CAACH,WAAWI,uBAAuB,IACnC,CAACJ,WAAWK,WAAW,IACvB,CAACL,WAAWM,cAAc;QAE5B,MAAMC,iBACJJ,sBAAsBH,WAAWQ,YAAY,CAACC,GAAG,GAC7Cd,qBAAqBK,WAAWU,kBAAkB,IAClD;QAEN,MAAMC,QAA+B;YACnCR;YACAJ;YACAa,UAAUZ,WAAWa,gBAAgB;YACrCC,kBACE,qEAAqE;YACrE,mDAAmD;YACnDd,WAAWc,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;YACvEC,cAAcjB,WAAWiB,YAAY;YACrCC,gBAAgBlB,WAAWmB,UAAU;YACrCC,YAAYpB,WAAWoB,UAAU;YACjCC,sBAAsBrB,WAAWqB,oBAAoB;YAErDhB,aAAaL,WAAWK,WAAW;YAEnCE;YACAN;QACF;QAEA,sFAAsF;QACtFD,WAAWW,KAAK,GAAGA;QAEnB,OAAOb,QAAQwB,GAAG,CAACX,OAAOT,UAAUS;IACtC;AACF,EAAC"}