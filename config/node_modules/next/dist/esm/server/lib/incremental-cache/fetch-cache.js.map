{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "names": ["L<PERSON><PERSON><PERSON>", "CACHE_ONE_YEAR", "NEXT_CACHE_SOFT_TAGS_HEADER", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "DEBUG", "Boolean", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "fetchRetryWithTimeout", "url", "init", "retryIndex", "controller", "AbortController", "timeout", "setTimeout", "abort", "fetch", "signal", "catch", "err", "console", "log", "finally", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "hasMatchingTags", "arr1", "arr2", "length", "set1", "Set", "set2", "size", "tag", "has", "isAvailable", "ctx", "_requestHeaders", "SUSPENSE_CACHE_URL", "constructor", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "scProto", "SUSPENSE_CACHE_PROTO", "cacheEndpoint", "maxMemoryCacheSize", "max", "value", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "resetRequestCache", "reset", "revalidateTag", "args", "tags", "Date", "now", "i", "Math", "ceil", "currentTags", "slice", "res", "map", "encodeURIComponent", "join", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "warn", "key", "softTags", "kindHint", "fetchIdx", "fetchUrl", "hasFetchKindAndMatchingTags", "start", "fetchParams", "fetchType", "error", "text", "cached", "json", "includes", "push", "cacheState", "age", "lastModified", "Object", "keys", "set", "fetchCache", "revalidate", "toString", "undefined"], "mappings": "AAGA,OAAOA,cAAc,+BAA8B;AACnD,SACEC,cAAc,EACdC,2BAA2B,QACtB,yBAAwB;AAE/B,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEnC,MAAMC,QAAQC,QAAQC,QAAQC,GAAG,CAACC,wBAAwB;AAE1D,eAAeC,sBACbC,GAAgC,EAChCC,IAAiC,EACjCC,aAAa,CAAC;IAEd,MAAMC,aAAa,IAAIC;IACvB,MAAMC,UAAUC,WAAW;QACzBH,WAAWI,KAAK;IAClB,GAAG;IAEH,OAAOC,MAAMR,KAAK;QAChB,GAAIC,QAAQ,CAAC,CAAC;QACdQ,QAAQN,WAAWM,MAAM;IAC3B,GACGC,KAAK,CAAC,CAACC;QACN,IAAIT,eAAe,GAAG;YACpB,MAAMS;QACR,OAAO;YACL,IAAIjB,OAAO;gBACTkB,QAAQC,GAAG,CAAC,CAAC,iBAAiB,EAAEb,IAAI,OAAO,EAAEE,WAAW,CAAC;YAC3D;YACA,OAAOH,sBAAsBC,KAAKC,MAAMC,aAAa;QACvD;IACF,GACCY,OAAO,CAAC;QACPC,aAAaV;IACf;AACJ;AAEA,eAAe,MAAMW;IAIXC,gBAAgBC,IAAc,EAAEC,IAAc,EAAE;QACtD,IAAID,KAAKE,MAAM,KAAKD,KAAKC,MAAM,EAAE,OAAO;QAExC,MAAMC,OAAO,IAAIC,IAAIJ;QACrB,MAAMK,OAAO,IAAID,IAAIH;QAErB,IAAIE,KAAKG,IAAI,KAAKD,KAAKC,IAAI,EAAE,OAAO;QAEpC,KAAK,IAAIC,OAAOJ,KAAM;YACpB,IAAI,CAACE,KAAKG,GAAG,CAACD,MAAM,OAAO;QAC7B;QAEA,OAAO;IACT;IAEA,OAAOE,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIjC,QAAQC,GAAG,CAACiC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYH,GAAwB,CAAE;QACpC,IAAI,CAACI,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAI3C,wBAAwBuC,IAAIC,eAAe,EAAE;YAC/C,MAAMI,aAAaC,KAAKC,KAAK,CAC3BP,IAAIC,eAAe,CAACxC,qBAAqB;YAE3C,IAAK,MAAM+C,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOR,IAAIC,eAAe,CAACxC,qBAAqB;QAClD;QACA,MAAMgD,SACJT,IAAIC,eAAe,CAAC,mBAAmB,IAAIjC,QAAQC,GAAG,CAACiC,kBAAkB;QAE3E,MAAMQ,aACJV,IAAIC,eAAe,CAAC,uBAAuB,IAC3CjC,QAAQC,GAAG,CAAC0C,uBAAuB;QAErC,IAAI3C,QAAQC,GAAG,CAAC2C,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CACV,gBACD,GAAG,CAAC,OAAO,EAAEpC,QAAQC,GAAG,CAAC2C,yBAAyB,CAAC,CAAC;QACvD;QAEA,IAAIH,QAAQ;YACV,MAAMI,UAAU7C,QAAQC,GAAG,CAAC6C,oBAAoB,IAAI;YACpD,IAAI,CAACC,aAAa,GAAG,CAAC,EAAEF,QAAQ,GAAG,EAAEJ,OAAO,EAAEC,cAAc,GAAG,CAAC;YAChE,IAAI5C,OAAO;gBACTkB,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAAC8B,aAAa;YACxD;QACF,OAAO,IAAIjD,OAAO;YAChBkB,QAAQC,GAAG,CAAC;QACd;QAEA,IAAIe,IAAIgB,kBAAkB,EAAE;YAC1B,IAAI,CAACzD,aAAa;gBAChB,IAAIO,OAAO;oBACTkB,QAAQC,GAAG,CAAC;gBACd;gBAEA1B,cAAc,IAAIJ,SAAS;oBACzB8D,KAAKjB,IAAIgB,kBAAkB;oBAC3BxB,QAAO,EAAE0B,KAAK,EAAE;4BAeXZ;wBAdH,IAAI,CAACY,OAAO;4BACV,OAAO;wBACT,OAAO,IAAIA,MAAMC,IAAI,KAAK,YAAY;4BACpC,OAAOb,KAAKc,SAAS,CAACF,MAAMG,KAAK,EAAE7B,MAAM;wBAC3C,OAAO,IAAI0B,MAAMC,IAAI,KAAK,SAAS;4BACjC,MAAM,IAAIG,MAAM;wBAClB,OAAO,IAAIJ,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOb,KAAKc,SAAS,CAACF,MAAMK,IAAI,IAAI,IAAI/B,MAAM;wBAChD,OAAO,IAAI0B,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOD,MAAMM,IAAI,CAAChC,MAAM;wBAC1B;wBACA,wCAAwC;wBACxC,OACE0B,MAAMO,IAAI,CAACjC,MAAM,GAChBc,CAAAA,EAAAA,kBAAAA,KAAKc,SAAS,CAACF,MAAMC,IAAI,KAAK,UAAUD,MAAMQ,QAAQ,sBAAtDpB,gBACGd,MAAM,KAAI,CAAA;oBAElB;gBACF;YACF;QACF,OAAO;YACL,IAAI1B,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEO0C,oBAA0B;QAC/BpE,+BAAAA,YAAaqE,KAAK;IACpB;IAEA,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAC3C,IAAIjE,OAAO;YACTkB,QAAQC,GAAG,CAAC,iBAAiB8C;QAC/B;QAEA,IAAI,CAACA,KAAKvC,MAAM,EAAE;QAElB,IAAIwC,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC,iBAAiB3B;YAC/B;YACA;QACF;QAEA,IAAK,IAAI4E,IAAI,GAAGA,IAAIC,KAAKC,IAAI,CAACL,KAAKvC,MAAM,GAAG,KAAK0C,IAAK;YACpD,MAAMG,cAAcN,KAAKO,KAAK,CAACJ,IAAI,IAAIA,IAAI,KAAK;YAChD,IAAI;gBACF,MAAMK,MAAM,MAAMpE,sBAChB,CAAC,EAAE,IAAI,CAAC4C,aAAa,CAAC,mCAAmC,EAAEsB,YACxDG,GAAG,CAAC,CAAC3C,MAAQ4C,mBAAmB5C,MAChC6C,IAAI,CAAC,KAAK,CAAC,EACd;oBACEC,QAAQ;oBACRvC,SAAS,IAAI,CAACA,OAAO;oBACrB,sCAAsC;oBACtCwC,MAAM;wBAAEC,UAAU;oBAAK;gBACzB;gBAGF,IAAIN,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAInC,OAAO,CAAC4C,GAAG,CAAC,kBAAkB;oBACrD1F,mBAAmB0E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACX,MAAM,IAAI5B,MAAM,CAAC,2BAA2B,EAAEiB,IAAIO,MAAM,CAAC,CAAC,CAAC;gBAC7D;YACF,EAAE,OAAO/D,KAAK;gBACZC,QAAQmE,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAEd,aAAatD;YACxD;QACF;IACF;IAEA,MAAaiE,IAAI,GAAGlB,IAAqC,EAAE;YAqBvDP;QApBF,MAAM,CAAC6B,KAAKpD,MAAM,CAAC,CAAC,CAAC,GAAG8B;QACxB,MAAM,EAAEC,IAAI,EAAEsB,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGxD;QAEzD,IAAIsD,aAAa,SAAS;YACxB,OAAO;QACT;QAEA,IAAItB,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,qDAAqD;QACrD,qDAAqD;QACrD,yBAAyB;QACzB,IAAIsC,OAAOhE,+BAAAA,YAAayF,GAAG,CAACI;QAE5B,MAAMK,8BACJlC,CAAAA,yBAAAA,cAAAA,KAAML,KAAK,qBAAXK,YAAaJ,IAAI,MAAK,WACtB,IAAI,CAAC9B,eAAe,CAAC0C,QAAQ,EAAE,EAAER,KAAKL,KAAK,CAACa,IAAI,IAAI,EAAE;QAExD,8DAA8D;QAC9D,gDAAgD;QAChD,IAAI,IAAI,CAAChB,aAAa,IAAK,CAAA,CAACQ,QAAQ,CAACkC,2BAA0B,GAAI;YACjE,IAAI;gBACF,MAAMC,QAAQ1B,KAAKC,GAAG;gBACtB,MAAM0B,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ,UAAUA;oBACVD;gBACF;gBACA,MAAMhB,MAAM,MAAM3D,MAChB,CAAC,EAAE,IAAI,CAACmC,aAAa,CAAC,mBAAmB,EAAEqC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRvC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACxC,uBAAuB,EAAE4F;wBAC1B,CAAChG,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMW,IAAI,CAAC,SAAQ;wBACxC,CAACrF,4BAA4B,EAAEgG,CAAAA,4BAAAA,SAAUX,IAAI,CAAC,SAAQ;oBACxD;oBACAE,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAInC,OAAO,CAAC4C,GAAG,CAAC,kBAAkB;oBACrD1F,mBAAmB0E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAIR,IAAIO,MAAM,KAAK,KAAK;oBACtB,IAAIhF,OAAO;wBACTkB,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAEmE,IAAI,YAAY,EAC1CpB,KAAKC,GAAG,KAAKyB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACnB,IAAIW,EAAE,EAAE;oBACXlE,QAAQ6E,KAAK,CAAC,MAAMtB,IAAIuB,IAAI;oBAC5B,MAAM,IAAIxC,MAAM,CAAC,4BAA4B,EAAEiB,IAAIO,MAAM,CAAC,CAAC;gBAC7D;gBAEA,MAAMiB,SAAgC,MAAMxB,IAAIyB,IAAI;gBAEpD,IAAI,CAACD,UAAUA,OAAO5C,IAAI,KAAK,SAAS;oBACtCrD,SAASkB,QAAQC,GAAG,CAAC;wBAAE8E;oBAAO;oBAC9B,MAAM,IAAIzC,MAAM;gBAClB;gBAEA,oEAAoE;gBACpE,IAAIyC,OAAO5C,IAAI,KAAK,SAAS;oBAC3B4C,OAAOhC,IAAI,KAAK,EAAE;oBAClB,KAAK,MAAMlC,OAAOkC,QAAQ,EAAE,CAAE;wBAC5B,IAAI,CAACgC,OAAOhC,IAAI,CAACkC,QAAQ,CAACpE,MAAM;4BAC9BkE,OAAOhC,IAAI,CAACmC,IAAI,CAACrE;wBACnB;oBACF;gBACF;gBAEA,MAAMsE,aAAa5B,IAAInC,OAAO,CAAC4C,GAAG,CAACtF;gBACnC,MAAM0G,MAAM7B,IAAInC,OAAO,CAAC4C,GAAG,CAAC;gBAE5BzB,OAAO;oBACLL,OAAO6C;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCM,cACEF,eAAe,UACXnC,KAAKC,GAAG,KAAK7E,iBACb4E,KAAKC,GAAG,KAAKgB,SAASmB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAItG,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAEmE,IAAI,YAAY,EAC3CpB,KAAKC,GAAG,KAAKyB,MACd,UAAU,EACTY,OAAOC,IAAI,CAACR,QAAQvE,MAAM,CAC3B,eAAe,EAAE2E,WAAW,OAAO,EAAEpC,wBAAAA,KAAMW,IAAI,CAC9C,KACA,WAAW,EAAEW,4BAAAA,SAAUX,IAAI,CAAC,KAAK,CAAC;gBAExC;gBAEA,IAAInB,MAAM;oBACRhE,+BAAAA,YAAaiH,GAAG,CAACpB,KAAK7B;gBACxB;YACF,EAAE,OAAOxC,KAAK;gBACZ,sCAAsC;gBACtC,IAAIjB,OAAO;oBACTkB,QAAQ6E,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE9E;gBAClD;YACF;QACF;QAEA,OAAOwC,QAAQ;IACjB;IAEA,MAAaiD,IAAI,GAAG1C,IAAqC,EAAE;QACzD,MAAM,CAACsB,KAAK7B,MAAMvB,IAAI,GAAG8B;QAEzB,MAAM,EAAE2C,UAAU,EAAElB,QAAQ,EAAEC,QAAQ,EAAEzB,IAAI,EAAE,GAAG/B;QACjD,IAAI,CAACyE,YAAY;QAEjB,IAAIzC,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA1B,+BAAAA,YAAaiH,GAAG,CAACpB,KAAK;YACpBlC,OAAOK;YACP8C,cAAcrC,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAAClB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAM2C,QAAQ1B,KAAKC,GAAG;gBACtB,IAAIV,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACnB,OAAO,CAACzC,wBAAwB,GAAG4D,KAAKmD,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAACvE,OAAO,CAACzC,wBAAwB,IACtC4D,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACnB,OAAO,CAACvC,2BAA2B,GACtC0D,KAAKA,IAAI,CAACnB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMoB,OAAOlB,KAAKc,SAAS,CAAC;oBAC1B,GAAGG,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBQ,MAAM6C;gBACR;gBAEA,IAAI9G,OAAO;oBACTkB,QAAQC,GAAG,CAAC,aAAamE;gBAC3B;gBACA,MAAMO,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ;oBACAD;gBACF;gBACA,MAAMhB,MAAM,MAAM3D,MAChB,CAAC,EAAE,IAAI,CAACmC,aAAa,CAAC,mBAAmB,EAAEqC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRvC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACxC,uBAAuB,EAAE4F,YAAY;wBACtC,CAAChG,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMW,IAAI,CAAC,SAAQ;oBAC1C;oBACAlB,MAAMA;oBACNoB,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAInC,OAAO,CAAC4C,GAAG,CAAC,kBAAkB;oBACrD1F,mBAAmB0E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACXpF,SAASkB,QAAQC,GAAG,CAAC,MAAMsD,IAAIuB,IAAI;oBACnC,MAAM,IAAIxC,MAAM,CAAC,iBAAiB,EAAEiB,IAAIO,MAAM,CAAC,CAAC;gBAClD;gBAEA,IAAIhF,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAEmE,IAAI,YAAY,EACrDpB,KAAKC,GAAG,KAAKyB,MACd,UAAU,EAAElC,KAAKhC,MAAM,CAAC,CAAC;gBAE9B;YACF,EAAE,OAAOT,KAAK;gBACZ,+BAA+B;gBAC/B,IAAIjB,OAAO;oBACTkB,QAAQ6E,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE9E;gBAChD;YACF;QACF;QACA;IACF;AACF"}