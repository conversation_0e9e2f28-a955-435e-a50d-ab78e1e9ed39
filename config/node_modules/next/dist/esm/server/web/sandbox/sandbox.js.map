{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["getModuleContext", "requestStore", "requestToBodyStream", "NEXT_RSC_UNION_QUERY", "ErrorSource", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "getRuntimeContext", "runtime", "evaluateInContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCacheShared", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "run", "runWithTaggedErrors", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "request", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "toString", "headers", "Headers", "key", "value", "Object", "entries", "set", "headerName", "response", "Error", "finalize"], "mappings": "AAGA,SAASA,gBAAgB,EAAEC,YAAY,QAAQ,YAAW;AAC1D,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,oBAAoB,QAAQ,gDAA+C;AAEpF,OAAO,MAAMC,cAAcC,OAAO,gBAAe;AAEjD,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAcD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEA,OAAO,eAAeY,kBAAkBN,MASvC;IACC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMtB,iBAAiB;QAC5DuB,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BT,QAAQU,OAAO,CAACC,UAAU,CAACC,wBAAwB,GAAG;QACtDZ,QAAQU,OAAO,CAACC,UAAU,CAACE,kBAAkB,GAAGpB,OAAOgB,gBAAgB;IACzE;IAEA,KAAK,MAAMK,aAAarB,OAAOsB,KAAK,CAAE;QACpCd,kBAAkBa;IACpB;IACA,OAAOd;AACT;AAEA,OAAO,MAAMgB,MAAM9B,iBAAiB,eAAe+B,oBAAoBxB,MAAM;QAUvEA;IATJ,MAAMO,UAAU,MAAMD,kBAAkBN;IAExC,MAAMyB,eAE4B,AAChC,CAAA,MAAMlB,QAAQU,OAAO,CAACS,QAAQ,CAAC,CAAC,WAAW,EAAE1B,OAAOU,IAAI,CAAC,CAAC,CAAC,AAAD,EAC1DiB,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAC7B,OAAO8B,OAAO,CAACC,MAAM,KAC1D/B,uBAAAA,OAAO8B,OAAO,CAACE,IAAI,qBAAnBhC,qBAAqBiC,eAAe,KACpCC;IAEJ,MAAMC,cAAc5B,QAAQ6B,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAItC,OAAO8B,OAAO,CAACS,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACpD;IAEhCW,OAAO8B,OAAO,CAACS,GAAG,GAAGF,YAAYK,QAAQ;IAEzC,MAAMC,UAAU,IAAIC;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAChD,OAAO8B,OAAO,CAACa,OAAO,EAAG;QACjEA,QAAQM,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOJ,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIxC,SAAuCgC;QAC3C,MAAM/C,aAAaoC,GAAG,CAAC;YAAEoB;QAAQ,GAAG;YAClCzC,SAAS,MAAMuB,aAAa;gBAC1BK,SAAS;oBACP,GAAG9B,OAAO8B,OAAO;oBACjBE,MACEJ,UAAUxC,oBAAoBmB,QAAQU,OAAO,EAAEkB,aAAaP;gBAChE;YACF;YACA,KAAK,MAAMsB,cAAc1D,kBAAmB;gBAC1CU,OAAOiD,QAAQ,CAACR,OAAO,CAACF,MAAM,CAACS;YACjC;QACF;QACA,IAAI,CAAChD,QAAQ,MAAM,IAAIkD,MAAM;QAC7B,OAAOlD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAO8B,OAAO,CAACE,IAAI,qBAAnBhC,sBAAqBqD,QAAQ;IACrC;AACF,GAAE"}