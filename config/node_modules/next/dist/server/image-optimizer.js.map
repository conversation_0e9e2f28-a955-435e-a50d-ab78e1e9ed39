{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["ImageError", "ImageOptimizerCache", "detectContentType", "fetchExternalImage", "fetchInternalImage", "getHash", "getImageSize", "getMaxAge", "imageOptimizer", "optimizeImage", "sendResponse", "AVIF", "WEBP", "PNG", "JPEG", "JXL", "JP2", "HEIC", "GIF", "SVG", "ICO", "ICNS", "TIFF", "BMP", "PDF", "CACHE_VERSION", "ANIMATABLE_TYPES", "BYPASS_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "cpus", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "mediaType", "includes", "items", "hash", "createHash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "join", "promises", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "every", "b", "i", "meta", "metadata", "_", "format", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "qualities", "url", "w", "q", "href", "Log", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "parseUrl", "test", "decodeURIComponent", "pathname", "hasLocalMatch", "hrefParsed", "URL", "toString", "_error", "protocol", "hasRemoteMatch", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "set", "revalidate", "Error", "err", "error", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "age", "endsWith", "slice", "n", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "webp", "png", "jpeg", "progressive", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "getOrientation", "operations", "Orientation", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "res", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "_req", "_res", "handleRequest", "mocked", "createRequestResponseMocks", "method", "socket", "nodeUrl", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageUpstream", "paramsResult", "upstreamBuffer", "upstreamType", "dangerouslyAllowSVG", "isAnimated", "getExtension", "output", "getMetadata", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getImageBlurSvg", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "sendEtagResponse", "finished", "contentDisposition", "contentDispositionType", "contentSecurityPolicy", "getContentType", "result", "byteLength", "end", "decodeBuffer", "imageSizeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IA+daA,UAAU;eAAVA;;IAvPAC,mBAAmB;eAAnBA;;IAzGSC,iBAAiB;eAAjBA;;IAggBAC,kBAAkB;eAAlBA;;IAkBAC,kBAAkB;eAAlBA;;IAnjBNC,OAAO;eAAPA;;IAyyBMC,YAAY;eAAZA;;IAvYNC,SAAS;eAATA;;IA0LMC,cAAc;eAAdA;;IA3KAC,aAAa;eAAbA;;IAwVNC,YAAY;eAAZA;;;wBAv2BW;oBACF;oBACJ;wBAEK;2EACK;gCACa;kEACpB;mEACD;sBACF;4DAC4B;8BAEjB;mCAEF;oCACC;6BAEY;6BAWV;6BACY;6DACxB;sBACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIzB,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACd;IAAMC;IAAKK;CAAI;AACzC,MAAMS,eAAe;IAACR;IAAKC;IAAKC;IAAME;IAAKR;IAAKE;CAAK;AACrD,MAAMW,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACC,IAAAA,QAAI,IAAGC,MAAM,GAAGN,SAAS;IACjE;AACF,EAAE,OAAOO,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BZ,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAmBvD,SAASQ,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWC,IAAAA,iBAAS,EAACF,QAAQD;IACnC,OAAOC,OAAOG,QAAQ,CAACF,YAAYA,WAAW;AAChD;AAEO,SAAS3C,QAAQ8C,KAAmC;IACzD,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;IACxB,KAAK,IAAIC,QAAQH,MAAO;QACtB,IAAI,OAAOG,SAAS,UAAUF,KAAKG,MAAM,CAACC,OAAOF;aAC5C;YACHF,KAAKG,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOF,KAAKK,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWC,IAAAA,UAAI,EAACP,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAMO,YAAQ,CAACC,EAAE,CAACT,KAAK;QAAEU,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMJ,YAAQ,CAACK,KAAK,CAACb,KAAK;QAAEU,WAAW;IAAK;IAC5C,MAAMF,YAAQ,CAACM,SAAS,CAACR,UAAUF;AACrC;AAOO,eAAe9D,kBACpB8D,MAAc;IAEd,IAAI;QAAC;QAAM;QAAM;KAAK,CAACW,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACvD,OAAO9D;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC6D,KAAK,CACpD,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAE1B;QACA,OAAO/D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC8D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAO1D;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOhE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC+D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACnE,OAAOzD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACwD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOjE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACgE,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOxD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACuD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOvD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOtD;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAACqD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACjD,OAAOrD;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAACoD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACjD,OAAO7D;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAAC4D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAChC;QACA,OAAO7D;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC4D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAO3D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC0D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACnE,OAAOpD;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAACmD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAChC;QACA,OAAO5D;IACT;IAEA,iCAAiC;IACjC,IAAIc,OAAO;QACT,MAAMgD,OAAO,MAAMhD,MAAMkC,QACtBe,QAAQ,GACRP,KAAK,CAAC,CAACQ,IAAM;QAChB,OAAQF,wBAAAA,KAAMG,MAAM;YAClB,KAAK;gBACH,OAAOtE;YACT,KAAK;gBACH,OAAOC;YACT,KAAK;gBACH,OAAOC;YACT,KAAK;YACL,KAAK;gBACH,OAAOC;YACT,KAAK;gBACH,OAAOI;YACT,KAAK;gBACH,OAAOC;YACT,KAAK;YACL,KAAK;gBACH,OAAOG;YACT,KAAK;gBACH,OAAOL;YACT;gBACE,OAAO;QACX;IACF;IAEA,OAAO;AACT;AAEO,MAAMhB;IAIX,OAAOiF,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA,qBACJA;QAVlB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAMC,aAAYX,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBW,SAAS;QAC9C,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGf;QACtB,IAAIgB;QAEJ,IAAIT,QAAQjD,MAAM,GAAG,GAAG;YACtB2D,KAAIC,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACL,KAAK;YACR,OAAO;gBAAEM,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACR,MAAM;YAC7B,OAAO;gBAAEM,cAAc;YAAqC;QAC9D;QAEA,IAAIN,IAAIvD,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAE6D,cAAc;YAA8B;QACvD;QAEA,IAAIN,IAAIS,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIV,IAAIS,UAAU,CAAC,MAAM;gBAKAE;YAJvBR,OAAOH;YACPU,aAAa;YACb,IACE,uBAAuBE,IAAI,CACzBC,mBAAmBF,EAAAA,YAAAA,IAAAA,cAAQ,EAACX,yBAATW,UAAeG,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLR,cAAc;gBAChB;YACF;YACA,IAAI,CAACS,IAAAA,gCAAa,EAACjB,eAAeE,MAAM;gBACtC,OAAO;oBAAEM,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIU;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIjB;gBACrBG,OAAOa,WAAWE,QAAQ;gBAC1BR,aAAa;YACf,EAAE,OAAOS,QAAQ;gBACf,OAAO;oBAAEb,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAACrD,QAAQ,CAAC+D,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAEd,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACe,IAAAA,kCAAc,EAAC3B,SAASG,gBAAgBmB,aAAa;gBACxD,OAAO;oBAAEV,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA6C;QACtE;QAEA,MAAMgB,QAAQC,SAAStB,GAAG;QAE1B,IAAIqB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLhB,cAAc;YAChB;QACF;QAEA,MAAMmB,QAAQ;eAAKjC,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACToC,MAAMC,IAAI,CAAC/F;QACb;QAEA,MAAMgG,cACJF,MAAMxE,QAAQ,CAACqE,UAAWjC,SAASiC,SAAS3F;QAE9C,IAAI,CAACgG,aAAa;YAChB,OAAO;gBACLrB,cAAc,CAAC,yBAAyB,EAAEgB,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASrB;QAEzB,IAAIsB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLtB,cACE;YACJ;QACF;QAEA,IAAIP,WAAW;YACb,IAAIV,OAAO;gBACTU,UAAU2B,IAAI,CAAC9F;YACjB;YAEA,IAAI,CAACmE,UAAU9C,QAAQ,CAAC2E,UAAU;gBAChC,OAAO;oBACLtB,cAAc,CAAC,2BAA2B,EAAEJ,EAAE,eAAe,CAAC;gBAChE;YACF;QACF;QAEA,MAAMnD,WAAWH,qBAAqBgD,WAAW,EAAE,EAAEV,IAAI2C,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW9B,IAAIS,UAAU,CAC7B,CAAC,EAAErB,WAAW2C,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACL5B;YACAsB;YACAf;YACAoB;YACAR;YACAM;YACA7E;YACA4C;QACF;IACF;IAEA,OAAOqC,YAAY,EACjB7B,IAAI,EACJmB,KAAK,EACLM,OAAO,EACP7E,QAAQ,EAMT,EAAU;QACT,OAAO3C,QAAQ;YAACoB;YAAe2E;YAAMmB;YAAOM;YAAS7E;SAAS;IAChE;IAEAkF,YAAY,EACVC,OAAO,EACP9C,UAAU,EAIX,CAAE;QACD,IAAI,CAAC+C,QAAQ,GAAGjE,IAAAA,UAAI,EAACgE,SAAS,SAAS;QACvC,IAAI,CAAC9C,UAAU,GAAGA;IACpB;IAEA,MAAMgD,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAWjE,IAAAA,UAAI,EAAC,IAAI,CAACiE,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMnE,YAAQ,CAACoE,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAY5E,MAAMJ,UAAU,GAAG8E,KAAKG,KAAK,CAAC,KAAK;gBAChE,MAAM9E,SAAS,MAAMI,YAAQ,CAAC2E,QAAQ,CAAC5E,IAAAA,UAAI,EAACiE,UAAUO;gBACtD,MAAM5E,WAAWiF,OAAOH;gBACxB,MAAM/E,SAASkF,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACNjF;wBACAD;wBACAH;oBACF;oBACAsF,iBACE7G,KAAKE,GAAG,CAACsB,QAAQ,IAAI,CAACuB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3D8C,KAAKD,GAAG;oBACVW,eAAetF;oBACfuF,SAASZ,MAAM1E;gBACjB;YACF;QACF,EAAE,OAAOiB,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMsE,IACJhB,QAAgB,EAChBW,KAAmC,EACnC,EACEM,UAAU,EAGX,EACD;QACA,IAAIN,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIM,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAMzF,WACJzB,KAAKE,GAAG,CAAC+G,YAAY,IAAI,CAAClE,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/D8C,KAAKD,GAAG;QAEV,IAAI;YACF,MAAM9E,gBACJQ,IAAAA,UAAI,EAAC,IAAI,CAACiE,QAAQ,EAAEE,WACpBW,MAAMpF,SAAS,EACf0F,YACAxF,UACAkF,MAAMjF,MAAM,EACZiF,MAAMhF,IAAI;QAEd,EAAE,OAAOwF,KAAK;YACZpD,KAAIqD,KAAK,CAAC,CAAC,+BAA+B,EAAEpB,SAAS,CAAC,EAAEmB;QAC1D;IACF;AACF;AACO,MAAMzJ,mBAAmBwJ;IAG9BtB,YAAYyB,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIhB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACoB,KAAKjB,MAAM,GAAGgB,UAAUE,IAAI,GAAGrB,KAAK,CAAC,KAAK;QAC/CoB,MAAMA,IAAIE,WAAW;QACrB,IAAInB,OAAO;YACTA,QAAQA,MAAMmB,WAAW;QAC3B;QACAL,IAAIT,GAAG,CAACY,KAAKjB;IACf;IACA,OAAOc;AACT;AAEO,SAASxJ,UAAUuJ,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIM,MAAMN,IAAI1B,GAAG,CAAC,eAAe0B,IAAI1B,GAAG,CAAC,cAAc;QACvD,IAAIgC,IAAI3D,UAAU,CAAC,QAAQ2D,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIhD,SAAS6C,KAAK;QACxB,IAAI,CAAC5C,MAAM+C,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEO,eAAe/J,cAAc,EAClCuD,MAAM,EACNyG,WAAW,EACX5C,OAAO,EACPN,KAAK,EACLmD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkB5G;IACtB,IAAIlC,OAAO;QACT,mCAAmC;QACnC,MAAM+I,cAAc/I,MAAMkC,QAAQ;YAChC8G,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAACzD,OAAOmD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAACzD,OAAO0D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgB9J,MAAM;YACxB,IAAIkK,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAcvD,UAAU;gBAC9BgD,YAAYM,IAAI,CAAC;oBACftD,SAASvF,KAAKE,GAAG,CAAC4I,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACLhF,KAAIC,QAAQ,CACV,CAAC,wIAAwI,CAAC,GACxI;gBAEJuE,YAAYS,IAAI,CAAC;oBAAEzD;gBAAQ;YAC7B;QACF,OAAO,IAAI4C,gBAAgB7J,MAAM;YAC/BiK,YAAYS,IAAI,CAAC;gBAAEzD;YAAQ;QAC7B,OAAO,IAAI4C,gBAAgB5J,KAAK;YAC9BgK,YAAYU,GAAG,CAAC;gBAAE1D;YAAQ;QAC5B,OAAO,IAAI4C,gBAAgB3J,MAAM;YAC/B+J,YAAYW,IAAI,CAAC;gBAAE3D;gBAAS4D,aAAa;YAAK;QAChD;QAEAb,kBAAkB,MAAMC,YAAYa,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAI9I,2BAA2B+H,qBAAqB,cAAc;YAChEtE,KAAIqD,KAAK,CACP,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAI1J,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAI4C,yBAAyB;YAC3ByD,KAAIC,QAAQ,CACV,CAAC,wLAAwL,CAAC,GACxL;YAEJ1D,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAM+I,cAAc,MAAMC,IAAAA,8BAAc,EAAC5H;QAEzC,MAAM6H,aAA0B,EAAE;QAElC,IAAIF,gBAAgBG,2BAAW,CAACC,SAAS,EAAE;YACzCF,WAAWlE,IAAI,CAAC;gBAAEqE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACI,YAAY,EAAE;YACnDL,WAAWlE,IAAI,CAAC;gBAAEqE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACK,WAAW,EAAE;YAClDN,WAAWlE,IAAI,CAAC;gBAAEqE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIvB,QAAQ;YACVmB,WAAWlE,IAAI,CAAC;gBAAEqE,MAAM;gBAAUzE;gBAAOmD;YAAO;QAClD,OAAO;YACLmB,WAAWlE,IAAI,CAAC;gBAAEqE,MAAM;gBAAUzE;YAAM;QAC1C;QAEA,MAAM,EAAE6E,aAAa,EAAE,GACrBrK,QAAQ;QAEV,IAAI0I,gBAAgB9J,MAAM;YACxBiK,kBAAkB,MAAMwB,cAAcpI,QAAQ6H,YAAY,QAAQhE;QACpE,OAAO,IAAI4C,gBAAgB7J,MAAM;YAC/BgK,kBAAkB,MAAMwB,cAAcpI,QAAQ6H,YAAY,QAAQhE;QACpE,OAAO,IAAI4C,gBAAgB5J,KAAK;YAC9B+J,kBAAkB,MAAMwB,cAAcpI,QAAQ6H,YAAY,OAAOhE;QACnE,OAAO,IAAI4C,gBAAgB3J,MAAM;YAC/B8J,kBAAkB,MAAMwB,cAAcpI,QAAQ6H,YAAY,QAAQhE;QACpE;IACF;IAEA,OAAO+C;AACT;AAEO,eAAezK,mBAAmBiG,IAAY;IACnD,MAAMiG,MAAM,MAAMC,MAAMlG;IAExB,IAAI,CAACiG,IAAIE,EAAE,EAAE;QACXlG,KAAIqD,KAAK,CAAC,sCAAsCtD,MAAMiG,IAAIG,MAAM;QAChE,MAAM,IAAIxM,WACRqM,IAAIG,MAAM,EACV;IAEJ;IAEA,MAAMxI,SAASyI,OAAOC,IAAI,CAAC,MAAML,IAAIM,WAAW;IAChD,MAAMlC,cAAc4B,IAAIvE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMuE,eAAeP,IAAIvE,OAAO,CAACO,GAAG,CAAC;IAErC,OAAO;QAAErE;QAAQyG;QAAamC;IAAa;AAC7C;AAEO,eAAexM,mBACpBgG,IAAY,EACZyG,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxChH,KAAKG;YACL8G,QAAQL,KAAKK,MAAM,IAAI;YACvBC,QAAQN,KAAKM,MAAM;QACrB;QAEA,MAAMJ,cAAcC,OAAO7H,GAAG,EAAE6H,OAAOX,GAAG,EAAEe,YAAO,CAACC,KAAK,CAACjH,MAAM;QAChE,MAAM4G,OAAOX,GAAG,CAACiB,WAAW;QAE5B,IAAI,CAACN,OAAOX,GAAG,CAAC1C,UAAU,EAAE;YAC1BtD,KAAIqD,KAAK,CAAC,6BAA6BtD,MAAM4G,OAAOX,GAAG,CAAC1C,UAAU;YAClE,MAAM,IAAI3J,WACRgN,OAAOX,GAAG,CAAC1C,UAAU,EACrB;QAEJ;QAEA,MAAM3F,SAASyI,OAAOc,MAAM,CAACP,OAAOX,GAAG,CAACmB,OAAO;QAC/C,MAAM/C,cAAcuC,OAAOX,GAAG,CAACoB,SAAS,CAAC;QACzC,MAAMb,eAAeI,OAAOX,GAAG,CAACoB,SAAS,CAAC;QAC1C,OAAO;YAAEzJ;YAAQyG;YAAamC;QAAa;IAC7C,EAAE,OAAOnD,KAAK;QACZpD,KAAIqD,KAAK,CAAC,sCAAsCtD,MAAMqD;QACtD,MAAM,IAAIzJ,WACR,KACA;IAEJ;AACF;AAEO,eAAeQ,eACpBkN,aAA4B,EAC5BC,YAGC,EACDtI,UAMC,EACDC,KAA0B;IAE1B,MAAM,EAAEc,IAAI,EAAEyB,OAAO,EAAEN,KAAK,EAAEvE,QAAQ,EAAE,GAAG2K;IAC3C,MAAMC,iBAAiBF,cAAc1J,MAAM;IAC3C,MAAMF,SAASvD,UAAUmN,cAAcd,YAAY;IACnD,MAAMiB,eAAe,MAAM3N,kBAAkB0N;IAE7C,IACE,CAACC,gBACD,CAACA,aAAanH,UAAU,CAAC,aACzBmH,aAAa3K,QAAQ,CAAC,MACtB;QACAmD,KAAIqD,KAAK,CACP,kDACAtD,MACA,YACAyH;QAEF,MAAM,IAAI7N,WAAW,KAAK;IAC5B;IACA,IACE6N,aAAanH,UAAU,CAAC,gBACxB,CAACrB,WAAWG,MAAM,CAACsI,mBAAmB,EACtC;QACAzH,KAAIqD,KAAK,CACP,CAAC,wBAAwB,EAAEtD,KAAK,YAAY,EAAEyH,aAAa,qCAAqC,CAAC;QAEnG,MAAM,IAAI7N,WACR,KACA;IAEJ;IACA,IAAI0B,iBAAiBwB,QAAQ,CAAC2K,iBAAiBE,IAAAA,mBAAU,EAACH,iBAAiB;QACzEvH,KAAIC,QAAQ,CACV,CAAC,wBAAwB,EAAEF,KAAK,8GAA8G,CAAC;QAEjJ,OAAO;YAAEpC,QAAQ4J;YAAgBnD,aAAaoD;YAAc/J;QAAO;IACrE;IACA,IAAInC,aAAauB,QAAQ,CAAC2K,eAAe;QACvC,OAAO;YAAE7J,QAAQ4J;YAAgBnD,aAAaoD;YAAc/J;QAAO;IACrE;IAEA,IAAI2G;IAEJ,IAAIzH,UAAU;QACZyH,cAAczH;IAChB,OAAO,IACLgL,IAAAA,yBAAY,EAACH,iBACbA,iBAAiBjN,QACjBiN,iBAAiBlN,MACjB;QACA8J,cAAcoD;IAChB,OAAO;QACLpD,cAAc3J;IAChB;IACA,IAAI;QACF,IAAI8J,kBAAkB,MAAMnK,cAAc;YACxCuD,QAAQ4J;YACRnD;YACA5C;YACAN;YACAoD,kBAAkBtF,WAAW4I,MAAM;QACrC;QACA,IAAIrD,iBAAiB;YACnB,IAAItF,SAASiC,SAAS3F,iBAAiBiG,YAAYhG,cAAc;gBAC/D,MAAM,EAAEqM,WAAW,EAAE,GACnBnM,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAM+C,OAAO,MAAMoJ,YAAYtD;gBAC/B,MAAMuD,OAAO;oBACXC,WAAWtJ,KAAKyC,KAAK;oBACrB8G,YAAYvJ,KAAK4F,MAAM;oBACvB4D,aAAa,CAAC,KAAK,EAAE7D,YAAY,QAAQ,EAAEG,gBAAgBzD,QAAQ,CACjE,UACA,CAAC;gBACL;gBACAyD,kBAAkB6B,OAAOC,IAAI,CAAC6B,SAASC,IAAAA,6BAAe,EAACL;gBACvD1D,cAAc;YAChB;YACA,OAAO;gBACLzG,QAAQ4G;gBACRH;gBACA3G,QAAQxB,KAAKE,GAAG,CAACsB,QAAQuB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAI5F,WAAW,KAAK;QAC5B;IACF,EAAE,OAAO0J,OAAO;QACd,IAAIkE,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACL7J,QAAQ4J;gBACRnD,aAAaoD;gBACb/J,QAAQuB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAI5F,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAASyO,yBACPxI,GAAW,EACXwE,WAA0B;IAE1B,MAAM,CAACiE,sBAAsB,GAAGzI,IAAI6C,KAAK,CAAC,KAAK;IAC/C,MAAM6F,wBAAwBD,sBAAsB5F,KAAK,CAAC,KAAK8F,GAAG;IAClE,IAAI,CAACnE,eAAe,CAACkE,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsB7F,KAAK,CAAC,KAAK;IACpD,MAAMjF,YAAYmK,IAAAA,yBAAY,EAACvD;IAC/B,OAAO,CAAC,EAAEoE,SAAS,CAAC,EAAEhL,UAAU,CAAC;AACnC;AAEA,SAASiL,mBACP3J,GAAoB,EACpBkH,GAAmB,EACnBpG,GAAW,EACXhC,IAAY,EACZwG,WAA0B,EAC1B1C,QAAiB,EACjBgH,MAAoB,EACpBC,YAAiC,EACjClL,MAAc,EACdwB,KAAc;IAEd+G,IAAI4C,SAAS,CAAC,QAAQ;IACtB5C,IAAI4C,SAAS,CACX,iBACAlH,WACI,yCACA,CAAC,gBAAgB,EAAEzC,QAAQ,IAAIxB,OAAO,iBAAiB,CAAC;IAE9D,IAAIoL,IAAAA,6BAAgB,EAAC/J,KAAKkH,KAAKpI,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAEkL,UAAU;QAAK;IAC1B;IACA,IAAI1E,aAAa;QACf4B,IAAI4C,SAAS,CAAC,gBAAgBxE;IAChC;IAEA,MAAMoE,WAAWJ,yBAAyBxI,KAAKwE;IAC/C4B,IAAI4C,SAAS,CACX,uBACAG,IAAAA,2BAAkB,EAACP,UAAU;QAAE7C,MAAMgD,aAAaK,sBAAsB;IAAC;IAG3EhD,IAAI4C,SAAS,CAAC,2BAA2BD,aAAaM,qBAAqB;IAC3EjD,IAAI4C,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEI,UAAU;IAAM;AAC3B;AAEO,SAASzO,aACdyE,GAAoB,EACpBkH,GAAmB,EACnBpG,GAAW,EACXpC,SAAiB,EACjBG,MAAc,EACd+D,QAAiB,EACjBgH,MAAoB,EACpBC,YAAiC,EACjClL,MAAc,EACdwB,KAAc;IAEd,MAAMmF,cAAc8E,IAAAA,2BAAc,EAAC1L;IACnC,MAAMI,OAAO5D,QAAQ;QAAC2D;KAAO;IAC7B,MAAMwL,SAASV,mBACb3J,KACAkH,KACApG,KACAhC,MACAwG,aACA1C,UACAgH,QACAC,cACAlL,QACAwB;IAEF,IAAI,CAACkK,OAAOL,QAAQ,EAAE;QACpB9C,IAAI4C,SAAS,CAAC,kBAAkBxC,OAAOgD,UAAU,CAACzL;QAClDqI,IAAIqD,GAAG,CAAC1L;IACV;AACF;AAEO,eAAe1D,aACpB0D,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI/B,OAAO;YACT,MAAM+I,cAAc/I,MAAMkC;YAC1B,MAAM,EAAEuD,KAAK,EAAEmD,MAAM,EAAE,GAAG,MAAMG,YAAY9F,QAAQ;YACpD,OAAO;gBAAEwC;gBAAOmD;YAAO;QACzB,OAAO;YACL,MAAM,EAAEiF,YAAY,EAAE,GACpB5N,QAAQ;YACV,MAAM,EAAEwF,KAAK,EAAEmD,MAAM,EAAE,GAAG,MAAMiF,aAAa3L;YAC7C,OAAO;gBAAEuD;gBAAOmD;YAAO;QACzB;IACF;IAEA,MAAM,EAAEnD,KAAK,EAAEmD,MAAM,EAAE,GAAGkF,IAAAA,kBAAW,EAAC5L;IACtC,OAAO;QAAEuD;QAAOmD;IAAO;AACzB"}