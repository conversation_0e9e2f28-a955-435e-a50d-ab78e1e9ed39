{"version": 3, "sources": ["../../src/server/send-payload.ts"], "names": ["sendEtagResponse", "sendRenderResult", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "fresh", "headers", "statusCode", "end", "result", "type", "generateEtags", "poweredByHeader", "revalidate", "swr<PERSON><PERSON><PERSON>", "isResSent", "<PERSON><PERSON><PERSON><PERSON>", "formatRevalidate", "payload", "isDynamic", "toUnchunkedString", "etagPayload", "split", "sort", "join", "includes", "parse", "require", "root", "scriptTags", "querySelector", "querySelectorAll", "filter", "node", "hasAttribute", "innerHTML", "a", "b", "localeCompare", "for<PERSON>ach", "script", "remove", "append<PERSON><PERSON><PERSON>", "toString", "err", "console", "error", "generateETag", "undefined", "contentType", "RSC_CONTENT_TYPE_HEADER", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": ";;;;;;;;;;;;;;;IAUgBA,gBAAgB;eAAhBA;;IAwBMC,gBAAgB;eAAhBA;;;uBA9BI;sBACG;8DACX;4BACe;kCACO;;;;;;AAEjC,SAASD,iBACdE,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,IAAIE,IAAAA,cAAK,EAACJ,IAAIK,OAAO,EAAE;QAAEH;IAAK,IAAI;QAChCD,IAAIK,UAAU,GAAG;QACjBL,IAAIM,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeR,iBAAiB,EACrCC,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,UAAU,EACVC,QAAQ,EAUT;IACC,IAAIC,IAAAA,gBAAS,EAACb,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,2DAA2D;IAC3D,6DAA6D;IAC7D,IAAI,OAAOS,eAAe,eAAe,CAACX,IAAIc,SAAS,CAAC,kBAAkB;QACxEd,IAAIE,SAAS,CACX,iBACAa,IAAAA,4BAAgB,EAAC;YACfJ;YACAC;QACF;IAEJ;IAEA,MAAMI,UAAUT,OAAOU,SAAS,GAAG,OAAOV,OAAOW,iBAAiB;IAElE,IAAIF,YAAY,MAAM;QACpB,IAAIG,cAAcH;QAClB,IAAIR,SAAS,OAAO;YAClB,6CAA6C;YAC7C,iDAAiD;YACjD,kBAAkB;YAClBW,cAAcH,QAAQI,KAAK,CAAC,MAAMC,IAAI,GAAGC,IAAI,CAAC;QAChD,OAAO,IAAId,SAAS,UAAUQ,QAAQO,QAAQ,CAAC,aAAa;YAC1D,MAAM,EAAEC,KAAK,EAAE,GACbC,QAAQ;YAEV,IAAI;oBAKeC;gBAJjB,iBAAiB;gBACjB,IAAIA,OAAOF,MAAMR;gBAEjB,sCAAsC;gBACtC,IAAIW,cAAaD,sBAAAA,KACdE,aAAa,CAAC,4BADAF,oBAEbG,gBAAgB,CAAC,UAClBC,MAAM,CACL,CAACC;wBAC8BA;2BAA7B,CAACA,KAAKC,YAAY,CAAC,YAAUD,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBR,QAAQ,CAAC;;gBAG5D,2CAA2C;gBAC3CI,8BAAAA,WAAYN,IAAI,CAAC,CAACa,GAAGC,IAAMD,EAAED,SAAS,CAACG,aAAa,CAACD,EAAEF,SAAS;gBAEhE,kCAAkC;gBAClCN,8BAAAA,WAAYU,OAAO,CAAC,CAACC,SAAgBA,OAAOC,MAAM;gBAElD,4CAA4C;gBAC5CZ,8BAAAA,WAAYU,OAAO,CAAC,CAACC;wBACnBZ;4BAAAA,sBAAAA,KAAKE,aAAa,CAAC,4BAAnBF,oBAA4Bc,WAAW,CAACF;;gBAG1C,yBAAyB;gBACzBnB,cAAcO,KAAKe,QAAQ;YAC7B,EAAE,OAAOC,KAAK;gBACZC,QAAQC,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAEF;YAC9C;QACF;QAEA,MAAMzC,OAAOQ,gBAAgBoC,IAAAA,kBAAY,EAAC1B,eAAe2B;QACzD,IAAIjD,iBAAiBE,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIc,SAAS,CAAC,iBAAiB;QAClCd,IAAIE,SAAS,CACX,gBACAK,OAAOwC,WAAW,GACdxC,OAAOwC,WAAW,GAClBvC,SAAS,QACTwC,yCAAuB,GACvBxC,SAAS,SACT,qBACA;IAER;IAEA,IAAIQ,SAAS;QACXhB,IAAIE,SAAS,CAAC,kBAAkB+C,OAAOC,UAAU,CAAClC;IACpD;IAEA,IAAIjB,IAAIoD,MAAM,KAAK,QAAQ;QACzBnD,IAAIM,GAAG,CAAC;QACR;IACF;IAEA,IAAIU,YAAY,MAAM;QACpBhB,IAAIM,GAAG,CAACU;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMT,OAAO6C,kBAAkB,CAACpD;AAClC"}