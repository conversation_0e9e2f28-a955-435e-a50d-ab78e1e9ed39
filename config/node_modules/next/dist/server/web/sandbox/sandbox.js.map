{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["ErrorSource", "getRuntimeContext", "run", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "runtime", "evaluateInContext", "getModuleContext", "moduleName", "name", "onWarning", "onError", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCacheShared", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "runWithTaggedErrors", "edgeFunction", "_ENTRIES", "default", "cloned", "includes", "request", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "toString", "headers", "Headers", "key", "value", "Object", "entries", "set", "requestStore", "requestToBodyStream", "headerName", "response", "Error", "finalize"], "mappings": ";;;;;;;;;;;;;;;;IAOaA,WAAW;eAAXA;;IA+CSC,iBAAiB;eAAjBA;;IA8BTC,GAAG;eAAHA;;;yBAjFkC;6BACX;kCACC;AAE9B,MAAMF,cAAcG,OAAO;AAElC,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAcD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEO,eAAeL,kBAAkBW,MASvC;IACC,MAAM,EAAEM,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMC,IAAAA,yBAAgB,EAAC;QAC5DC,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,SAASZ,OAAOY,OAAO,IAAK,CAAA,KAAO,CAAA;QACnCC,UAAUb,OAAOa,QAAQ,KAAK;QAC9BC,mBAAmBd,OAAOc,iBAAiB;QAC3CC,SAASf,OAAOe,OAAO;IACzB;IAEA,IAAIf,OAAOgB,gBAAgB,EAAE;QAC3BV,QAAQW,OAAO,CAACC,UAAU,CAACC,wBAAwB,GAAG;QACtDb,QAAQW,OAAO,CAACC,UAAU,CAACE,kBAAkB,GAAGpB,OAAOgB,gBAAgB;IACzE;IAEA,KAAK,MAAMK,aAAarB,OAAOsB,KAAK,CAAE;QACpCf,kBAAkBc;IACpB;IACA,OAAOf;AACT;AAEO,MAAMhB,MAAMG,iBAAiB,eAAe8B,oBAAoBvB,MAAM;QAUvEA;IATJ,MAAMM,UAAU,MAAMjB,kBAAkBW;IAExC,MAAMwB,eAE4B,AAChC,CAAA,MAAMlB,QAAQW,OAAO,CAACQ,QAAQ,CAAC,CAAC,WAAW,EAAEzB,OAAOU,IAAI,CAAC,CAAC,CAAC,AAAD,EAC1DgB,OAAO;IAET,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACC,QAAQ,CAAC5B,OAAO6B,OAAO,CAACC,MAAM,KAC1D9B,uBAAAA,OAAO6B,OAAO,CAACE,IAAI,qBAAnB/B,qBAAqBgC,eAAe,KACpCC;IAEJ,MAAMC,cAAc5B,QAAQ6B,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAIrC,OAAO6B,OAAO,CAACS,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAEpDzC,OAAO6B,OAAO,CAACS,GAAG,GAAGF,YAAYM,QAAQ;IAEzC,MAAMC,UAAU,IAAIC;IACpB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAAChD,OAAO6B,OAAO,CAACc,OAAO,EAAG;QACjEA,QAAQM,GAAG,CAACJ,KAAKC,CAAAA,yBAAAA,MAAOJ,QAAQ,OAAM;IACxC;IAEA,IAAI;QACF,IAAIxC,SAAuC+B;QAC3C,MAAMiB,qBAAY,CAAC5D,GAAG,CAAC;YAAEqD;QAAQ,GAAG;YAClCzC,SAAS,MAAMsB,aAAa;gBAC1BK,SAAS;oBACP,GAAG7B,OAAO6B,OAAO;oBACjBE,MACEJ,UAAUwB,IAAAA,gCAAmB,EAAC7C,QAAQW,OAAO,EAAEiB,aAAaP;gBAChE;YACF;YACA,KAAK,MAAMyB,cAAc5D,kBAAmB;gBAC1CU,OAAOmD,QAAQ,CAACV,OAAO,CAACH,MAAM,CAACY;YACjC;QACF;QACA,IAAI,CAAClD,QAAQ,MAAM,IAAIoD,MAAM;QAC7B,OAAOpD;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAO6B,OAAO,CAACE,IAAI,qBAAnB/B,sBAAqBuD,QAAQ;IACrC;AACF"}