{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-regex.ts"], "names": ["getNamedMiddlewareRegex", "getNamedRouteRegex", "getRouteRegex", "parseParameter", "param", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "segments", "removeTrailingSlash", "split", "groups", "groupIndex", "parameterizedRoute", "map", "segment", "markerMatch", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "paramMatch<PERSON>", "match", "pos", "escapeStringRegexp", "join", "normalizedRoute", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "interceptionPrefix", "getNamedParametrizedRoute", "prefixRouteKeys", "namedParameterizedRoute", "hasInterceptionMarker", "some", "usedMarker", "NEXT_INTERCEPTION_MARKER_PREFIX", "undefined", "NEXT_QUERY_PARAM_PREFIX", "prefixRouteKey", "result", "namedRegex", "options", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;;;;;;;;;;;;IA4NgBA,uBAAuB;eAAvBA;;IAhBAC,kBAAkB;eAAlBA;;IAnIAC,aAAa;eAAbA;;IA9CAC,cAAc;eAAdA;;;2BAxBT;oCACoC;8BACR;qCACC;AAqB7B,SAASA,eAAeC,KAAa;IAC1C,MAAMC,WAAWD,MAAME,UAAU,CAAC,QAAQF,MAAMG,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZD,QAAQA,MAAMI,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMC,SAASL,MAAME,UAAU,CAAC;IAChC,IAAIG,QAAQ;QACVL,QAAQA,MAAMI,KAAK,CAAC;IACtB;IACA,OAAO;QAAEE,KAAKN;QAAOK;QAAQJ;IAAS;AACxC;AAEA,SAASM,qBAAqBC,KAAa;IACzC,MAAMC,WAAWC,IAAAA,wCAAmB,EAACF,OAAOJ,KAAK,CAAC,GAAGO,KAAK,CAAC;IAC3D,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IACjB,OAAO;QACLC,oBAAoBL,SACjBM,GAAG,CAAC,CAACC;YACJ,MAAMC,cAAcC,8CAA0B,CAACC,IAAI,CAAC,CAACC,IACnDJ,QAAQd,UAAU,CAACkB;YAErB,MAAMC,eAAeL,QAAQM,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAIL,eAAeI,cAAc;gBAC/B,MAAM,EAAEf,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAesB,YAAY,CAAC,EAAE;gBAChET,MAAM,CAACN,IAAI,GAAG;oBAAEiB,KAAKV;oBAAcR;oBAAQJ;gBAAS;gBACpD,OAAO,AAAC,MAAGuB,IAAAA,gCAAkB,EAACP,eAAa;YAC7C,OAAO,IAAII,cAAc;gBACvB,MAAM,EAAEf,GAAG,EAAED,MAAM,EAAEJ,QAAQ,EAAE,GAAGF,eAAesB,YAAY,CAAC,EAAE;gBAChET,MAAM,CAACN,IAAI,GAAG;oBAAEiB,KAAKV;oBAAcR;oBAAQJ;gBAAS;gBACpD,OAAOI,SAAUJ,WAAW,gBAAgB,WAAY;YAC1D,OAAO;gBACL,OAAO,AAAC,MAAGuB,IAAAA,gCAAkB,EAACR;YAChC;QACF,GACCS,IAAI,CAAC;QACRb;IACF;AACF;AAOO,SAASd,cAAc4B,eAAuB;IACnD,MAAM,EAAEZ,kBAAkB,EAAEF,MAAM,EAAE,GAAGL,qBAAqBmB;IAC5D,OAAO;QACLC,IAAI,IAAIC,OAAO,AAAC,MAAGd,qBAAmB;QACtCF,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAASiB;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAM,AAACF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAC,AAACJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAY9B;IAZ8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACfvB,OAAO,EACPwB,SAAS,EACTC,SAAS,EAOV,GAZ8B;IAa7B,MAAM,EAAEnC,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAeiB;IAEjD,uDAAuD;IACvD,kBAAkB;IAClB,IAAI0B,aAAapC,IAAIqC,OAAO,CAAC,OAAO;IAEpC,IAAIF,WAAW;QACbC,aAAa,AAAC,KAAED,YAAYC;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWtC,KAAK,CAAC,GAAG,MAAM;QAC5CwC,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaH;IACf;IAEA,IAAIE,WAAW;QACbD,SAAS,CAACE,WAAW,GAAG,AAAC,KAAED,YAAYnC;IACzC,OAAO;QACLkC,SAAS,CAACE,WAAW,GAAGpC;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAM0C,qBAAqBV,qBACvBd,IAAAA,gCAAkB,EAACc,sBACnB;IAEJ,OAAOjC,SACHJ,WACE,AAAC,SAAM+C,qBAAmB,QAAKN,aAAW,YAC1C,AAAC,MAAGM,qBAAmB,QAAKN,aAAW,UACzC,AAAC,MAAGM,qBAAmB,QAAKN,aAAW;AAC7C;AAEA,SAASO,0BAA0BzC,KAAa,EAAE0C,eAAwB;IACxE,MAAMzC,WAAWC,IAAAA,wCAAmB,EAACF,OAAOJ,KAAK,CAAC,GAAGO,KAAK,CAAC;IAC3D,MAAM4B,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAChD,OAAO;QACLW,yBAAyB1C,SACtBM,GAAG,CAAC,CAACC;YACJ,MAAMoC,wBAAwBlC,8CAA0B,CAACmC,IAAI,CAAC,CAACjC,IAC7DJ,QAAQd,UAAU,CAACkB;YAErB,MAAMC,eAAeL,QAAQM,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAI8B,yBAAyB/B,cAAc;gBACzC,MAAM,CAACiC,WAAW,GAAGtC,QAAQL,KAAK,CAACU,YAAY,CAAC,EAAE;gBAElD,OAAOgB,sBAAsB;oBAC3BE;oBACAD,oBAAoBgB;oBACpBtC,SAASK,YAAY,CAAC,EAAE;oBACxBmB;oBACAC,WAAWS,kBACPK,0CAA+B,GAC/BC;gBACN;YACF,OAAO,IAAInC,cAAc;gBACvB,OAAOgB,sBAAsB;oBAC3BE;oBACAvB,SAASK,YAAY,CAAC,EAAE;oBACxBmB;oBACAC,WAAWS,kBAAkBO,kCAAuB,GAAGD;gBACzD;YACF,OAAO;gBACL,OAAO,AAAC,MAAGhC,IAAAA,gCAAkB,EAACR;YAChC;QACF,GACCS,IAAI,CAAC;QACRe;IACF;AACF;AAUO,SAAS3C,mBACd6B,eAAuB,EACvBgC,cAAuB;IAEvB,MAAMC,SAASV,0BAA0BvB,iBAAiBgC;IAC1D,OAAO;QACL,GAAG5D,cAAc4B,gBAAgB;QACjCkC,YAAY,AAAC,MAAGD,OAAOR,uBAAuB,GAAC;QAC/CX,WAAWmB,OAAOnB,SAAS;IAC7B;AACF;AAMO,SAAS5C,wBACd8B,eAAuB,EACvBmC,OAEC;IAED,MAAM,EAAE/C,kBAAkB,EAAE,GAAGP,qBAAqBmB;IACpD,MAAM,EAAEoC,WAAW,IAAI,EAAE,GAAGD;IAC5B,IAAI/C,uBAAuB,KAAK;QAC9B,IAAIiD,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLF,YAAY,AAAC,OAAIG,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEZ,uBAAuB,EAAE,GAAGF,0BAClCvB,iBACA;IAEF,IAAIsC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLF,YAAY,AAAC,MAAGT,0BAA0Ba,uBAAqB;IACjE;AACF"}