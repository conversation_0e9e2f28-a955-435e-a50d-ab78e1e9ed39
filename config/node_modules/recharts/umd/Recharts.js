/*! For license information please see Recharts.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-is"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","react-is","react-dom"],t):"object"==typeof exports?exports.Recharts=t(require("react"),require("react-is"),require("react-dom")):e.Recharts=t(e.<PERSON><PERSON>,e.<PERSON>actIs,e.ReactDOM)}(this,((e,t,r)=>(()=>{var n={8:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6773);t.debounce=function(e,t=0,r={}){"object"!=typeof r&&(r={});const{leading:i=!1,trailing:a=!0,maxWait:o}=r,l=Array(2);let c;i&&(l[0]="leading"),a&&(l[1]="trailing");let s=null;const u=n.debounce((function(...t){c=e.apply(this,t),s=null}),t,{edges:l}),f=function(...t){return null!=o&&(null===s&&(s=Date.now()),Date.now()-s>=o)?(c=e.apply(this,t),s=Date.now(),u.cancel(),u.schedule(),c):(u.apply(this,t),c)};return f.cancel=u.cancel,f.flush=()=>(u.flush(),c),f}},25:(e,t,r)=>{e.exports=r(1334).last},58:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9181);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},184:(e,t,r)=>{e.exports=r(4259).sortBy},228:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw new TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=new Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,s,u=this._events[l],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(s=1,c=new Array(f-1);s<f;s++)c[s-1]=arguments[s];u.fn.apply(u.context,c)}else{var d,p=u.length;for(s=0;s<p;s++)switch(u[s].once&&this.removeListener(e,u[s].fn,void 0,!0),f){case 1:u[s].fn.call(u[s].context);break;case 2:u[s].fn.call(u[s].context,t);break;case 3:u[s].fn.call(u[s].context,t,n);break;case 4:u[s].fn.call(u[s].context,t,n,i);break;default:if(!c)for(d=1,c=new Array(f-1);d<f;d++)c[d-1]=arguments[d];u[s].fn.apply(u[s].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,s=[],u=l.length;c<u;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&s.push(l[c]);s.length?this._events[a]=1===s.length?s[0]:s:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},305:(e,t,r)=>{e.exports=r(4200).get},316:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8509),i=r(58),a=r(4905),o=r(6761);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t)&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e))}},334:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.maxBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let i=1;i<e.length;i++){const a=e[i],o=t(a);o>n&&(n=o,r=a)}return r}},645:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},717:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8273);t.isMatch=function(e,t){return n.isMatchWith(e,t,(()=>{}))}},924:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8240),i=r(6440),a=r(8202);t.minBy=function(e,t){if(null!=e)return n.minBy(Array.from(e),a.iteratee(t??i.identity))}},993:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(7074),i=r(6012),a=r(2049),o=r(9184),l=r(6761);function c(e,t,r,n,i,a,o){const l=o(e,t,r,n,i,a);if(void 0!==l)return l;if(typeof e==typeof t)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===t;case"number":return e===t||Object.is(e,t);case"object":return s(e,t,a,o)}return s(e,t,a,o)}function s(e,t,r,u){if(Object.is(e,t))return!0;let f=a.getTag(e),d=a.getTag(t);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return e.toString()===t.toString();case o.numberTag:{const r=e.valueOf(),n=t.valueOf();return l.eq(r,n)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(e.valueOf(),t.valueOf());case o.regexpTag:return e.source===t.source&&e.flags===t.flags;case o.functionTag:return e===t}const p=(r=r??new Map).get(e),h=r.get(t);if(null!=p&&null!=h)return p===t;r.set(e,t),r.set(t,e);try{switch(f){case o.mapTag:if(e.size!==t.size)return!1;for(const[n,i]of e.entries())if(!t.has(n)||!c(i,t.get(n),n,e,t,r,u))return!1;return!0;case o.setTag:{if(e.size!==t.size)return!1;const n=Array.from(e.values()),i=Array.from(t.values());for(let a=0;a<n.length;a++){const o=n[a],l=i.findIndex((n=>c(o,n,void 0,e,t,r,u)));if(-1===l)return!1;i.splice(l,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)!==Buffer.isBuffer(t))return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!c(e[n],t[n],n,e,t,r,u))return!1;return!0;case o.arrayBufferTag:return e.byteLength===t.byteLength&&s(new Uint8Array(e),new Uint8Array(t),r,u);case o.dataViewTag:return e.byteLength===t.byteLength&&e.byteOffset===t.byteOffset&&s(new Uint8Array(e),new Uint8Array(t),r,u);case o.errorTag:return e.name===t.name&&e.message===t.message;case o.objectTag:{if(!(s(e.constructor,t.constructor,r,u)||n.isPlainObject(e)&&n.isPlainObject(t)))return!1;const a=[...Object.keys(e),...i.getSymbols(e)],o=[...Object.keys(t),...i.getSymbols(t)];if(a.length!==o.length)return!1;for(let n=0;n<a.length;n++){const i=a[n],o=e[i];if(!Object.hasOwn(t,i))return!1;if(!c(o,t[i],i,e,t,r,u))return!1}return!0}default:return!1}}finally{r.delete(e),r.delete(t)}}t.isEqualWith=function(e,t,r){return c(e,t,void 0,void 0,void 0,void 0,r)}},1081:(e,t,r)=>{e.exports=r(2810).uniqBy},1334:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(645),i=r(4483),a=r(58);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},1366:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},1465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},1576:(e,t,r)=>{e.exports=r(4167).omit},1846:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},2049:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},2067:(e,t,r)=>{e.exports=r(3667).sumBy},2162:(e,t,r)=>{"use strict";var n=r(5442),i=r(9888);var a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,s=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;f=s((function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]}),[t,r,n,i]);var p=o(e,f[0],f[1]);return c((function(){d.hasValue=!0,d.value=p}),[p]),u(p),p}},2520:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},2751:e=>{"use strict";e.exports=t},2810:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8805),i=r(6440),a=r(8161),o=r(8202);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},2938:(e,t,r)=>{e.exports=r(8695).isPlainObject},2972:(e,t,r)=>{e.exports=r(924).minBy},2984:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(2049);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},3025:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){const t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){const l=e[n];a?"\\"===l&&n+1<r?(n++,i+=e[n]):l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},3036:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(717),i=r(1465),a=r(3923),o=r(4200),l=r(7324);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){const i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},3097:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3500),i=r(3998),a=r(3025);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map((e=>String(e)));const l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=t.map((e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e)?e:{key:e,path:a.toPath(e)})));return e.map((e=>({original:e,criteria:c.map((t=>((e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t)(t,e)))}))).slice().sort(((e,t)=>{for(let i=0;i<c.length;i++){const a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0})).map((e=>e.original))}},3403:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4200);t.property=function(e){return function(t){return n.get(t,e)}}},3412:(e,t,r)=>{e.exports=r(5012).range},3500:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:e!=e?4:0}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});t.compareValues=(e,t,n)=>{if(e!==t){const i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},3667:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8202);t.sumBy=function(e,t){if(!e||!e.length)return 0;let r;null!=t&&(t=n.iteratee(t));for(let n=0;n<e.length;n++){const i=t?t(e[n]):e[n];void 0!==i&&(void 0===r?r=i:r+=i)}return r}},3844:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3964);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},3908:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},3923:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9467);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},3964:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6012),i=r(2049),a=r(9184),o=r(2520),l=r(3908);function c(e,t,r,n=new Map,u=void 0){const f=u?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){const t=new Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,u);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){const t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){const t=new Map;n.set(e,t);for(const[i,a]of e)t.set(i,c(a,i,r,n,u));return t}if(e instanceof Set){const t=new Set;n.set(e,t);for(const i of e)t.add(c(i,void 0,r,n,u));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){const t=new(Object.getPrototypeOf(e).constructor)(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,u);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){const t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),s(t,e,r,n,u),t}if("undefined"!=typeof File&&e instanceof File){const t=new File([e],e.name,{type:e.type});return n.set(e,t),s(t,e,r,n,u),t}if(e instanceof Blob){const t=new Blob([e],{type:e.type});return n.set(e,t),s(t,e,r,n,u),t}if(e instanceof Error){const t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,n,u),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){const t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),s(t,e,r,n,u),t}return e}function s(e,t,r=e,i,a){const o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){const l=o[n],s=Object.getOwnPropertyDescriptor(e,l);(null==s||s.writable)&&(e[l]=c(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},3998:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(1366),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!("number"!=typeof e&&"boolean"!=typeof e&&null!=e&&!n.isSymbol(e))||("string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e)))}},4146:(e,t,r)=>{"use strict";var n=r(2751),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function c(e){return n.isMemo(e)?o:l[e.$$typeof]||i}l[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[n.Memo]=o;var s=Object.defineProperty,u=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(h){var i=p(r);i&&i!==h&&e(t,i,n)}var o=u(r);f&&(o=o.concat(f(r)));for(var l=c(t),y=c(r),v=0;v<o.length;++v){var m=o[v];if(!(a[m]||n&&n[m]||y&&y[m]||l&&l[m])){var g=d(r,m);try{s(t,m,g)}catch(e){}}}}return t}},4167:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(7841),i=r(3844);t.omit=function(e,...t){if(null==e)return{};const r=i.cloneDeep(e);for(let e=0;e<t.length;e++){let i=t[e];switch(typeof i){case"object":Array.isArray(i)||(i=Array.from(i));for(let e=0;e<i.length;e++){const t=i[e];n.unset(r,t)}break;case"string":case"symbol":case"number":n.unset(r,i)}}return r}},4200:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8193),i=r(5112),a=r(1465),o=r(3025);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;const a=t[r];return void 0===a?i.isDeepKey(r)?e(t,o.toPath(r),l):l:a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));const e=t[r];return void 0===e?l:e}default:{if(Array.isArray(r))return function(e,t,r){if(0===t.length)return r;let i=e;for(let e=0;e<t.length;e++){if(null==i)return r;if(n.isUnsafeProperty(t[e]))return r;i=i[t[e]]}if(void 0===i)return r;return i}(t,r,l);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;const e=t[r];return void 0===e?l:e}}}},4259:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3097),i=r(5711),a=r(316);t.sortBy=function(e,...t){const r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},4297:(e,t,r)=>{e.exports=r(5259).throttle},4338:(e,t,r)=>{e.exports=r(5938).maxBy},4483:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},4569:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8919);t.toFinite=function(e){if(!e)return 0===e?e:0;if((e=n.toNumber(e))===1/0||e===-1/0){return(e<0?-1:1)*Number.MAX_VALUE}return e==e?e:0}},4905:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},5012:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(316),i=r(4569);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);const a=Math.max(Math.ceil((t-e)/(r||1)),0),o=new Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},5112:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},5259:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8);t.throttle=function(e,t=0,r={}){const{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},5442:t=>{"use strict";t.exports=e},5711:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){const r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){const o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},5938:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(334),i=r(6440),a=r(8202);t.maxBy=function(e,t){if(null!=e)return n.maxBy(Array.from(e),a.iteratee(t??i.identity))}},6003:e=>{"use strict";e.exports=r},6012:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter((t=>Object.prototype.propertyIsEnumerable.call(e,t)))}},6440:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},6502:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},6761:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},6773:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,a=null;const o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),c=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)};let s=null;const u=()=>{null!=s&&clearTimeout(s),s=setTimeout((()=>{s=null,l&&c(),f()}),t)},f=()=>{null!==s&&(clearTimeout(s),s=null),i=void 0,a=null},d=function(...e){if(r?.aborted)return;i=this,a=e;const t=null==s;u(),o&&t&&c()};return d.schedule=u,d.cancel=f,d.flush=()=>{c()},r?.addEventListener("abort",f,{once:!0}),d}},7074:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},7324:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(5112),i=r(8509),a=r(2984),o=r(3025);t.has=function(e,t){let r;if(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&null==e?.[t]?o.toPath(t):[t],0===r.length)return!1;let l=e;for(let e=0;e<r.length;e++){const t=r[e];if(null==l||!Object.hasOwn(l,t)){if(!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1}l=l[t]}return!0}},7541:(e,t,r)=>{e.exports=r(9341).isEqual},7841:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4200),i=r(8193),a=r(5112),o=r(1465),l=r(3025);function c(e,t){const r=n.get(e,t.slice(0,-1),e),a=t[t.length-1];if(void 0===r?.[a])return!0;if(i.isUnsafeProperty(a))return!1;try{return delete r[a],!0}catch{return!1}}t.unset=function(e,t){if(null==e)return!0;switch(typeof t){case"symbol":case"number":case"object":if(Array.isArray(t))return c(e,t);if("number"==typeof t?t=o.toKey(t):"object"==typeof t&&(t=Object.is(t?.valueOf(),-0)?"-0":String(t)),i.isUnsafeProperty(t))return!1;if(void 0===e?.[t])return!0;try{return delete e[t],!0}catch{return!1}case"string":if(void 0===e?.[t]&&a.isDeepKey(t))return c(e,l.toPath(t));if(i.isUnsafeProperty(t))return!1;try{return delete e[t],!0}catch{return!1}}}},7861:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(717),i=r(3844);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},8161:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(58),i=r(1846);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},8193:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},8202:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6440),i=r(3403),a=r(7861),o=r(3036);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":return Array.isArray(e)&&2===e.length?o.matchesProperty(e[0],e[1]):a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},8240:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.minBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let i=1;i<e.length;i++){const a=e[i],o=t(a);o<n&&(n=o,r=a)}return r}},8273:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(717),i=r(4905),a=r(2520),o=r(6761);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(const[i,a]of t.entries()){if(!1===r(e.get(i),a,i,e,t,n))return!1}return!0}(e,t,r,n);if(t instanceof Set)return s(e,t,r,n);const i=Object.keys(t);if(null==e)return 0===i.length;if(0===i.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let o=0;o<i.length;o++){const l=i[o];if(!a.isPrimitive(e)&&!(l in e))return!1;if(void 0===t[l]&&void 0!==e[l])return!1;if(null===t[l]&&null!==e[l])return!1;if(!r(e[l],t[l],l,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":return Object.keys(t).length>0?l(e,{...t},r,n):o.eq(e,t);default:return i.isObject(e)?"string"!=typeof t||""===t:o.eq(e,t)}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;const i=new Set;for(let a=0;a<t.length;a++){const o=t[a];let l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let s=!1;if(r(e[c],o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function s(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,(function e(t,n,i,a,o,c){const s=r(t,n,i,a,o,c);return void 0!==s?Boolean(s):l(t,n,e,c)}),new Map)},t.isSetMatch=s},8351:function(e,t,r){var n;!function(){"use strict";var i,a=1e9,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,c="[DecimalError] ",s=c+"Invalid argument: ",u=c+"Exponent out of range: ",f=Math.floor,d=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=1e7,y=9007199254740991,v=f(1286742750677284.5),m={};function g(e,t){var r,n,i,a,o,c,s,u,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?k(t,d):t;if(s=e.d,u=t.d,o=e.e,i=t.e,s=s.slice(),a=o-i){for(a<0?(n=s,a=-a,c=u.length):(n=u,i=o,c=s.length),a>(c=(o=Math.ceil(d/7))>c?o+1:c+1)&&(a=c,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((c=s.length)-(a=u.length)<0&&(a=c,n=u,u=s,s=n),r=0;a;)r=(s[--a]=s[a]+u[a]+r)/h|0,s[a]%=h;for(r&&(s.unshift(r),++i),c=s.length;0==s[--c];)s.pop();return t.d=s,t.e=i,l?k(t,d):t}function b(e,t,r){if(e!==~~e||e<t||e>r)throw Error(s+e)}function x(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=A(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=A(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}m.absoluteValue=m.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},m.comparedTo=m.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(t=0,r=(n=a.d.length)<(i=e.d.length)?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1},m.decimalPlaces=m.dp=function(){var e=this,t=e.d.length-1,r=7*(t-e.e);if(t=e.d[t])for(;t%10==0;t/=10)r--;return r<0?0:r},m.dividedBy=m.div=function(e){return w(this,new this.constructor(e))},m.dividedToIntegerBy=m.idiv=function(e){var t=this.constructor;return k(w(this,new t(e),0,1),t.precision)},m.equals=m.eq=function(e){return!this.cmp(e)},m.exponent=function(){return P(this)},m.greaterThan=m.gt=function(e){return this.cmp(e)>0},m.greaterThanOrEqualTo=m.gte=function(e){return this.cmp(e)>=0},m.isInteger=m.isint=function(){return this.e>this.d.length-2},m.isNegative=m.isneg=function(){return this.s<0},m.isPositive=m.ispos=function(){return this.s>0},m.isZero=function(){return 0===this.s},m.lessThan=m.lt=function(e){return this.cmp(e)<0},m.lessThanOrEqualTo=m.lte=function(e){return this.cmp(e)<1},m.logarithm=m.log=function(e){var t,r=this,n=r.constructor,a=n.precision,o=a+5;if(void 0===e)e=new n(10);else if((e=new n(e)).s<1||e.eq(i))throw Error(c+"NaN");if(r.s<1)throw Error(c+(r.s?"NaN":"-Infinity"));return r.eq(i)?new n(0):(l=!1,t=w(j(r,o),j(e,o),o),l=!0,k(t,a))},m.minus=m.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?M(t,e):g(t,(e.s=-e.s,e))},m.modulo=m.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(!(e=new n(e)).s)throw Error(c+"NaN");return r.s?(l=!1,t=w(r,e,0,1).times(e),l=!0,r.minus(t)):k(new n(r),i)},m.naturalExponential=m.exp=function(){return O(this)},m.naturalLogarithm=m.ln=function(){return j(this)},m.negated=m.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},m.plus=m.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?g(t,e):M(t,(e.s=-e.s,e))},m.precision=m.sd=function(e){var t,r,n,i=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(s+e);if(t=P(i)+1,r=7*(n=i.d.length-1)+1,n=i.d[n]){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},m.squareRoot=m.sqrt=function(){var e,t,r,n,i,a,o,s=this,u=s.constructor;if(s.s<1){if(!s.s)return new u(0);throw Error(c+"NaN")}for(e=P(s),l=!1,0==(i=Math.sqrt(+s))||i==1/0?(((t=x(s.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new u(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new u(i.toString()),i=o=(r=u.precision)+3;;)if(n=(a=n).plus(w(s,a,o+2)).times(.5),x(a.d).slice(0,o)===(t=x(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(k(a,r+1,0),a.times(a).eq(s)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,k(n,r)},m.times=m.mul=function(e){var t,r,n,i,a,o,c,s,u,f=this,d=f.constructor,p=f.d,y=(e=new d(e)).d;if(!f.s||!e.s)return new d(0);for(e.s*=f.s,r=f.e+e.e,(s=p.length)<(u=y.length)&&(a=p,p=y,y=a,o=s,s=u,u=o),a=[],n=o=s+u;n--;)a.push(0);for(n=u;--n>=0;){for(t=0,i=s+n;i>n;)c=a[i]+y[n]*p[i-n-1]+t,a[i--]=c%h|0,t=c/h|0;a[i]=(a[i]+t)%h|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?k(e,d.precision):e},m.toDecimalPlaces=m.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),void 0===e?r:(b(e,0,a),void 0===t?t=n.rounding:b(t,0,8),k(r,e+P(r)+1,t))},m.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=T(n,!0):(b(e,0,a),void 0===t?t=i.rounding:b(t,0,8),r=T(n=k(new i(n),e+1,t),!0,e+1)),r},m.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?T(i):(b(e,0,a),void 0===t?t=o.rounding:b(t,0,8),r=T((n=k(new o(i),e+P(i)+1,t)).abs(),!1,e+P(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)},m.toInteger=m.toint=function(){var e=this,t=e.constructor;return k(new t(e),P(e)+1,t.rounding)},m.toNumber=function(){return+this},m.toPower=m.pow=function(e){var t,r,n,a,o,s,u=this,d=u.constructor,p=+(e=new d(e));if(!e.s)return new d(i);if(!(u=new d(u)).s){if(e.s<1)throw Error(c+"Infinity");return u}if(u.eq(i))return u;if(n=d.precision,e.eq(i))return k(u,n);if(s=(t=e.e)>=(r=e.d.length-1),o=u.s,s){if((r=p<0?-p:p)<=y){for(a=new d(i),t=Math.ceil(n/7+4),l=!1;r%2&&D((a=a.times(u)).d,t),0!==(r=f(r/2));)D((u=u.times(u)).d,t);return l=!0,e.s<0?new d(i).div(a):k(a,n)}}else if(o<0)throw Error(c+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,u.s=1,l=!1,a=e.times(j(u,n+12)),l=!0,(a=O(a)).s=o,a},m.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?n=T(i,(r=P(i))<=o.toExpNeg||r>=o.toExpPos):(b(e,1,a),void 0===t?t=o.rounding:b(t,0,8),n=T(i=k(new o(i),e,t),e<=(r=P(i))||r<=o.toExpNeg,e)),n},m.toSignificantDigits=m.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(b(e,1,a),void 0===t?t=r.rounding:b(t,0,8)),k(new r(this),e,t)},m.toString=m.valueOf=m.val=m.toJSON=function(){var e=this,t=P(e),r=e.constructor;return T(e,t<=r.toExpNeg||t>=r.toExpPos)};var w=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%h|0,n=r/h|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=n*h+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,s,u,f,d,p,y,v,m,g,b,x,w,O,E,A,j,S,M=n.constructor,T=n.s==i.s?1:-1,D=n.d,C=i.d;if(!n.s)return new M(n);if(!i.s)throw Error(c+"Division by zero");for(s=n.e-i.e,j=C.length,E=D.length,v=(y=new M(T)).d=[],u=0;C[u]==(D[u]||0);)++u;if(C[u]>(D[u]||0)&&--s,(x=null==a?a=M.precision:o?a+(P(n)-P(i))+1:a)<0)return new M(0);if(x=x/7+2|0,u=0,1==j)for(f=0,C=C[0],x++;(u<E||f)&&x--;u++)w=f*h+(D[u]||0),v[u]=w/C|0,f=w%C|0;else{for((f=h/(C[0]+1)|0)>1&&(C=e(C,f),D=e(D,f),j=C.length,E=D.length),O=j,g=(m=D.slice(0,j)).length;g<j;)m[g++]=0;(S=C.slice()).unshift(0),A=C[0],C[1]>=h/2&&++A;do{f=0,(l=t(C,m,j,g))<0?(b=m[0],j!=g&&(b=b*h+(m[1]||0)),(f=b/A|0)>1?(f>=h&&(f=h-1),1==(l=t(d=e(C,f),m,p=d.length,g=m.length))&&(f--,r(d,j<p?S:C,p))):(0==f&&(l=f=1),d=C.slice()),(p=d.length)<g&&d.unshift(0),r(m,d,g),-1==l&&(l=t(C,m,j,g=m.length))<1&&(f++,r(m,j<g?S:C,g)),g=m.length):0===l&&(f++,m=[0]),v[u++]=f,l&&m[0]?m[g++]=D[O]||0:(m=[D[O]],g=1)}while((O++<E||void 0!==m[0])&&x--)}return v[0]||v.shift(),y.e=s,k(y,o?a+P(y)+1:a)}}();function O(e,t){var r,n,a,o,c,s=0,f=0,p=e.constructor,h=p.precision;if(P(e)>16)throw Error(u+P(e));if(!e.s)return new p(i);for(null==t?(l=!1,c=h):c=t,o=new p(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(c+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=a=new p(i),p.precision=c;;){if(n=k(n.times(e),c),r=r.times(++s),x((o=a.plus(w(n,r,c))).d).slice(0,c)===x(a.d).slice(0,c)){for(;f--;)a=k(a.times(a),c);return p.precision=h,null==t?(l=!0,k(a,h)):a}a=o}}function P(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function E(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(c+"LN10 precision limit exceeded");return k(new e(e.LN10),t)}function A(e){for(var t="";e--;)t+="0";return t}function j(e,t){var r,n,a,o,s,u,f,d,p,h=1,y=e,v=y.d,m=y.constructor,g=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==t?(l=!1,d=g):d=t,y.eq(10))return null==t&&(l=!0),E(m,d);if(d+=10,m.precision=d,n=(r=x(v)).charAt(0),o=P(y),!(Math.abs(o)<15e14))return f=E(m,d+2,g).times(o+""),y=j(new m(n+"."+r.slice(1)),d-10).plus(f),m.precision=g,null==t?(l=!0,k(y,g)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=x((y=y.times(e)).d)).charAt(0),h++;for(o=P(y),n>1?(y=new m("0."+r),o++):y=new m(n+"."+r.slice(1)),u=s=y=w(y.minus(i),y.plus(i),d),p=k(y.times(y),d),a=3;;){if(s=k(s.times(p),d),x((f=u.plus(w(s,new m(a),d))).d).slice(0,d)===x(u.d).slice(0,d))return u=u.times(2),0!==o&&(u=u.plus(E(m,d+2,g).times(o+""))),u=w(u,new m(h),d),m.precision=g,null==t?(l=!0,k(u,g)):u;u=f,a+=2}}function S(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=f(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>v||e.e<-v))throw Error(u+r)}else e.s=0,e.e=0,e.d=[0];return e}function k(e,t,r){var n,i,a,o,c,s,p,y,m=e.d;for(o=1,a=m[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,p=m[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=m.length))return e;for(p=a=m[y],o=1;a>=10;a/=10)o++;i=(n%=7)-7+o}if(void 0!==r&&(c=p/(a=d(10,o-i-1))%10|0,s=t<0||void 0!==m[y+1]||p%a,s=r<4?(c||s)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||s||6==r&&(n>0?i>0?p/d(10,o-i):0:m[y-1])%10&1||r==(e.s<0?8:7))),t<1||!m[0])return s?(a=P(e),m.length=1,t=t-a-1,m[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(m.length=1,m[0]=e.e=e.s=0),e;if(0==n?(m.length=y,a=1,y--):(m.length=y+1,a=d(10,7-n),m[y]=i>0?(p/d(10,o-i)%d(10,i)|0)*a:0),s)for(;;){if(0==y){(m[0]+=a)==h&&(m[0]=1,++e.e);break}if(m[y]+=a,m[y]!=h)break;m[y--]=0,a=1}for(n=m.length;0===m[--n];)m.pop();if(l&&(e.e>v||e.e<-v))throw Error(u+P(e));return e}function M(e,t){var r,n,i,a,o,c,s,u,f,d,p=e.constructor,y=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),l?k(t,y):t;if(s=e.d,d=t.d,n=t.e,u=e.e,s=s.slice(),o=u-n){for((f=o<0)?(r=s,o=-o,c=d.length):(r=d,n=u,c=s.length),o>(i=Math.max(Math.ceil(y/7),c)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=s.length)<(c=d.length))&&(c=i),i=0;i<c;i++)if(s[i]!=d[i]){f=s[i]<d[i];break}o=0}for(f&&(r=s,s=d,d=r,t.s=-t.s),c=s.length,i=d.length-c;i>0;--i)s[c++]=0;for(i=d.length;i>o;){if(s[--i]<d[i]){for(a=i;a&&0===s[--a];)s[a]=h-1;--s[a],s[i]+=h}s[i]-=d[i]}for(;0===s[--c];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,l?k(t,y):t):new p(0)}function T(e,t,r){var n,i=P(e),a=x(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+A(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+A(-i-1)+a,r&&(n=r-o)>0&&(a+=A(n))):i>=o?(a+=A(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+A(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=A(n))),e.s<0?"-"+a:a}function D(e,t){if(e.length>t)return e.length=t,!0}function C(e){if(!e||"object"!=typeof e)throw Error(c+"Object expected");var t,r,n,i=["precision",1,a,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(!(f(n)===n&&n>=i[t+1]&&n<=i[t+2]))throw Error(s+r+": "+n);this[r]=n}if(void 0!==(n=e[r="LN10"])){if(n!=Math.LN10)throw Error(s+r+": "+n);this[r]=new this(n)}return this}o=function e(t){var r,n,i;function a(e){var t=this;if(!(t instanceof a))return new a(e);if(t.constructor=a,e instanceof a)return t.s=e.s,t.e=e.e,void(t.d=(e=e.d)?e.slice():e);if("number"==typeof e){if(0*e!=0)throw Error(s+e);if(e>0)t.s=1;else{if(!(e<0))return t.s=0,t.e=0,void(t.d=[0]);e=-e,t.s=-1}return e===~~e&&e<1e7?(t.e=0,void(t.d=[e])):S(t,e.toString())}if("string"!=typeof e)throw Error(s+e);if(45===e.charCodeAt(0)?(e=e.slice(1),t.s=-1):t.s=1,!p.test(e))throw Error(s+e);S(t,e)}if(a.prototype=m,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=C,void 0===t&&(t={}),t)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o),o.default=o.Decimal=o,i=new o(1),void 0===(n=function(){return o}.call(t,r,t,e))||(e.exports=n)}()},8493:(e,t,r)=>{"use strict";var n=r(5442);var i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,c=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,u=n[1];return l((function(){i.value=r,i.getSnapshot=t,s(i)&&u({inst:i})}),[e,r,t]),o((function(){return s(i)&&u({inst:i}),e((function(){s(i)&&u({inst:i})}))}),[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},8509:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},8695:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e)return!1;if(null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){const t=e[Symbol.toStringTag];if(null==t)return!1;return!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},8805:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){const r=new Map;for(let n=0;n<e.length;n++){const i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},8919:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(1366);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},9181:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},9184:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},9242:(e,t,r)=>{"use strict";e.exports=r(2162)},9341:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(993),i=r(6502);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},9467:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3964),i=r(9184);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,((r,a,o,l)=>{const c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{const t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{const t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}}))}},9888:(e,t,r)=>{"use strict";e.exports=r(8493)}},i={};function a(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}};return n[e].call(r.exports,r,r.exports,a),r.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return(()=>{"use strict";a.r(o),a.d(o,{Area:()=>mk,AreaChart:()=>GD,Bar:()=>cE,BarChart:()=>KT,Brush:()=>VA,CartesianAxis:()=>qj,CartesianGrid:()=>pS,Cell:()=>zg,ComposedChart:()=>tC,Cross:()=>ul,Curve:()=>il,Customized:()=>Vb,DefaultLegendContent:()=>Le,DefaultTooltipContent:()=>po,Dot:()=>Jb,ErrorBar:()=>TM,Funnel:()=>BC,FunnelChart:()=>WC,Global:()=>Oo,Label:()=>Db,LabelList:()=>Ub,Layer:()=>V,Legend:()=>oo,Line:()=>WS,LineChart:()=>LT,Pie:()=>MO,PieChart:()=>$T,PolarAngleAxis:()=>fw,PolarGrid:()=>Mx,PolarRadiusAxis:()=>Zx,Polygon:()=>Gb,Radar:()=>hP,RadarChart:()=>$D,RadialBar:()=>uA,RadialBarChart:()=>QD,Rectangle:()=>Yl,ReferenceArea:()=>Cj,ReferenceDot:()=>Ej,ReferenceLine:()=>vj,ResponsiveContainer:()=>Kg,Sankey:()=>UD,Scatter:()=>rM,ScatterChart:()=>qD,Sector:()=>tc,SunburstChart:()=>pC,Surface:()=>W,Symbols:()=>De,Text:()=>mb,Tooltip:()=>Dg,Trapezoid:()=>jw,Treemap:()=>hD,XAxis:()=>fM,YAxis:()=>bM,ZAxis:()=>Ck,getNiceTickValues:()=>Rp,useActiveTooltipDataPoints:()=>_O,useActiveTooltipLabel:()=>CO,useChartHeight:()=>Gi,useChartWidth:()=>Yi,useOffset:()=>IO,usePlotArea:()=>NO});var e={};a.r(e),a.d(e,{scaleBand:()=>hc,scaleDiverging:()=>sp,scaleDivergingLog:()=>up,scaleDivergingPow:()=>dp,scaleDivergingSqrt:()=>pp,scaleDivergingSymlog:()=>fp,scaleIdentity:()=>ru,scaleImplicit:()=>dc,scaleLinear:()=>tu,scaleLog:()=>fu,scaleOrdinal:()=>pc,scalePoint:()=>vc,scalePow:()=>xu,scaleQuantile:()=>Cu,scaleQuantize:()=>Iu,scaleRadial:()=>Pu,scaleSequential:()=>rp,scaleSequentialLog:()=>np,scaleSequentialPow:()=>ap,scaleSequentialQuantile:()=>lp,scaleSequentialSqrt:()=>op,scaleSequentialSymlog:()=>ip,scaleSqrt:()=>wu,scaleSymlog:()=>yu,scaleThreshold:()=>Nu,scaleTime:()=>Jd,scaleUtc:()=>Qd,tickFormat:()=>Qs});var t=a(5442);function r(e){var t,n,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=r(e[t]))&&(i&&(i+=" "),i+=n)}else for(n in e)e[n]&&(i&&(i+=" "),i+=n);return i}function n(){for(var e,t,n=0,i="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=r(e))&&(i&&(i+=" "),i+=t);return i}var i=a(305),l=a.n(i),c=a(2751),s=e=>0===e?0:e>0?1:-1,u=e=>"number"==typeof e&&e!=+e,f=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,d=e=>("number"==typeof e||e instanceof Number)&&!u(e),p=e=>d(e)||"string"==typeof e,h=0,y=e=>{var t=++h;return"".concat(e||"").concat(t)},v=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!d(e)&&"string"!=typeof e)return n;if(f(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return u(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},m=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},g=(e,t)=>d(e)&&d(t)?r=>e+r*(t-e):()=>t;function b(e,t,r){return d(e)&&d(t)?e+r*(t-e):t}function x(e,t,r){if(e&&e.length)return e.find((e=>e&&("function"==typeof t?t(e):l()(e,t))===r))}var w=e=>{if(!e||!e.length)return null;for(var t=e.length,r=0,n=0,i=0,a=0,o=1/0,l=-1/0,c=0,s=0,u=0;u<t;u++)r+=c=e[u].cx||0,n+=s=e[u].cy||0,i+=c*s,a+=c*c,o=Math.min(o,c),l=Math.max(l,c);var f=t*a!=r*r?(t*i-r*n)/(t*a-r*r):0;return{xmin:o,xmax:l,a:f,b:(n-f*r)/t}},O=e=>null==e,P=e=>O(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),E=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"];function A(e){return"string"==typeof e&&E.includes(e)}var j=["points","pathLength"],S={svg:["viewBox","children"],polygon:j,polyline:j},k=(e,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,t.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach((e=>{A(e)&&(i[e]=r||(t=>n[e](n,t)))})),i},M=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach((i=>{var a=e[i];A(i)&&"function"==typeof a&&(n||(n={}),n[i]=((e,t,r)=>n=>(e(t,r,n),null))(a,t,r))})),n},T=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"];function D(e){return"string"==typeof e&&T.includes(e)}function C(e){var t=Object.entries(e).filter((e=>{var[t]=e;return D(t)}));return Object.fromEntries(t)}var I=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",N=null,_=null,L=e=>{if(e===N&&Array.isArray(_))return _;var r=[];return t.Children.forEach(e,(e=>{O(e)||((0,c.isFragment)(e)?r=r.concat(L(e.props.children)):r.push(e))})),_=r,N=e,r};function R(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map((e=>I(e))):[I(t)],L(e).forEach((e=>{var t=l()(e,"type.displayName")||l()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)})),r}var K=e=>!e||"object"!=typeof e||!("clipDot"in e)||Boolean(e.clipDot),z=(e,r,n)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var i=e;if((0,t.isValidElement)(e)&&(i=e.props),"object"!=typeof i&&"function"!=typeof i)return null;var a={};return Object.keys(i).forEach((e=>{var t;((e,t,r,n)=>{var i;if("symbol"==typeof t||"number"==typeof t)return!0;var a=null!==(i=n&&(null==S?void 0:S[n]))&&void 0!==i?i:[],o=t.startsWith("data-"),l="function"!=typeof e&&(Boolean(n)&&a.includes(t)||D(t)),c=Boolean(r)&&A(t);return o||l||c})(null===(t=i)||void 0===t?void 0:t[e],e,r,n)&&(a[e]=i[e])})),a},B=["children","width","height","viewBox","className","style","title","desc"];function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},F.apply(null,arguments)}var W=(0,t.forwardRef)(((e,r)=>{var{children:i,width:a,height:o,viewBox:l,className:c,style:s,title:u,desc:f}=e,d=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,B),p=l||{width:a,height:o,x:0,y:0},h=n("recharts-surface",c);return t.createElement("svg",F({},z(d,!0,"svg"),{className:h,width:a,height:o,style:s,viewBox:"".concat(p.x," ").concat(p.y," ").concat(p.width," ").concat(p.height),ref:r}),t.createElement("title",null,u),t.createElement("desc",null,f),i)})),U=["children","className"];function X(){return X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},X.apply(null,arguments)}var V=t.forwardRef(((e,r)=>{var{children:i,className:a}=e,o=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,U),l=n("recharts-layer",a);return t.createElement("g",X({className:l},z(o,!0),{ref:r}),i)})),$=a(6003),H=(0,t.createContext)(null);Math.abs,Math.atan2;const q=Math.cos,Y=(Math.max,Math.min,Math.sin),G=Math.sqrt,Z=Math.PI,J=2*Z;const Q={draw(e,t){const r=G(t/Z);e.moveTo(r,0),e.arc(0,0,r,0,J)}},ee={draw(e,t){const r=G(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},te=G(1/3),re=2*te,ne={draw(e,t){const r=G(t/re),n=r*te;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},ie={draw(e,t){const r=G(t),n=-r/2;e.rect(n,n,r,r)}},ae=Y(Z/10)/Y(7*Z/10),oe=Y(J/10)*ae,le=-q(J/10)*ae,ce={draw(e,t){const r=G(.8908130915292852*t),n=oe*r,i=le*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){const a=J*t/5,o=q(a),l=Y(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},se=G(3),ue={draw(e,t){const r=-G(t/(3*se));e.moveTo(0,2*r),e.lineTo(-se*r,-r),e.lineTo(se*r,-r),e.closePath()}},fe=-.5,de=G(3)/2,pe=1/G(12),he=3*(pe/2+1),ye={draw(e,t){const r=G(t/he),n=r/2,i=r*pe,a=n,o=r*pe+r,l=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(l,c),e.lineTo(fe*n-de*i,de*n+fe*i),e.lineTo(fe*a-de*o,de*a+fe*o),e.lineTo(fe*l-de*c,de*l+fe*c),e.lineTo(fe*n+de*i,fe*i-de*n),e.lineTo(fe*a+de*o,fe*o-de*a),e.lineTo(fe*l+de*c,fe*c-de*l),e.closePath()}};function ve(e){return function(){return e}}const me=Math.PI,ge=2*me,be=1e-6,xe=ge-be;function we(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class Oe{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?we:function(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return we;const r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e=+e,t=+t,r=+r,n=+n,(i=+i)<0)throw new Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,c=n-t,s=a-e,u=o-t,f=s*s+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>be)if(Math.abs(u*l-c*s)>be&&i){let d=r-a,p=n-o,h=l*l+c*c,y=d*d+p*p,v=Math.sqrt(h),m=Math.sqrt(f),g=i*Math.tan((me-Math.acos((h+f-y)/(2*v*m)))/2),b=g/m,x=g/v;Math.abs(b-1)>be&&this._append`L${e+b*s},${t+b*u}`,this._append`A${i},${i},0,0,${+(u*d>s*p)},${this._x1=e+x*l},${this._y1=t+x*c}`}else this._append`L${this._x1=e},${this._y1=t}`;else;}arc(e,t,r,n,i,a){if(e=+e,t=+t,a=!!a,(r=+r)<0)throw new Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),c=e+o,s=t+l,u=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${c},${s}`:(Math.abs(this._x1-c)>be||Math.abs(this._y1-s)>be)&&this._append`L${c},${s}`,r&&(f<0&&(f=f%ge+ge),f>xe?this._append`A${r},${r},0,1,${u},${e-o},${t-l}A${r},${r},0,1,${u},${this._x1=c},${this._y1=s}`:f>be&&this._append`A${r},${r},0,${+(f>=me)},${u},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function Pe(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{const e=Math.floor(r);if(!(e>=0))throw new RangeError(`invalid digits: ${r}`);t=e}return e},()=>new Oe(t)}G(3),G(3);var Ee=["type","size","sizeType"];function Ae(){return Ae=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ae.apply(null,arguments)}function je(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?je(Object(r),!0).forEach((function(t){ke(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):je(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ke(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Me={symbolCircle:Q,symbolCross:ee,symbolDiamond:ne,symbolSquare:ie,symbolStar:ce,symbolTriangle:ue,symbolWye:ye},Te=Math.PI/180,De=e=>{var r,i,{type:a="circle",size:o=64,sizeType:l="area"}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ee),s=Se(Se({},c),{},{type:a,size:o,sizeType:l}),{className:u,cx:f,cy:d}=s,p=z(s,!0);return f===+f&&d===+d&&o===+o?t.createElement("path",Ae({},p,{className:n("recharts-symbols",u),transform:"translate(".concat(f,", ").concat(d,")"),d:(r=(e=>{var t="symbol".concat(P(e));return Me[t]||Q})(a),i=function(e,t){let r=null,n=Pe(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:ve(e||Q),t="function"==typeof t?t:ve(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:ve(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:ve(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i}().type(r).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return.5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*Te;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(o,l,a)),i())})):null};function Ce(){return Ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ce.apply(null,arguments)}function Ie(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ne(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}De.registerSymbol=(e,t)=>{Me["symbol".concat(P(e))]=t};var _e=32;class Le extends t.PureComponent{renderIcon(e,r){var{inactiveColor:n}=this.props,i=16,a=_e/6,o=_e/3,l=e.inactive?n:e.color,c=null!=r?r:e.type;if("none"===c)return null;if("plainline"===c)return t.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:i,x2:_e,y2:i,className:"recharts-legend-icon"});if("line"===c)return t.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(i,"h").concat(o,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*o,",").concat(i,"\n            H").concat(_e,"M").concat(2*o,",").concat(i,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(o,",").concat(i),className:"recharts-legend-icon"});if("rect"===c)return t.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(_e,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(t.isValidElement(e.legendIcon)){var s=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ie(Object(r),!0).forEach((function(t){Ne(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ie(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e);return delete s.legendIcon,t.cloneElement(e.legendIcon,s)}return t.createElement(De,{fill:l,cx:i,cy:i,size:_e,sizeType:"diameter",type:c})}renderItems(){var{payload:e,iconSize:r,layout:i,formatter:a,inactiveColor:o,iconType:l}=this.props,c={x:0,y:0,width:_e,height:_e},s={display:"horizontal"===i?"inline-block":"block",marginRight:10},u={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map(((e,i)=>{var f=e.formatter||a,d=n({"recharts-legend-item":!0,["legend-item-".concat(i)]:!0,inactive:e.inactive});if("none"===e.type)return null;var p=e.inactive?o:e.color,h=f?f(e.value,e,i):e.value;return t.createElement("li",Ce({className:d,style:s,key:"legend-item-".concat(i)},M(this.props,e,i)),t.createElement(W,{width:r,height:r,viewBox:c,style:u,"aria-label":"".concat(h," legend icon")},this.renderIcon(e,l)),t.createElement("span",{className:"recharts-legend-item-text",style:{color:p}},h))}))}render(){var{payload:e,layout:r,align:n}=this.props;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?n:"left"};return t.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}Ne(Le,"displayName","Legend"),Ne(Le,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var Re=a(1081),Ke=a.n(Re);function ze(e,t,r){return!0===t?Ke()(e,r):"function"==typeof t?Ke()(e,t):e}var Be=a(9242),Fe=(0,t.createContext)(null),We=e=>e,Ue=()=>{var e=(0,t.useContext)(Fe);return e?e.store.dispatch:We},Xe=()=>{},Ve=()=>Xe,$e=(e,t)=>e===t;function He(e){var r=(0,t.useContext)(Fe);return(0,Be.useSyncExternalStoreWithSelector)(r?r.subscription.addNestedSub:Ve,r?r.store.getState:Xe,r?r.store.getState:Xe,r?e:Xe,$e)}function qe(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}var Ye=e=>Array.isArray(e)?e:[e];function Ge(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every((e=>"function"==typeof e))){const r=e.map((e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e)).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}Symbol(),Object.getPrototypeOf({});var Ze="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function Je(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let i,a=0;function o(){let t=r;const{length:o}=arguments;for(let e=0,r=o;e<r;e++){const r=arguments[e];if("function"==typeof r||"object"==typeof r&&null!==r){let e=t.o;null===e&&(t.o=e=new WeakMap);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}else{let e=t.p;null===e&&(t.p=e=new Map);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}}const l=t;let c;if(1===t.s)c=t.v;else if(c=e.apply(null,arguments),a++,n){const e=i?.deref?.()??i;null!=e&&n(e,c)&&(c=e,0!==a&&a--);i="object"==typeof c&&null!==c||"function"==typeof c?new Ze(c):c}return l.s=1,l.v=c,c}return o.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}function Qe(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),qe(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);const l={...r,...a},{memoize:c,memoizeOptions:s=[],argsMemoize:u=Je,argsMemoizeOptions:f=[],devModeChecks:d={}}=l,p=Ye(s),h=Ye(f),y=Ge(e),v=c((function(){return n++,o.apply(null,arguments)}),...p);const m=u((function(){i++;const e=function(e,t){const r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e),t}),...h);return Object.assign(m,{resultFunc:o,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:c,argsMemoize:u})};return Object.assign(n,{withTypes:()=>n}),n}var et=Qe(Je),tt=Object.assign(((e,t=et)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e),n=t(r.map((t=>e[t])),((...e)=>e.reduce(((e,t,n)=>(e[r[n]]=t,e)),{})));return n}),{withTypes:()=>tt}),rt=a(184),nt=a.n(rt),it=e=>e.legend.settings,at=et([e=>e.legend.payload,it],((e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?nt()(n,r):n}));var ot=1;function lt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[r,n]=(0,t.useState)({height:0,left:0,top:0,width:0}),i=(0,t.useCallback)((e=>{if(null!=e){var t=e.getBoundingClientRect(),i={height:t.height,left:t.left,top:t.top,width:t.width};(Math.abs(i.height-r.height)>ot||Math.abs(i.left-r.left)>ot||Math.abs(i.top-r.top)>ot||Math.abs(i.width-r.width)>ot)&&n({height:i.height,left:i.left,top:i.top,width:i.width})}}),[r.width,r.height,r.top,r.left,...e]);return[r,i]}function ct(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];throw Error("[Immer] minified error nr: "+e+(r.length?" "+r.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function st(e){return!!e&&!!e[Jt]}function ut(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===Qt}(e)||Array.isArray(e)||!!e[Zt]||!!(null===(t=e.constructor)||void 0===t?void 0:t[Zt])||mt(e)||gt(e))}function ft(e,t,r){void 0===r&&(r=!1),0===dt(e)?(r?Object.keys:er)(e).forEach((function(n){r&&"symbol"==typeof n||t(n,e[n],e)})):e.forEach((function(r,n){return t(n,r,e)}))}function dt(e){var t=e[Jt];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:mt(e)?2:gt(e)?3:0}function pt(e,t){return 2===dt(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ht(e,t){return 2===dt(e)?e.get(t):e[t]}function yt(e,t,r){var n=dt(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function vt(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function mt(e){return Ht&&e instanceof Map}function gt(e){return qt&&e instanceof Set}function bt(e){return e.o||e.t}function xt(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=tr(e);delete t[Jt];for(var r=er(t),n=0;n<r.length;n++){var i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(Object.getPrototypeOf(e),t)}function wt(e,t){return void 0===t&&(t=!1),Pt(e)||st(e)||!ut(e)||(dt(e)>1&&(e.set=e.add=e.clear=e.delete=Ot),Object.freeze(e),t&&ft(e,(function(e,t){return wt(t,!0)}),!0)),e}function Ot(){ct(2)}function Pt(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function Et(e){var t=rr[e];return t||ct(18,e),t}function At(e,t){rr[e]||(rr[e]=t)}function jt(){return Vt}function St(e,t){t&&(Et("Patches"),e.u=[],e.s=[],e.v=t)}function kt(e){Mt(e),e.p.forEach(Dt),e.p=null}function Mt(e){e===Vt&&(Vt=e.l)}function Tt(e){return Vt={p:[],l:Vt,h:e,m:!0,_:0}}function Dt(e){var t=e[Jt];0===t.i||1===t.i?t.j():t.g=!0}function Ct(e,t){t._=t.p.length;var r=t.p[0],n=void 0!==e&&e!==r;return t.h.O||Et("ES5").S(t,e,n),n?(r[Jt].P&&(kt(t),ct(4)),ut(e)&&(e=It(t,e),t.l||_t(t,e)),t.u&&Et("Patches").M(r[Jt].t,e,t.u,t.s)):e=It(t,r,[]),kt(t),t.u&&t.v(t.u,t.s),e!==Gt?e:void 0}function It(e,t,r){if(Pt(t))return t;var n=t[Jt];if(!n)return ft(t,(function(i,a){return Nt(e,n,t,i,a,r)}),!0),t;if(n.A!==e)return t;if(!n.P)return _t(e,n.t,!0),n.t;if(!n.I){n.I=!0,n.A._--;var i=4===n.i||5===n.i?n.o=xt(n.k):n.o,a=i,o=!1;3===n.i&&(a=new Set(i),i.clear(),o=!0),ft(a,(function(t,a){return Nt(e,n,i,t,a,r,o)})),_t(e,i,!1),r&&e.u&&Et("Patches").N(n,r,e.u,e.s)}return n.o}function Nt(e,t,r,n,i,a,o){if(st(i)){var l=It(e,i,a&&t&&3!==t.i&&!pt(t.R,n)?a.concat(n):void 0);if(yt(r,n,l),!st(l))return;e.m=!1}else o&&r.add(i);if(ut(i)&&!Pt(i)){if(!e.h.D&&e._<1)return;It(e,i),t&&t.A.l||_t(e,i)}}function _t(e,t,r){void 0===r&&(r=!1),!e.l&&e.h.D&&e.m&&wt(t,r)}function Lt(e,t){var r=e[Jt];return(r?bt(r):e)[t]}function Rt(e,t){if(t in e)for(var r=Object.getPrototypeOf(e);r;){var n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=Object.getPrototypeOf(r)}}function Kt(e){e.P||(e.P=!0,e.l&&Kt(e.l))}function zt(e){e.o||(e.o=xt(e.t))}function Bt(e,t,r){var n=mt(t)?Et("MapSet").F(t,r):gt(t)?Et("MapSet").T(t,r):e.O?function(e,t){var r=Array.isArray(e),n={i:r?1:0,A:t?t.A:jt(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},i=n,a=nr;r&&(i=[n],a=ir);var o=Proxy.revocable(i,a),l=o.revoke,c=o.proxy;return n.k=c,n.j=l,c}(t,r):Et("ES5").J(t,r);return(r?r.A:jt()).p.push(n),n}function Ft(e){return st(e)||ct(22,e),function e(t){if(!ut(t))return t;var r,n=t[Jt],i=dt(t);if(n){if(!n.P&&(n.i<4||!Et("ES5").K(n)))return n.t;n.I=!0,r=Wt(t,i),n.I=!1}else r=Wt(t,i);return ft(r,(function(t,i){n&&ht(n.t,t)===i||yt(r,t,e(i))})),3===i?new Set(r):r}(e)}function Wt(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return xt(e)}function Ut(){function e(e,t){var r=i[e];return r?r.enumerable=t:i[e]=r={configurable:!0,enumerable:t,get:function(){var t=this[Jt];return nr.get(t,e)},set:function(t){var r=this[Jt];nr.set(r,e,t)}},r}function t(e){for(var t=e.length-1;t>=0;t--){var i=e[t][Jt];if(!i.P)switch(i.i){case 5:n(i)&&Kt(i);break;case 4:r(i)&&Kt(i)}}}function r(e){for(var t=e.t,r=e.k,n=er(r),i=n.length-1;i>=0;i--){var a=n[i];if(a!==Jt){var o=t[a];if(void 0===o&&!pt(t,a))return!0;var l=r[a],c=l&&l[Jt];if(c?c.t!==o:!vt(l,o))return!0}}var s=!!t[Jt];return n.length!==er(t).length+(s?0:1)}function n(e){var t=e.k;if(t.length!==e.t.length)return!0;var r=Object.getOwnPropertyDescriptor(t,t.length-1);if(r&&!r.get)return!0;for(var n=0;n<t.length;n++)if(!t.hasOwnProperty(n))return!0;return!1}var i={};At("ES5",{J:function(t,r){var n=Array.isArray(t),i=function(t,r){if(t){for(var n=Array(r.length),i=0;i<r.length;i++)Object.defineProperty(n,""+i,e(i,!0));return n}var a=tr(r);delete a[Jt];for(var o=er(a),l=0;l<o.length;l++){var c=o[l];a[c]=e(c,t||!!a[c].enumerable)}return Object.create(Object.getPrototypeOf(r),a)}(n,t),a={i:n?5:4,A:r?r.A:jt(),P:!1,I:!1,R:{},l:r,t,k:i,o:null,g:!1,C:!1};return Object.defineProperty(i,Jt,{value:a,writable:!0}),i},S:function(e,r,i){i?st(r)&&r[Jt].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var r=t[Jt];if(r){var i=r.t,a=r.k,o=r.R,l=r.i;if(4===l)ft(a,(function(t){t!==Jt&&(void 0!==i[t]||pt(i,t)?o[t]||e(a[t]):(o[t]=!0,Kt(r)))})),ft(i,(function(e){void 0!==a[e]||pt(a,e)||(o[e]=!1,Kt(r))}));else if(5===l){if(n(r)&&(Kt(r),o.length=!0),a.length<i.length)for(var c=a.length;c<i.length;c++)o[c]=!1;else for(var s=i.length;s<a.length;s++)o[s]=!0;for(var u=Math.min(a.length,i.length),f=0;f<u;f++)a.hasOwnProperty(f)||(o[f]=!0),void 0===o[f]&&e(a[f])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?r(e):n(e)}})}var Xt,Vt,$t="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Ht="undefined"!=typeof Map,qt="undefined"!=typeof Set,Yt="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,Gt=$t?Symbol.for("immer-nothing"):((Xt={})["immer-nothing"]=!0,Xt),Zt=$t?Symbol.for("immer-draftable"):"__$immer_draftable",Jt=$t?Symbol.for("immer-state"):"__$immer_state",Qt=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),er="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,tr=Object.getOwnPropertyDescriptors||function(e){var t={};return er(e).forEach((function(r){t[r]=Object.getOwnPropertyDescriptor(e,r)})),t},rr={},nr={get:function(e,t){if(t===Jt)return e;var r=bt(e);if(!pt(r,t))return function(e,t,r){var n,i=Rt(t,r);return i?"value"in i?i.value:null===(n=i.get)||void 0===n?void 0:n.call(e.k):void 0}(e,r,t);var n=r[t];return e.I||!ut(n)?n:n===Lt(e.t,t)?(zt(e),e.o[t]=Bt(e.A.h,n,e)):n},has:function(e,t){return t in bt(e)},ownKeys:function(e){return Reflect.ownKeys(bt(e))},set:function(e,t,r){var n=Rt(bt(e),t);if(null==n?void 0:n.set)return n.set.call(e.k,r),!0;if(!e.P){var i=Lt(bt(e),t),a=null==i?void 0:i[Jt];if(a&&a.t===r)return e.o[t]=r,e.R[t]=!1,!0;if(vt(r,i)&&(void 0!==r||pt(e.t,t)))return!0;zt(e),Kt(e)}return e.o[t]===r&&(void 0!==r||t in e.o)||Number.isNaN(r)&&Number.isNaN(e.o[t])||(e.o[t]=r,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==Lt(e.t,t)||t in e.t?(e.R[t]=!1,zt(e),Kt(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var r=bt(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty:function(){ct(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){ct(12)}},ir={};ft(nr,(function(e,t){ir[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),ir.deleteProperty=function(e,t){return ir.set.call(this,e,t,void 0)},ir.set=function(e,t,r){return nr.set.call(this,e[0],t,r,e[0])};var ar=function(){function e(e){var t=this;this.O=Yt,this.D=!0,this.produce=function(e,r,n){if("function"==typeof e&&"function"!=typeof r){var i=r;r=e;var a=t;return function(e){var t=this;void 0===e&&(e=i);for(var n=arguments.length,o=Array(n>1?n-1:0),l=1;l<n;l++)o[l-1]=arguments[l];return a.produce(e,(function(e){var n;return(n=r).call.apply(n,[t,e].concat(o))}))}}var o;if("function"!=typeof r&&ct(6),void 0!==n&&"function"!=typeof n&&ct(7),ut(e)){var l=Tt(t),c=Bt(t,e,void 0),s=!0;try{o=r(c),s=!1}finally{s?kt(l):Mt(l)}return"undefined"!=typeof Promise&&o instanceof Promise?o.then((function(e){return St(l,n),Ct(e,l)}),(function(e){throw kt(l),e})):(St(l,n),Ct(o,l))}if(!e||"object"!=typeof e){if(void 0===(o=r(e))&&(o=e),o===Gt&&(o=void 0),t.D&&wt(o,!0),n){var u=[],f=[];Et("Patches").M(e,o,u,f),n(u,f)}return o}ct(21,e)},this.produceWithPatches=function(e,r){if("function"==typeof e)return function(r){for(var n=arguments.length,i=Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return t.produceWithPatches(r,(function(t){return e.apply(void 0,[t].concat(i))}))};var n,i,a=t.produce(e,r,(function(e,t){n=e,i=t}));return"undefined"!=typeof Promise&&a instanceof Promise?a.then((function(e){return[e,n,i]})):[a,n,i]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){ut(e)||ct(8),st(e)&&(e=Ft(e));var t=Tt(this),r=Bt(this,e,void 0);return r[Jt].C=!0,Mt(t),r},t.finishDraft=function(e,t){var r=(e&&e[Jt]).A;return St(r,t),Ct(void 0,r)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!Yt&&ct(20),this.O=e},t.applyPatches=function(e,t){var r;for(r=t.length-1;r>=0;r--){var n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));var i=Et("Patches").$;return st(e)?i(e,t):this.produce(e,(function(e){return i(e,t)}))},e}(),or=new ar,lr=or.produce;or.produceWithPatches.bind(or),or.setAutoFreeze.bind(or),or.setUseProxies.bind(or),or.applyPatches.bind(or),or.createDraft.bind(or),or.finishDraft.bind(or);const cr=lr;function sr(e){return sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sr(e)}function ur(e){var t=function(e,t){if("object"!=sr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sr(t)?t:t+""}function fr(e,t,r){return(t=ur(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function pr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dr(Object(r),!0).forEach((function(t){fr(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function hr(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var yr="function"==typeof Symbol&&Symbol.observable||"@@observable",vr=function(){return Math.random().toString(36).substring(7).split("").join(".")},mr={INIT:"@@redux/INIT"+vr(),REPLACE:"@@redux/REPLACE"+vr(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+vr()}};function gr(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function br(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(hr(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(hr(1));return r(br)(e,t)}if("function"!=typeof e)throw new Error(hr(2));var i=e,a=t,o=[],l=o,c=!1;function s(){l===o&&(l=o.slice())}function u(){if(c)throw new Error(hr(3));return a}function f(e){if("function"!=typeof e)throw new Error(hr(4));if(c)throw new Error(hr(5));var t=!0;return s(),l.push(e),function(){if(t){if(c)throw new Error(hr(6));t=!1,s();var r=l.indexOf(e);l.splice(r,1),o=null}}}function d(e){if(!gr(e))throw new Error(hr(7));if(void 0===e.type)throw new Error(hr(8));if(c)throw new Error(hr(9));try{c=!0,a=i(a,e)}finally{c=!1}for(var t=o=l,r=0;r<t.length;r++){(0,t[r])()}return e}return d({type:mr.INIT}),(n={dispatch:d,subscribe:f,getState:u,replaceReducer:function(e){if("function"!=typeof e)throw new Error(hr(10));i=e,d({type:mr.REPLACE})}})[yr]=function(){var e,t=f;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(hr(11));function r(){e.next&&e.next(u())}return r(),{unsubscribe:t(r)}}})[yr]=function(){return this},e},n}function xr(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++){var i=t[n];0,"function"==typeof e[i]&&(r[i]=e[i])}var a,o=Object.keys(r);try{!function(e){Object.keys(e).forEach((function(t){var r=e[t];if(void 0===r(void 0,{type:mr.INIT}))throw new Error(hr(12));if(void 0===r(void 0,{type:mr.PROBE_UNKNOWN_ACTION()}))throw new Error(hr(13))}))}(r)}catch(e){a=e}return function(e,t){if(void 0===e&&(e={}),a)throw a;for(var n=!1,i={},l=0;l<o.length;l++){var c=o[l],s=r[c],u=e[c],f=s(u,t);if(void 0===f){t&&t.type;throw new Error(hr(14))}i[c]=f,n=n||f!==u}return(n=n||o.length!==Object.keys(e).length)?i:e}}function wr(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function Or(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),n=function(){throw new Error(hr(15))},i={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},a=t.map((function(e){return e(i)}));return n=wr.apply(void 0,a)(r.dispatch),pr(pr({},r),{},{dispatch:n})}}}function Pr(e){return function(t){var r=t.dispatch,n=t.getState;return function(t){return function(i){return"function"==typeof i?i(r,n,e):t(i)}}}}var Er=Pr();Er.withExtraArgument=Pr;const Ar=Er;var jr,Sr=(jr=function(e,t){return jr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},jr(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}jr(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),kr=function(e,t){var r,n,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(a){return function(l){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,l])}}},Mr=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e},Tr=Object.defineProperty,Dr=Object.defineProperties,Cr=Object.getOwnPropertyDescriptors,Ir=Object.getOwnPropertySymbols,Nr=Object.prototype.hasOwnProperty,_r=Object.prototype.propertyIsEnumerable,Lr=function(e,t,r){return t in e?Tr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},Rr=function(e,t){for(var r in t||(t={}))Nr.call(t,r)&&Lr(e,r,t[r]);if(Ir)for(var n=0,i=Ir(t);n<i.length;n++){r=i[n];_r.call(t,r)&&Lr(e,r,t[r])}return e},Kr=function(e,t){return Dr(e,Cr(t))},zr=function(e,t,r){return new Promise((function(n,i){var a=function(e){try{l(r.next(e))}catch(e){i(e)}},o=function(e){try{l(r.throw(e))}catch(e){i(e)}},l=function(e){return e.done?n(e.value):Promise.resolve(e.value).then(a,o)};l((r=r.apply(e,t)).next())}))},Br="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?wr:wr.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function Fr(e){if("object"!=typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var r=t;null!==Object.getPrototypeOf(r);)r=Object.getPrototypeOf(r);return t===r}function Wr(e,t){function r(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];if(t){var i=t.apply(void 0,r);if(!i)throw new Error("prepareAction did not return an object");return Rr(Rr({type:e,payload:i.payload},"meta"in i&&{meta:i.meta}),"error"in i&&{error:i.error})}return{type:e,payload:r[0]}}return r.toString=function(){return""+e},r.type=e,r.match=function(t){return t.type===e},r}function Ur(e){return Fr(e)&&"type"in e}var Xr=function(e){function t(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=e.apply(this,r)||this;return Object.setPrototypeOf(i,t.prototype),i}return Sr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Mr([void 0],e[0].concat(this)))):new(t.bind.apply(t,Mr([void 0],e.concat(this))))},t}(Array),Vr=function(e){function t(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=e.apply(this,r)||this;return Object.setPrototypeOf(i,t.prototype),i}return Sr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Mr([void 0],e[0].concat(this)))):new(t.bind.apply(t,Mr([void 0],e.concat(this))))},t}(Array);function $r(e){return ut(e)?cr(e,(function(){})):e}function Hr(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,r=void 0===t||t,n=(e.immutableCheck,e.serializableCheck,e.actionCreatorCheck,new Xr);r&&(!function(e){return"boolean"==typeof e}(r)?n.push(Ar.withExtraArgument(r.extraArgument)):n.push(Ar));0;return n}(e)}}function qr(e){var t,r=Hr(),n=e||{},i=n.reducer,a=void 0===i?void 0:i,o=n.middleware,l=void 0===o?r():o,c=n.devTools,s=void 0===c||c,u=n.preloadedState,f=void 0===u?void 0:u,d=n.enhancers,p=void 0===d?void 0:d;if("function"==typeof a)t=a;else{if(!Fr(a))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=xr(a)}var h=l;"function"==typeof h&&(h=h(r));var y=Or.apply(void 0,h),v=wr;s&&(v=Br(Rr({trace:!1},"object"==typeof s&&s)));var m=new Vr(y),g=m;return Array.isArray(p)?g=Mr([y],p):"function"==typeof p&&(g=p(m)),br(t,f,v.apply(void 0,g))}function Yr(e){var t,r={},n=[],i={addCase:function(e,t){var n="string"==typeof e?e:e.type;if(!n)throw new Error("`builder.addCase` cannot be called with an empty action type");if(n in r)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return r[n]=t,i},addMatcher:function(e,t){return n.push({matcher:e,reducer:t}),i},addDefaultCase:function(e){return t=e,i}};return e(i),[r,n,t]}function Gr(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var r,n="function"==typeof e.initialState?e.initialState:$r(e.initialState),i=e.reducers||{},a=Object.keys(i),o={},l={},c={};function s(){var t="function"==typeof e.extraReducers?Yr(e.extraReducers):[e.extraReducers],r=t[0],i=void 0===r?{}:r,a=t[1],o=void 0===a?[]:a,c=t[2],s=void 0===c?void 0:c,u=Rr(Rr({},i),l);return function(e,t,r,n){void 0===r&&(r=[]);var i,a="function"==typeof t?Yr(t):[t,r,n],o=a[0],l=a[1],c=a[2];if(function(e){return"function"==typeof e}(e))i=function(){return $r(e())};else{var s=$r(e);i=function(){return s}}function u(e,t){void 0===e&&(e=i());var r=Mr([o[t.type]],l.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===r.filter((function(e){return!!e})).length&&(r=[c]),r.reduce((function(e,r){if(r){var n;if(st(e))return void 0===(n=r(e,t))?e:n;if(ut(e))return cr(e,(function(e){return r(e,t)}));if(void 0===(n=r(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e}),e)}return u.getInitialState=i,u}(n,(function(e){for(var t in u)e.addCase(t,u[t]);for(var r=0,n=o;r<n.length;r++){var i=n[r];e.addMatcher(i.matcher,i.reducer)}s&&e.addDefaultCase(s)}))}return a.forEach((function(e){var r,n,a=i[e],s=function(e,t){return e+"/"+t}(t,e);"reducer"in a?(r=a.reducer,n=a.prepare):r=a,o[e]=r,l[s]=r,c[e]=n?Wr(s,n):Wr(s)})),{name:t,reducer:function(e,t){return r||(r=s()),r(e,t)},actions:c,caseReducers:o,getInitialState:function(){return r||(r=s()),r.getInitialState()}}}var Zr=function(e){void 0===e&&(e=21);for(var t="",r=e;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},Jr=["name","message","stack","code"],Qr=function(e,t){this.payload=e,this.meta=t},en=function(e,t){this.payload=e,this.meta=t},tn=function(e){if("object"==typeof e&&null!==e){for(var t={},r=0,n=Jr;r<n.length;r++){var i=n[r];"string"==typeof e[i]&&(t[i]=e[i])}return t}return{message:String(e)}};!function(){function e(e,t,r){var n=Wr(e+"/fulfilled",(function(e,t,r,n){return{payload:e,meta:Kr(Rr({},n||{}),{arg:r,requestId:t,requestStatus:"fulfilled"})}})),i=Wr(e+"/pending",(function(e,t,r){return{payload:void 0,meta:Kr(Rr({},r||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),a=Wr(e+"/rejected",(function(e,t,n,i,a){return{payload:i,error:(r&&r.serializeError||tn)(e||"Rejected"),meta:Kr(Rr({},a||{}),{arg:n,requestId:t,rejectedWithValue:!!i,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),o="undefined"!=typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){0},e}();return Object.assign((function(e){return function(l,c,s){var u,f=(null==r?void 0:r.idGenerator)?r.idGenerator(e):Zr(),d=new o;function p(e){u=e,d.abort()}var h=function(){return zr(this,null,(function(){var o,h,y,v,m,g;return kr(this,(function(b){switch(b.label){case 0:return b.trys.push([0,4,,5]),function(e){return null!==e&&"object"==typeof e&&"function"==typeof e.then}(v=null==(o=null==r?void 0:r.condition)?void 0:o.call(r,e,{getState:c,extra:s}))?[4,v]:[3,2];case 1:v=b.sent(),b.label=2;case 2:if(!1===v||d.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return m=new Promise((function(e,t){return d.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:u||"Aborted"})}))})),l(i(f,e,null==(h=null==r?void 0:r.getPendingMeta)?void 0:h.call(r,{requestId:f,arg:e},{getState:c,extra:s}))),[4,Promise.race([m,Promise.resolve(t(e,{dispatch:l,getState:c,extra:s,requestId:f,signal:d.signal,abort:p,rejectWithValue:function(e,t){return new Qr(e,t)},fulfillWithValue:function(e,t){return new en(e,t)}})).then((function(t){if(t instanceof Qr)throw t;return t instanceof en?n(t.payload,f,e,t.meta):n(t,f,e)}))])];case 3:return y=b.sent(),[3,5];case 4:return g=b.sent(),y=g instanceof Qr?a(null,f,e,g.payload,g.meta):a(g,f,e),[3,5];case 5:return r&&!r.dispatchConditionRejection&&a.match(y)&&y.meta.condition||l(y),[2,y]}}))}))}();return Object.assign(h,{abort:p,requestId:f,arg:e,unwrap:function(){return h.then(rn)}})}}),{pending:i,rejected:a,fulfilled:n,typePrefix:e})}e.withTypes=function(){return e}}();function rn(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var nn=function(e,t){if("function"!=typeof e)throw new TypeError(t+" is not a function")},an=function(){},on=function(e,t){return void 0===t&&(t=an),e.catch(t),e},ln=function(e,t){return e.addEventListener("abort",t,{once:!0}),function(){return e.removeEventListener("abort",t)}},cn=function(e,t){var r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},sn="listener",un="completed",fn="cancelled",dn="task-"+fn,pn="task-"+un,hn=sn+"-"+fn,yn=sn+"-"+un,vn=function(e){this.code=e,this.name="TaskAbortError",this.message="task "+fn+" (reason: "+e+")"},mn=function(e){if(e.aborted)throw new vn(e.reason)};function gn(e,t){var r=an;return new Promise((function(n,i){var a=function(){return i(new vn(e.reason))};e.aborted?a():(r=ln(e,a),t.finally((function(){return r()})).then(n,i))})).finally((function(){r=an}))}var bn=function(e){return function(t){return on(gn(e,t).then((function(t){return mn(e),t})))}},xn=function(e){var t=bn(e);return function(e){return t(new Promise((function(t){return setTimeout(t,e)})))}},wn=Object.assign,On={},Pn="listenerMiddleware",En=function(e,t){return function(r,n){nn(r,"taskExecutor");var i,a=new AbortController;i=a,ln(e,(function(){return cn(i,e.reason)}));var o,l,c=(o=function(){return zr(void 0,null,(function(){var t;return kr(this,(function(n){switch(n.label){case 0:return mn(e),mn(a.signal),[4,r({pause:bn(a.signal),delay:xn(a.signal),signal:a.signal})];case 1:return t=n.sent(),mn(a.signal),[2,t]}}))}))},l=function(){return cn(a,pn)},zr(void 0,null,(function(){var e;return kr(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,4,5]),[4,Promise.resolve()];case 1:return t.sent(),[4,o()];case 2:return[2,{status:"ok",value:t.sent()}];case 3:return[2,{status:(e=t.sent())instanceof vn?"cancelled":"rejected",error:e}];case 4:return null==l||l(),[7];case 5:return[2]}}))})));return(null==n?void 0:n.autoJoin)&&t.push(c),{result:bn(e)(c),cancel:function(){cn(a,dn)}}}},An=function(e,t){return function(r,n){return on(function(r,n){return zr(void 0,null,(function(){var i,a,o,l;return kr(this,(function(c){switch(c.label){case 0:mn(t),i=function(){},a=new Promise((function(t,n){var a=e({predicate:r,effect:function(e,r){r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=function(){a(),n()}})),o=[a],null!=n&&o.push(new Promise((function(e){return setTimeout(e,n,null)}))),c.label=1;case 1:return c.trys.push([1,,3,4]),[4,gn(t,Promise.race(o))];case 2:return l=c.sent(),mn(t),[2,l];case 3:return i(),[7];case 4:return[2]}}))}))}(r,n))}},jn=function(e){var t=e.type,r=e.actionCreator,n=e.matcher,i=e.predicate,a=e.effect;if(t)i=Wr(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(!i)throw new Error("Creating or removing a listener requires one of the known fields for matching an action");return nn(a,"options.listener"),{predicate:i,type:t,effect:a}},Sn=function(e){e.pending.forEach((function(e){cn(e,hn)}))},kn=function(e,t,r){try{e(t,r)}catch(e){setTimeout((function(){throw e}),0)}},Mn=Wr(Pn+"/add"),Tn=Wr(Pn+"/removeAll"),Dn=Wr(Pn+"/remove"),Cn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];console.error.apply(console,Mr([Pn+"/error"],e))};function In(e){var t=this;void 0===e&&(e={});var r=new Map,n=e.extra,i=e.onError,a=void 0===i?Cn:i;nn(a,"onError");var o=function(e){for(var t=0,n=Array.from(r.values());t<n.length;t++){var i=n[t];if(e(i))return i}},l=function(e){var t=o((function(t){return t.effect===e.effect}));return t||(t=function(e){var t=jn(e),r=t.type,n=t.predicate,i=t.effect;return{id:Zr(),effect:i,type:r,predicate:n,pending:new Set,unsubscribe:function(){throw new Error("Unsubscribe not initialized")}}}(e)),function(e){return e.unsubscribe=function(){return r.delete(e.id)},r.set(e.id,e),function(t){e.unsubscribe(),(null==t?void 0:t.cancelActive)&&Sn(e)}}(t)},c=function(e){var t=jn(e),r=t.type,n=t.effect,i=t.predicate,a=o((function(e){return("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n}));return a&&(a.unsubscribe(),e.cancelActive&&Sn(a)),!!a},s=function(e,i,o,c){return zr(t,null,(function(){var t,s,u,f;return kr(this,(function(d){switch(d.label){case 0:t=new AbortController,s=An(l,t.signal),u=[],d.label=1;case 1:return d.trys.push([1,3,4,6]),e.pending.add(t),[4,Promise.resolve(e.effect(i,wn({},o,{getOriginalState:c,condition:function(e,t){return s(e,t).then(Boolean)},take:s,delay:xn(t.signal),pause:bn(t.signal),extra:n,signal:t.signal,fork:En(t.signal,u),unsubscribe:e.unsubscribe,subscribe:function(){r.set(e.id,e)},cancelActiveListeners:function(){e.pending.forEach((function(e,r,n){e!==t&&(cn(e,hn),n.delete(e))}))}})))];case 2:return d.sent(),[3,6];case 3:return(f=d.sent())instanceof vn||kn(a,f,{raisedBy:"effect"}),[3,6];case 4:return[4,Promise.allSettled(u)];case 5:return d.sent(),cn(t,yn),e.pending.delete(t),[7];case 6:return[2]}}))}))},u=function(e){return function(){e.forEach(Sn),e.clear()}}(r);return{middleware:function(e){return function(t){return function(n){if(!Ur(n))return t(n);if(Mn.match(n))return l(n.payload);if(!Tn.match(n)){if(Dn.match(n))return c(n.payload);var i,o=e.getState(),f=function(){if(o===On)throw new Error(Pn+": getOriginalState can only be called synchronously");return o};try{if(i=t(n),r.size>0)for(var d=e.getState(),p=Array.from(r.values()),h=0,y=p;h<y.length;h++){var v=y[h],m=!1;try{m=v.predicate(n,d,o)}catch(e){m=!1,kn(a,e,{raisedBy:"predicate"})}m&&s(v,n,e,f)}}finally{o=On}return i}u()}}},startListening:l,stopListening:c,clearListeners:u}}"function"==typeof queueMicrotask&&queueMicrotask.bind("undefined"!=typeof window?window:void 0!==a.g?a.g:globalThis);var Nn,_n=function(e){return function(t){setTimeout(t,e)}};"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:_n(10);Ut();var Ln=Gr({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:Rn,setLayout:Kn,setChartSize:zn,setScale:Bn}=Ln.actions,Fn=Ln.reducer;function Wn(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}Array.prototype.slice;function Un(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function Xn(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Vn(e,t){return e[t]}function $n(e){const t=[];return t.key=e,t}function Hn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function qn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Hn(Object(r),!0).forEach((function(t){Yn(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hn(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Yn(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Gn=Math.PI/180,Zn=e=>180*e/Math.PI,Jn=(e,t,r,n)=>({x:e+Math.cos(-Gn*n)*r,y:t+Math.sin(-Gn*n)*r}),Qn=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},ei=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=(r-i)/o,c=Math.acos(l);return n>a&&(c=2*Math.PI-c),{radius:o,angle:Zn(c),angleInRadian:c}},ti=(e,t)=>{var{startAngle:r,endAngle:n}=t,i=Math.floor(r/360),a=Math.floor(n/360);return e+360*Math.min(i,a)},ri=(e,t)=>{var{x:r,y:n}=e,{radius:i,angle:a}=ei({x:r,y:n},t),{innerRadius:o,outerRadius:l}=t;if(i<o||i>l)return null;if(0===i)return null;var c,{startAngle:s,endAngle:u}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.floor(t/360),i=Math.floor(r/360),a=Math.min(n,i);return{startAngle:t-360*a,endAngle:r-360*a}})(t),f=a;if(s<=u){for(;f>u;)f-=360;for(;f<s;)f+=360;c=f>=s&&f<=u}else{for(;f>s;)f-=360;for(;f<u;)f+=360;c=f>=u&&f<=s}return c?qn(qn({},t),{},{radius:i,angle:ti(f,t)}):null},ni=e=>(0,t.isValidElement)(e)||"function"==typeof e||"boolean"==typeof e||null==e?"":e.className;function ii(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}function ai(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function oi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ai(Object(r),!0).forEach((function(t){li(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ai(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function li(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ci(e,t,r){return O(e)||O(t)?r:p(t)?l()(e,t,r):"function"==typeof t?t(e):r}var si=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,ui=(e,t,r,n)=>{if(n)return e.map((e=>e.coordinate));var i,a,o=e.map((e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate)));return i||o.push(t),a||o.push(r),o},fi=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:f,tickCount:d,ticks:p,niceTicks:h,axisType:y}=e;if(!o)return null;var v="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,m=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/v:0;return m="angleAxis"===y&&a&&a.length>=2?2*s(a[0]-a[1])*m:m,t&&(p||h)?(p||h||[]).map(((e,t)=>{var r=n?n.indexOf(e):e;return{coordinate:o(r)+m,value:e,offset:m,index:t}})).filter((e=>!u(e.coordinate))):c&&f?f.map(((e,t)=>({coordinate:o(e)+m,value:e,index:t,offset:m}))):o.ticks&&!r&&null!=d?o.ticks(d).map(((e,t)=>({coordinate:o(e)+m,value:e,offset:m,index:t}))):o.domain().map(((e,t)=>({coordinate:o(e)+m,value:n?n[e]:e,index:t,offset:m})))},di=1e-4,pi=(e,t)=>{if(!t||2!==t.length||!d(t[0])||!d(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!d(e[0])||e[0]<r)&&(i[0]=r),(!d(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},hi={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=u(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}Wn(e,t)}},none:Wn,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}Wn(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,c=0,s=0;l<i;++l){for(var u=e[t[l]],f=u[o][1]||0,d=(f-(u[o-1][1]||0))/2,p=0;p<l;++p){var h=e[t[p]];d+=(h[o][1]||0)-(h[o-1][1]||0)}c+=f,s+=d*f}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,Wn(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=u(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},yi=(e,t,r)=>{var n=hi[r],i=function(){var e=ve([]),t=Xn,r=Wn,n=Vn;function i(i){var a,o,l=Array.from(e.apply(this,arguments),$n),c=l.length,s=-1;for(const e of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=Un(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:ve(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:ve(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?Xn:"function"==typeof e?e:ve(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?Wn:e,i):r},i}().keys(t).value(((e,t)=>+ci(e,t,0))).order(Xn).offset(n);return i(e)};function vi(e){return null==e?void 0:String(e)}function mi(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!O(i[t.dataKey])){var l=x(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=ci(i,O(o)?t.dataKey:o);return O(c)?null:t.scale(c)}var gi=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=ci(a,t.dataKey,t.scale.domain()[o]);return O(l)?null:t.scale(l)-i/2+n},bi=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},xi=(e,t,r)=>{var n;if(null!=e)return n=Object.keys(e).reduce(((n,i)=>{var a=e[i],{stackedData:o}=a,l=o.reduce(((e,n)=>{var i,a=ii(n,t,r),o=(i=a.flat(2).filter(d),[Math.min(...i),Math.max(...i)]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]}),[1/0,-1/0]);return[Math.min(l[0],n[0]),Math.max(l[1],n[1])]}),[1/0,-1/0]),[n[0]===1/0?0:n[0],n[1]===-1/0?0:n[1]]},wi=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Oi=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Pi=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=nt()(t,(e=>e.coordinate)),a=1/0,o=1,l=i.length;o<l;o++){var c=i[o],s=i[o-1];a=Math.min((c.coordinate||0)-(s.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function Ei(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return oi(oi({},t),{},{dataKey:r,payload:n,value:i,name:a})}function Ai(e,t){return e?String(e):"string"==typeof t?t:void 0}var ji=e=>e.layout.width,Si=e=>e.layout.height,ki=e=>e.layout.scale,Mi=e=>e.layout.margin,Ti=et((e=>e.cartesianAxis.xAxis),(e=>Object.values(e))),Di=et((e=>e.cartesianAxis.yAxis),(e=>Object.values(e))),Ci=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],Ii="data-recharts-item-index",Ni="data-recharts-item-data-key";function _i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Li(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_i(Object(r),!0).forEach((function(t){Ri(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ri(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ki=et([ji,Si,Mi,e=>e.brush.height,Ti,Di,it,e=>e.legend.size],((e,t,r,n,i,a,o,c)=>{var s=a.reduce(((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return Li(Li({},e),{},{[r]:e[r]+n})}return e}),{left:r.left||0,right:r.right||0}),u=i.reduce(((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:Li(Li({},e),{},{[r]:l()(e,"".concat(r))+t.height})}),{top:r.top||0,bottom:r.bottom||0}),f=Li(Li({},u),s),p=f.bottom;f.bottom+=n,f=((e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&d(e[a]))return oi(oi({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&d(e[o]))return oi(oi({},e),{},{[o]:e[o]+(i||0)})}return e})(f,o,c);var h=e-f.left-f.right,y=t-f.top-f.bottom;return Li(Li({brushBottom:p},f),{},{width:Math.max(h,0),height:Math.max(y,0)})})),zi=et(Ki,(e=>({x:e.left,y:e.top,width:e.width,height:e.height}))),Bi=et(ji,Si,((e,t)=>({x:0,y:0,width:e,height:t}))),Fi=(0,t.createContext)(null),Wi=()=>null!=(0,t.useContext)(Fi),Ui=e=>{var{children:r}=e;return t.createElement(Fi.Provider,{value:!0},r)},Xi=e=>e.brush,Vi=et([Xi,Ki,Mi],((e,t,r)=>({height:e.height,x:d(e.x)?e.x:t.left,y:d(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:d(e.width)?e.width:t.width}))),$i=()=>{var e,t=Wi(),r=He(zi),n=He(Vi),i=null===(e=He(Xi))||void 0===e?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},Hi={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},qi=()=>{var e;return null!==(e=He(Ki))&&void 0!==e?e:Hi},Yi=()=>He(ji),Gi=()=>He(Si),Zi={top:0,right:0,bottom:0,left:0},Ji=e=>e.layout.layoutType,Qi=()=>He(Ji),ea=e=>{var r=Ue();return(0,t.useEffect)((()=>{r(zn(e))}),[r,e]),null},ta=e=>{var{margin:r}=e,n=Ue();return(0,t.useEffect)((()=>{n(Rn(r))}),[n,r]),null},ra=Symbol.for("immer-nothing"),na=Symbol.for("immer-draftable"),ia=Symbol.for("immer-state");function aa(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var oa=Object.getPrototypeOf;function la(e){return!!e&&!!e[ia]}function ca(e){return!!e&&(ua(e)||Array.isArray(e)||!!e[na]||!!e.constructor?.[na]||ya(e)||va(e))}var sa=Object.prototype.constructor.toString();function ua(e){if(!e||"object"!=typeof e)return!1;const t=oa(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===sa}function fa(e,t){0===da(e)?Reflect.ownKeys(e).forEach((r=>{t(r,e[r],e)})):e.forEach(((r,n)=>t(n,r,e)))}function da(e){const t=e[ia];return t?t.type_:Array.isArray(e)?1:ya(e)?2:va(e)?3:0}function pa(e,t){return 2===da(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ha(e,t,r){const n=da(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function ya(e){return e instanceof Map}function va(e){return e instanceof Set}function ma(e){return e.copy_||e.base_}function ga(e,t){if(ya(e))return new Map(e);if(va(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=ua(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[ia];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(oa(e),t)}{const t=oa(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function ba(e,t=!1){return wa(e)||la(e)||!ca(e)||(da(e)>1&&(e.set=e.add=e.clear=e.delete=xa),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>ba(t,!0)))),e}function xa(){aa(2)}function wa(e){return Object.isFrozen(e)}var Oa,Pa={};function Ea(e){const t=Pa[e];return t||aa(0),t}function Aa(){return Oa}function ja(e,t){t&&(Ea("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Sa(e){ka(e),e.drafts_.forEach(Ta),e.drafts_=null}function ka(e){e===Oa&&(Oa=e.parent_)}function Ma(e){return Oa={drafts_:[],parent_:Oa,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Ta(e){const t=e[ia];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function Da(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[ia].modified_&&(Sa(t),aa(4)),ca(e)&&(e=Ca(t,e),t.parent_||Na(t,e)),t.patches_&&Ea("Patches").generateReplacementPatches_(r[ia].base_,e,t.patches_,t.inversePatches_)):e=Ca(t,r,[]),Sa(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==ra?e:void 0}function Ca(e,t,r){if(wa(t))return t;const n=t[ia];if(!n)return fa(t,((i,a)=>Ia(e,n,t,i,a,r))),t;if(n.scope_!==e)return t;if(!n.modified_)return Na(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),fa(i,((i,o)=>Ia(e,n,t,i,o,r,a))),Na(e,t,!1),r&&e.patches_&&Ea("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function Ia(e,t,r,n,i,a,o){if(la(i)){const o=Ca(e,i,a&&t&&3!==t.type_&&!pa(t.assigned_,n)?a.concat(n):void 0);if(ha(r,n,o),!la(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(ca(i)&&!wa(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Ca(e,i),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||Na(e,i)}}function Na(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&ba(t,r)}var _a={get(e,t){if(t===ia)return e;const r=ma(e);if(!pa(r,t))return function(e,t,r){const n=Ka(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!ca(n)?n:n===Ra(e.base_,t)?(Ba(e),e.copy_[t]=Fa(n,e)):n},has:(e,t)=>t in ma(e),ownKeys:e=>Reflect.ownKeys(ma(e)),set(e,t,r){const n=Ka(ma(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=Ra(ma(e),t),i=n?.[ia];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(r,n)&&(void 0!==r||pa(e.base_,t)))return!0;Ba(e),za(e)}return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==Ra(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,Ba(e),za(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=ma(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){aa(11)},getPrototypeOf:e=>oa(e.base_),setPrototypeOf(){aa(12)}},La={};function Ra(e,t){const r=e[ia];return(r?ma(r):e)[t]}function Ka(e,t){if(!(t in e))return;let r=oa(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=oa(r)}}function za(e){e.modified_||(e.modified_=!0,e.parent_&&za(e.parent_))}function Ba(e){e.copy_||(e.copy_=ga(e.base_,e.scope_.immer_.useStrictShallowCopy_))}fa(_a,((e,t)=>{La[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),La.deleteProperty=function(e,t){return La.set.call(this,e,t,void 0)},La.set=function(e,t,r){return _a.set.call(this,e[0],t,r,e[0])};function Fa(e,t){const r=ya(e)?Ea("MapSet").proxyMap_(e,t):va(e)?Ea("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:Aa(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=n,a=_a;r&&(i=[n],a=La);const{revoke:o,proxy:l}=Proxy.revocable(i,a);return n.draft_=l,n.revoke_=o,l}(e,t);return(t?t.scope_:Aa()).drafts_.push(r),r}function Wa(e){if(!ca(e)||wa(e))return e;const t=e[ia];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=ga(e,t.scope_.immer_.useStrictShallowCopy_)}else r=ga(e,!0);return fa(r,((e,t)=>{ha(r,e,Wa(t))})),t&&(t.finalized_=!1),r}var Ua=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...i){return n.produce(e,(e=>t.call(this,e,...i)))}}let n;if("function"!=typeof t&&aa(6),void 0!==r&&"function"!=typeof r&&aa(7),ca(e)){const i=Ma(this),a=Fa(e,void 0);let o=!0;try{n=t(a),o=!1}finally{o?Sa(i):ka(i)}return ja(i,r),Da(n,i)}if(!e||"object"!=typeof e){if(n=t(e),void 0===n&&(n=e),n===ra&&(n=void 0),this.autoFreeze_&&ba(n,!0),r){const t=[],i=[];Ea("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}aa(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,(t=>e(t,...r)));let r,n;const i=this.produce(e,t,((e,t)=>{r=e,n=t}));return[i,r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){ca(e)||aa(8),la(e)&&(e=function(e){la(e)||aa(10);return Wa(e)}(e));const t=Ma(this),r=Fa(e,void 0);return r[ia].isManual_=!0,ka(t),r}finishDraft(e,t){const r=e&&e[ia];r&&r.isManual_||aa(9);const{scope_:n}=r;return ja(n,t),Da(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=Ea("Patches").applyPatches_;return la(e)?n(e,t):this.produce(e,(e=>n(e,t)))}};Ua.produce,Ua.produceWithPatches.bind(Ua),Ua.setAutoFreeze.bind(Ua),Ua.setUseStrictShallowCopy.bind(Ua),Ua.applyPatches.bind(Ua),Ua.createDraft.bind(Ua),Ua.finishDraft.bind(Ua);var Xa=Gr({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=Ft(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:Va,setLegendSettings:$a,addLegendPayload:Ha,removeLegendPayload:qa}=Xa.actions,Ya=Xa.reducer,Ga=["contextPayload"];function Za(){return Za=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Za.apply(null,arguments)}function Ja(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Qa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ja(Object(r),!0).forEach((function(t){eo(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ja(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function eo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function to(e){return e.value}function ro(e){var{contextPayload:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ga),i=ze(r,e.payloadUniqBy,to),a=Qa(Qa({},n),{},{payload:i});return t.isValidElement(e.content)?t.cloneElement(e.content,a):"function"==typeof e.content?t.createElement(e.content,a):t.createElement(Le,a)}function no(e){var r=Ue();return(0,t.useEffect)((()=>{r($a(e))}),[r,e]),null}function io(e){var r=Ue();return(0,t.useEffect)((()=>(r(Va(e)),()=>{r(Va({width:0,height:0}))})),[r,e]),null}function ao(e){var r,n=He(at),i=(0,t.useContext)(H),a=null!==(r=He((e=>e.layout.margin)))&&void 0!==r?r:Zi,{width:o,height:l,wrapperStyle:c,portal:s}=e,[u,f]=lt([n]),d=Yi(),p=Gi(),h=d-(a.left||0)-(a.right||0),y=oo.getWidthOrHeight(e.layout,l,o,h),v=s?c:Qa(Qa({position:"absolute",width:(null==y?void 0:y.width)||o||"auto",height:(null==y?void 0:y.height)||l||"auto"},function(e,t,r,n,i,a){var o,l,{layout:c,align:s,verticalAlign:u}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(o="center"===s&&"vertical"===c?{left:((n||0)-a.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===u?{top:((i||0)-a.height)/2}:"bottom"===u?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),Qa(Qa({},o),l)}(c,e,a,d,p,u)),c),m=null!=s?s:i;if(null==m)return null;var g=t.createElement("div",{className:"recharts-legend-wrapper",style:v,ref:f},t.createElement(no,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),t.createElement(io,{width:u.width,height:u.height}),t.createElement(ro,Za({},e,y,{margin:a,chartWidth:d,chartHeight:p,contextPayload:n})));return(0,$.createPortal)(g,m)}class oo extends t.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&d(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return t.createElement(ao,this.props)}}function lo(){return lo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lo.apply(null,arguments)}function co(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function so(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?co(Object(r),!0).forEach((function(t){uo(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):co(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function uo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fo(e){return Array.isArray(e)&&p(e[0])&&p(e[1])?e.join(" ~ "):e}eo(oo,"displayName","Legend"),eo(oo,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});var po=e=>{var{separator:r=" : ",contentStyle:i={},itemStyle:a={},labelStyle:o={},payload:l,formatter:c,itemSorter:s,wrapperClassName:u,labelClassName:f,label:d,labelFormatter:h,accessibilityLayer:y=!1}=e,v=so({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),m=so({margin:0},o),g=!O(d),b=g?d:"",x=n("recharts-default-tooltip",u),w=n("recharts-tooltip-label",f);g&&h&&null!=l&&(b=h(d,l));var P=y?{role:"status","aria-live":"assertive"}:{};return t.createElement("div",lo({className:x,style:v},P),t.createElement("p",{className:w,style:m},t.isValidElement(b)?b:"".concat(b)),(()=>{if(l&&l.length){var e=(s?nt()(l,s):l).map(((e,n)=>{if("none"===e.type)return null;var i=e.formatter||c||fo,{value:o,name:s}=e,u=o,f=s;if(i){var d=i(o,s,e,n,l);if(Array.isArray(d))[u,f]=d;else{if(null==d)return null;u=d}}var h=so({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},a);return t.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(n),style:h},p(f)?t.createElement("span",{className:"recharts-tooltip-item-name"},f):null,p(f)?t.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,t.createElement("span",{className:"recharts-tooltip-item-value"},u),t.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))}));return t.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},ho="recharts-tooltip-wrapper",yo={visibility:"hidden"};function vo(e){var{coordinate:t,translateX:r,translateY:i}=e;return n(ho,{["".concat(ho,"-right")]:d(r)&&t&&d(t.x)&&r>=t.x,["".concat(ho,"-left")]:d(r)&&t&&d(t.x)&&r<t.x,["".concat(ho,"-bottom")]:d(i)&&t&&d(t.y)&&i>=t.y,["".concat(ho,"-top")]:d(i)&&t&&d(t.y)&&i<t.y})}function mo(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(a&&d(a[n]))return a[n];var u=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?u:f;var p=c[n];return null==p?0:o[n]?u<p?Math.max(f,p):Math.max(u,p):null==s?0:f+l>p+s?Math.max(u,p):Math.max(f,p)}function go(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function bo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?go(Object(r),!0).forEach((function(t){xo(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):go(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function xo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class wo extends t.PureComponent{constructor(){super(...arguments),xo(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),xo(this,"handleKeyDown",(e=>{var t,r,n,i;"Escape"===e.key&&this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(i=this.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==n?n:0}})}))}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)===this.state.dismissedAtCoordinate.x&&(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}render(){var{active:e,allowEscapeViewBox:r,animationDuration:n,animationEasing:i,children:a,coordinate:o,hasPayload:l,isAnimationActive:c,offset:s,position:u,reverseDirection:f,useTranslate3d:d,viewBox:p,wrapperStyle:h,lastBoundingBox:y,innerRef:v,hasPortalFromProps:m}=this.props,{cssClasses:g,cssProperties:b}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:c,tooltipBox:s,useTranslate3d:u,viewBox:f}=e;return t=s.height>0&&s.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=mo({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=mo({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:u}):yo,{cssProperties:t,cssClasses:vo({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:r,coordinate:o,offsetTopLeft:s,position:u,reverseDirection:f,tooltipBox:{height:y.height,width:y.width},useTranslate3d:d,viewBox:p}),x=m?{}:bo(bo({transition:c&&e?"transform ".concat(n,"ms ").concat(i):void 0},b),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&l?"visible":"hidden",position:"absolute",top:0,left:0}),w=bo(bo({},x),{},{visibility:!this.state.dismissed&&e&&l?"visible":"hidden"},h);return t.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:g,style:w,ref:v},a)}}var Oo={isSsr:!("undefined"!=typeof window&&window.document&&Boolean(window.document.createElement)&&window.setTimeout)},Po=()=>He((e=>e.rootProps.accessibilityLayer));function Eo(){}function Ao(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function jo(e){this._context=e}function So(e){this._context=e}function ko(e){this._context=e}jo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ao(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ao(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},So.prototype={areaStart:Eo,areaEnd:Eo,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Ao(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},ko.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ao(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class Mo{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function To(e){this._context=e}function Do(e){this._context=e}function Co(e){return new Do(e)}function Io(e){return e<0?-1:1}function No(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),l=(a*i+o*n)/(n+i);return(Io(a)+Io(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(l))||0}function _o(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Lo(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function Ro(e){this._context=e}function Ko(e){this._context=new zo(e)}function zo(e){this._context=e}function Bo(e){this._context=e}function Fo(e){var t,r,n=e.length-1,i=new Array(n),a=new Array(n),o=new Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[n-1]=(e[n]+i[n-1])/2,t=0;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Wo(e,t){this._context=e,this._t=t}function Uo(e){return e[0]}function Xo(e){return e[1]}function Vo(e,t){var r=ve(!0),n=null,i=Co,a=null,o=Pe(l);function l(l){var c,s,u,f=(l=Un(l)).length,d=!1;for(null==n&&(a=i(u=o())),c=0;c<=f;++c)!(c<f&&r(s=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(s,c,l),+t(s,c,l));if(u)return a=null,u+""||null}return e="function"==typeof e?e:void 0===e?Uo:ve(e),t="function"==typeof t?t:void 0===t?Xo:ve(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:ve(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:ve(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:ve(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function $o(e,t,r){var n=null,i=ve(!0),a=null,o=Co,l=null,c=Pe(s);function s(s){var u,f,d,p,h,y=(s=Un(s)).length,v=!1,m=new Array(y),g=new Array(y);for(null==a&&(l=o(h=c())),u=0;u<=y;++u){if(!(u<y&&i(p=s[u],u,s))===v)if(v=!v)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(m[d],g[d]);l.lineEnd(),l.areaEnd()}v&&(m[u]=+e(p,u,s),g[u]=+t(p,u,s),l.point(n?+n(p,u,s):m[u],r?+r(p,u,s):g[u]))}if(h)return l=null,h+""||null}function u(){return Vo().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?Uo:ve(+e),t="function"==typeof t?t:ve(void 0===t?0:+t),r="function"==typeof r?r:void 0===r?Xo:ve(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:ve(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:ve(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:ve(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:ve(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:ve(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:ve(+e),s):r},s.lineX0=s.lineY0=function(){return u().x(e).y(t)},s.lineY1=function(){return u().x(e).y(r)},s.lineX1=function(){return u().x(n).y(t)},s.defined=function(e){return arguments.length?(i="function"==typeof e?e:ve(!!e),s):i},s.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),s):o},s.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),s):a},s}function Ho(e){return Number.isFinite(e)}function qo(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function Yo(){return Yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yo.apply(null,arguments)}function Go(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Zo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Go(Object(r),!0).forEach((function(t){Jo(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Go(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Jo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}To.prototype={areaStart:Eo,areaEnd:Eo,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},Do.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},Ro.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Lo(this,this._t0,_o(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Lo(this,_o(this,r=No(this,e,t)),r);break;default:Lo(this,this._t0,r=No(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(Ko.prototype=Object.create(Ro.prototype)).point=function(e,t){Ro.prototype.point.call(this,t,e)},zo.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},Bo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=Fo(e),i=Fo(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},Wo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var Qo={curveBasisClosed:function(e){return new So(e)},curveBasisOpen:function(e){return new ko(e)},curveBasis:function(e){return new jo(e)},curveBumpX:function(e){return new Mo(e,!0)},curveBumpY:function(e){return new Mo(e,!1)},curveLinearClosed:function(e){return new To(e)},curveLinear:Co,curveMonotoneX:function(e){return new Ro(e)},curveMonotoneY:function(e){return new Ko(e)},curveNatural:function(e){return new Bo(e)},curveStep:function(e){return new Wo(e,.5)},curveStepAfter:function(e){return new Wo(e,1)},curveStepBefore:function(e){return new Wo(e,0)}},el=e=>Ho(e.x)&&Ho(e.y),tl=e=>e.x,rl=e=>e.y,nl=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat(P(e));return"curveMonotone"!==r&&"curveBump"!==r||!t?Qo[r]||Co:Qo["".concat(r).concat("vertical"===t?"Y":"X")]})(r,a),c=o?n.filter(el):n;if(Array.isArray(i)){var s=o?i.filter((e=>el(e))):i,u=c.map(((e,t)=>Zo(Zo({},e),{},{base:s[t]})));return t="vertical"===a?$o().y(rl).x1(tl).x0((e=>e.base.x)):$o().x(tl).y1(rl).y0((e=>e.base.y)),t.defined(el).curve(l),t(u)}return(t="vertical"===a&&d(i)?$o().y(rl).x1(tl).x0(i):d(i)?$o().x(tl).y1(rl).y0(i):Vo().x(tl).y(rl)).defined(el).curve(l),t(c)},il=e=>{var{className:r,points:i,path:a,pathRef:o}=e;if(!(i&&i.length||a))return null;var l=i&&i.length?nl(e):a;return t.createElement("path",Yo({},C(e),k(e),{className:n("recharts-curve",r),d:null===l?void 0:l,ref:o}))},al=["x","y","top","left","width","height","className"];function ol(){return ol=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ol.apply(null,arguments)}function ll(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function cl(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var sl=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),ul=e=>{var{x:r=0,y:i=0,top:a=0,left:o=0,width:l=0,height:c=0,className:s}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,al),f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ll(Object(r),!0).forEach((function(t){cl(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ll(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({x:r,y:i,top:a,left:o,width:l,height:c},u);return d(r)&&d(i)&&d(l)&&d(c)&&d(a)&&d(o)?t.createElement("path",ol({},z(f,!0),{className:n("recharts-cross",s),d:sl(r,i,l,c,a,o)})):null};function fl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function dl(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pl(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fl(Object(r),!0).forEach((function(t){dl(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fl(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e),n=t;return Object.keys(t).reduce(((e,t)=>(void 0===e[t]&&void 0!==n[t]&&(e[t]=n[t]),e)),r)}var hl=a(7541),yl=a.n(hl),vl=1e-4,ml=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],gl=(e,t)=>e.map(((e,r)=>e*t**r)).reduce(((e,t)=>e+t)),bl=(e,t)=>r=>{var n=ml(e,t);return gl(n,r)},xl=function(){for(var e,t,r,n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map((e=>parseFloat(e))))}else 4===a.length&&([e,r,t,n]=a);var c,s,u=bl(e,t),f=bl(r,n),d=(c=e,s=t,e=>{var t=[...ml(c,s).map(((e,t)=>e*t)).slice(1),0];return gl(t,e)}),p=e=>e>1?1:e<0?0:e,h=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=u(r)-t,a=d(r);if(Math.abs(i-t)<vl||a<vl)return f(r);r=p(r-i/a)}return f(r)};return h.isStepper=!1,h},wl=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return xl(e);case"spring":return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return Math.abs(l-i)<vl&&Math.abs(o)<vl?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i}();default:if("cubic-bezier"===e.split("(")[0])return xl(e)}return"function"==typeof e?e:null};function Ol(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Pl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ol(Object(r),!0).forEach((function(t){El(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ol(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function El(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Al=(e,t,r)=>e.map((e=>{return"".concat((n=e,n.replace(/([A-Z])/g,(e=>"-".concat(e.toLowerCase()))))," ").concat(t,"ms ").concat(r);var n})).join(","),jl=(e,t)=>Object.keys(t).reduce(((r,n)=>Pl(Pl({},r),{},{[n]:e(n,t[n])})),{});function Sl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function kl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sl(Object(r),!0).forEach((function(t){Ml(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sl(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ml(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Tl=(e,t,r)=>e+(t-e)*r,Dl=e=>{var{from:t,to:r}=e;return t!==r},Cl=(e,t,r)=>{var n=jl(((t,r)=>{if(Dl(r)){var[n,i]=e(r.from,r.to,r.velocity);return kl(kl({},r),{},{from:n,velocity:i})}return r}),t);return r<1?jl(((e,t)=>Dl(t)?kl(kl({},t),{},{velocity:Tl(t.velocity,n[e].velocity,r),from:Tl(t.from,n[e].from,r)}):t),t):Cl(e,n,r-1)};function Il(e,t,r,n,i,a){var o,l=n.reduce(((r,n)=>kl(kl({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}})),{}),c=null,s=n=>{o||(o=n);var u=(n-o)/r.dt;l=Cl(r,l,u),i(kl(kl(kl({},e),t),jl(((e,t)=>t.from),l))),o=n,Object.values(l).filter(Dl).length&&(c=a.setTimeout(s))};return()=>(c=a.setTimeout(s),()=>{c()})}const Nl=(e,t,r,n,i,a)=>{var o,l,c=(o=e,l=t,[Object.keys(o),Object.keys(l)].reduce(((e,t)=>e.filter((e=>t.includes(e))))));return!0===r.isStepper?Il(e,t,r,c,i,a):function(e,t,r,n,i,a,o){var l,c=null,s=i.reduce(((r,n)=>kl(kl({},r),{},{[n]:[e[n],t[n]]})),{}),u=i=>{l||(l=i);var f=(i-l)/n,d=jl(((e,t)=>Tl(...t,r(f))),s);if(a(kl(kl(kl({},e),t),d)),f<1)c=o.setTimeout(u);else{var p=jl(((e,t)=>Tl(...t,r(1))),s);a(kl(kl(kl({},e),t),p))}};return()=>(c=o.setTimeout(u),()=>{c()})}(e,t,r,n,c,i,a)};class _l{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}function Ll(){return e=new _l,t=()=>null,r=!1,n=null,i=a=>{if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,[l,...c]=o;return"number"==typeof l?void(n=e.setTimeout(i.bind(null,c),l)):(i(l),void(n=e.setTimeout(i.bind(null,c))))}"string"==typeof a&&t(a),"object"==typeof a&&t(a),"function"==typeof a&&a()}},{stop:()=>{r=!0},start:e=>{r=!1,n&&(n(),n=null),i(e)},subscribe:e=>(t=e,()=>{t=()=>null}),getTimeoutController:()=>e};var e,t,r,n,i}var Rl=(0,t.createContext)(Ll);function Kl(e,r){var n=(0,t.useContext)(Rl);return(0,t.useMemo)((()=>null!=r?r:n(e)),[e,r,n])}var zl=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function Bl(){return Bl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Bl.apply(null,arguments)}function Fl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Wl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fl(Object(r),!0).forEach((function(t){Ul(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fl(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ul(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class Xl extends t.PureComponent{constructor(e,t){super(e,t),Ul(this,"mounted",!1),Ul(this,"manager",void 0),Ul(this,"stopJSAnimation",null),Ul(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0)return this.state={style:{}},void("function"==typeof o&&(this.state={style:a}));if(i){if("function"==typeof o)return void(this.state={style:i});this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r)if(t){if(!(yl()(e.to,a)&&e.canBegin&&e.isActive)){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?o:e.to;if(this.state&&l){var u={style:n?{[n]:s}:s};(n&&l[n]!==s||!n&&l!==s)&&this.setState(u)}this.runAnimation(Wl(Wl({},this.props),{},{from:s,begin:0}))}}else{var f={style:n?{[n]:a}:a};this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState(f)}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,c=Nl(t,r,wl(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"!=typeof a&&"function"!=typeof c&&"spring"!==a){var s=n?{[n]:i}:i,u=Al(Object.keys(s),r,a);this.manager.start([o,t,Wl(Wl({},s),{},{transition:u}),r,l])}else this.runJSAnimation(e)}render(){var e=this.props,{children:r,begin:n,duration:i,attributeName:a,easing:o,isActive:l,from:c,to:s,canBegin:u,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:p,animationManager:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,zl),v=t.Children.count(r),m=this.state.style;if("function"==typeof r)return r(m);if(!l||0===v||i<=0)return r;var g=e=>{var{style:r={},className:n}=e.props;return(0,t.cloneElement)(e,Wl(Wl({},y),{},{style:Wl(Wl({},r),m),className:n}))};return 1===v?g(t.Children.only(r)):t.createElement("div",null,t.Children.map(r,(e=>g(e))))}}function Vl(e){var r,n=Kl(null!==(r=e.attributeName)&&void 0!==r?r:Object.keys(e.to).join(","),e.animationManager);return t.createElement(Xl,Bl({},e,{animationManager:n}))}function $l(){return $l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$l.apply(null,arguments)}Ul(Xl,"displayName","Animate"),Ul(Xl,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var Hl=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(a+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(s,",").concat(e+c*u[0],",").concat(t)),a+="L ".concat(e+r-c*u[1],",").concat(t),u[1]>0&&(a+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+l*u[1])),a+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(a+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(s,",\n        ").concat(e+r-c*u[2],",").concat(t+n)),a+="L ".concat(e+c*u[3],",").concat(t+n),u[3]>0&&(a+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-l*u[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},ql={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Yl=e=>{var r=pl(e,ql),i=(0,t.useRef)(null),[a,o]=(0,t.useState)(-1);(0,t.useEffect)((()=>{if(i.current&&i.current.getTotalLength)try{var e=i.current.getTotalLength();e&&o(e)}catch(e){}}),[]);var{x:l,y:c,width:s,height:u,radius:f,className:d}=r,{animationEasing:p,animationDuration:h,animationBegin:y,isAnimationActive:v,isUpdateAnimationActive:m}=r;if(l!==+l||c!==+c||s!==+s||u!==+u||0===s||0===u)return null;var g=n("recharts-rectangle",d);return m?t.createElement(Vl,{canBegin:a>0,from:{width:s,height:u,x:l,y:c},to:{width:s,height:u,x:l,y:c},duration:h,animationEasing:p,isActive:m},(e=>{var{width:n,height:o,x:l,y:c}=e;return t.createElement(Vl,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,isActive:v,easing:p},t.createElement("path",$l({},z(r,!0),{className:g,d:Hl(l,c,n,o,f),ref:i})))})):t.createElement("path",$l({},z(r,!0),{className:g,d:Hl(l,c,s,u,f)}))};function Gl(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[Jn(t,r,n,i),Jn(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function Zl(){return Zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zl.apply(null,arguments)}var Jl=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:c}=e,s=l*(o?1:-1)+n,u=Math.asin(l/s)/Gn,f=c?i:i+a*u,d=c?i-a*u:i;return{center:Jn(t,r,s,f),circleTangency:Jn(t,r,n,f),lineTangency:Jn(t,r,s*Math.cos(u*Gn),d),theta:u}},Ql=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=((e,t)=>s(t-e)*Math.min(Math.abs(t-e),359.999))(a,o),c=a+l,u=Jn(t,r,i,a),f=Jn(t,r,i,c),d="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>c),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var p=Jn(t,r,n,a),h=Jn(t,r,n,c);d+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=c),",\n            ").concat(p.x,",").concat(p.y," Z")}else d+="L ".concat(t,",").concat(r," Z");return d},ec={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},tc=e=>{var r=pl(e,ec),{cx:i,cy:a,innerRadius:o,outerRadius:l,cornerRadius:c,forceCornerRadius:u,cornerIsExternal:f,startAngle:d,endAngle:p,className:h}=r;if(l<o||d===p)return null;var y,m=n("recharts-sector",h),g=l-o,b=v(c,g,0,!0);return y=b>0&&Math.abs(d-p)<360?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:u}=e,f=s(u-c),{circleTangency:d,lineTangency:p,theta:h}=Jl({cx:t,cy:r,radius:i,angle:c,sign:f,cornerRadius:a,cornerIsExternal:l}),{circleTangency:y,lineTangency:v,theta:m}=Jl({cx:t,cy:r,radius:i,angle:u,sign:-f,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(c-u):Math.abs(c-u)-h-m;if(g<0)return o?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*-a,",0\n      "):Ql({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:u});var b="M ".concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var{circleTangency:x,lineTangency:w,theta:O}=Jl({cx:t,cy:r,radius:n,angle:c,sign:f,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:P,lineTangency:E,theta:A}=Jl({cx:t,cy:r,radius:n,angle:u,sign:-f,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),j=l?Math.abs(c-u):Math.abs(c-u)-O-A;if(j<0&&0===a)return"".concat(b,"L").concat(t,",").concat(r,"Z");b+="L".concat(E.x,",").concat(E.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(j>180),",").concat(+(f>0),",").concat(x.x,",").concat(x.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(w.x,",").concat(w.y,"Z")}else b+="L".concat(t,",").concat(r,"Z");return b})({cx:i,cy:a,innerRadius:o,outerRadius:l,cornerRadius:Math.min(b,g/2),forceCornerRadius:u,cornerIsExternal:f,startAngle:d,endAngle:p}):Ql({cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:d,endAngle:p}),t.createElement("path",Zl({},z(r,!0),{className:m,d:y}))};function rc(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return Gl(t);var{cx:l,cy:c,innerRadius:s,outerRadius:u,angle:f}=t,d=Jn(l,c,s,f),p=Jn(l,c,u,f);n=d.x,i=d.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}var nc=a(3412),ic=a.n(nc);function ac(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function oc(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class lc extends Map{constructor(e,t=fc){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[t,r]of e)this.set(t,r)}get(e){return super.get(cc(this,e))}has(e){return super.has(cc(this,e))}set(e,t){return super.set(sc(this,e),t)}delete(e){return super.delete(uc(this,e))}}Set;function cc({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function sc({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function uc({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function fc(e){return null!==e&&"object"==typeof e?e.valueOf():e}const dc=Symbol("implicit");function pc(){var e=new lc,t=[],r=[],n=dc;function i(i){let a=e.get(i);if(void 0===a){if(n!==dc)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();t=[],e=new lc;for(const n of r)e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return pc(t,r).unknown(n)},ac.apply(i,arguments),i}function hc(){var e,t,r=pc().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,s=0,u=.5;function f(){var r=n().length,f=o<a,d=f?o:a,p=f?a:o;e=(p-d)/Math.max(1,r-c+2*s),l&&(e=Math.floor(e)),d+=(p-d-e*(r-c))*u,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var h=function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=new Array(i);++n<i;)a[n]=e+n*r;return a}(r).map((function(t){return d+e*t}));return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,s=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return hc(n(),[a,o]).round(l).paddingInner(c).paddingOuter(s).align(u)},ac.apply(f(),arguments)}function yc(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return yc(t())},e}function vc(){return yc(hc.apply(null,arguments).paddingInner(1))}const mc=Math.sqrt(50),gc=Math.sqrt(10),bc=Math.sqrt(2);function xc(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=mc?10:a>=gc?5:a>=bc?2:1;let l,c,s;return i<0?(s=Math.pow(10,-i)/o,l=Math.round(e*s),c=Math.round(t*s),l/s<e&&++l,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,l=Math.round(e/s),c=Math.round(t/s),l*s<e&&++l,c*s>t&&--c),c<l&&.5<=r&&r<2?xc(e,t,2*r):[l,c,s]}function wc(e,t,r){if(!((r=+r)>0))return[];if((e=+e)===(t=+t))return[e];const n=t<e,[i,a,o]=n?xc(t,e,r):xc(e,t,r);if(!(a>=i))return[];const l=a-i+1,c=new Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=(a-e)/-o;else for(let e=0;e<l;++e)c[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=(i+e)/-o;else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function Oc(e,t,r){return xc(e=+e,t=+t,r=+r)[2]}function Pc(e,t,r){r=+r;const n=(t=+t)<(e=+e),i=n?Oc(t,e,r):Oc(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Ec(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Ac(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function jc(e){let t,r,n;function i(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<0?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=Ec,r=(t,r)=>Ec(e(t),r),n=(t,r)=>e(t)-r):(t=e===Ec||e===Ac?e:Sc,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){const o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<=0?i=t+1:a=t}while(i<a)}return i}}}function Sc(){return 0}function kc(e){return null===e?NaN:+e}const Mc=jc(Ec),Tc=Mc.right,Dc=(Mc.left,jc(kc).center,Tc);function Cc(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Ic(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Nc(){}var _c=.7,Lc=1/_c,Rc="\\s*([+-]?\\d+)\\s*",Kc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",zc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Bc=/^#([0-9a-f]{3,8})$/,Fc=new RegExp(`^rgb\\(${Rc},${Rc},${Rc}\\)$`),Wc=new RegExp(`^rgb\\(${zc},${zc},${zc}\\)$`),Uc=new RegExp(`^rgba\\(${Rc},${Rc},${Rc},${Kc}\\)$`),Xc=new RegExp(`^rgba\\(${zc},${zc},${zc},${Kc}\\)$`),Vc=new RegExp(`^hsl\\(${Kc},${zc},${zc}\\)$`),$c=new RegExp(`^hsla\\(${Kc},${zc},${zc},${Kc}\\)$`),Hc={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function qc(){return this.rgb().formatHex()}function Yc(){return this.rgb().formatRgb()}function Gc(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Bc.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?Zc(t):3===r?new es(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?Jc(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?Jc(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Fc.exec(e))?new es(t[1],t[2],t[3],1):(t=Wc.exec(e))?new es(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Uc.exec(e))?Jc(t[1],t[2],t[3],t[4]):(t=Xc.exec(e))?Jc(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Vc.exec(e))?os(t[1],t[2]/100,t[3]/100,1):(t=$c.exec(e))?os(t[1],t[2]/100,t[3]/100,t[4]):Hc.hasOwnProperty(e)?Zc(Hc[e]):"transparent"===e?new es(NaN,NaN,NaN,0):null}function Zc(e){return new es(e>>16&255,e>>8&255,255&e,1)}function Jc(e,t,r,n){return n<=0&&(e=t=r=NaN),new es(e,t,r,n)}function Qc(e,t,r,n){return 1===arguments.length?function(e){return e instanceof Nc||(e=Gc(e)),e?new es((e=e.rgb()).r,e.g,e.b,e.opacity):new es}(e):new es(e,t,r,null==n?1:n)}function es(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ts(){return`#${as(this.r)}${as(this.g)}${as(this.b)}`}function rs(){const e=ns(this.opacity);return`${1===e?"rgb(":"rgba("}${is(this.r)}, ${is(this.g)}, ${is(this.b)}${1===e?")":`, ${e})`}`}function ns(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function is(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function as(e){return((e=is(e))<16?"0":"")+e.toString(16)}function os(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new cs(e,t,r,n)}function ls(e){if(e instanceof cs)return new cs(e.h,e.s,e.l,e.opacity);if(e instanceof Nc||(e=Gc(e)),!e)return new cs;if(e instanceof cs)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+6*(r<n):r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new cs(o,l,c,e.opacity)}function cs(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ss(e){return(e=(e||0)%360)<0?e+360:e}function us(e){return Math.max(0,Math.min(1,e||0))}function fs(e,t,r){return 255*(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)}function ds(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}Cc(Nc,Gc,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:qc,formatHex:qc,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ls(this).formatHsl()},formatRgb:Yc,toString:Yc}),Cc(es,Qc,Ic(Nc,{brighter(e){return e=null==e?Lc:Math.pow(Lc,e),new es(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?_c:Math.pow(_c,e),new es(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new es(is(this.r),is(this.g),is(this.b),ns(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ts,formatHex:ts,formatHex8:function(){return`#${as(this.r)}${as(this.g)}${as(this.b)}${as(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:rs,toString:rs})),Cc(cs,(function(e,t,r,n){return 1===arguments.length?ls(e):new cs(e,t,r,null==n?1:n)}),Ic(Nc,{brighter(e){return e=null==e?Lc:Math.pow(Lc,e),new cs(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?_c:Math.pow(_c,e),new cs(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new es(fs(e>=240?e-240:e+120,i,n),fs(e,i,n),fs(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new cs(ss(this.h),us(this.s),us(this.l),ns(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ns(this.opacity);return`${1===e?"hsl(":"hsla("}${ss(this.h)}, ${100*us(this.s)}%, ${100*us(this.l)}%${1===e?")":`, ${e})`}`}}));const ps=e=>()=>e;function hs(e,t){return function(r){return e+r*t}}function ys(e){return 1==(e=+e)?vs:function(t,r){return r-t?function(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}(t,r,e):ps(isNaN(t)?r:t)}}function vs(e,t){var r=t-e;return r?hs(e,r):ps(isNaN(e)?t:e)}const ms=function e(t){var r=ys(t);function n(e,t){var n=r((e=Qc(e)).r,(t=Qc(t)).r),i=r(e.g,t.g),a=r(e.b,t.b),o=vs(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return n.gamma=e,n}(1);function gs(e){return function(t){var r,n,i=t.length,a=new Array(i),o=new Array(i),l=new Array(i);for(r=0;r<i;++r)n=Qc(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}gs((function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ds((r-n/t)*t,o,i,a,l)}})),gs((function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ds((r-n/t)*t,i,a,o,l)}}));function bs(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=new Array(i),o=new Array(n);for(r=0;r<i;++r)a[r]=Ss(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}function xs(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function ws(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function Os(e,t){var r,n={},i={};for(r in null!==e&&"object"==typeof e||(e={}),null!==t&&"object"==typeof t||(t={}),t)r in e?n[r]=Ss(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}var Ps=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Es=new RegExp(Ps.source,"g");function As(e,t){var r,n,i,a=Ps.lastIndex=Es.lastIndex=0,o=-1,l=[],c=[];for(e+="",t+="";(r=Ps.exec(e))&&(n=Es.exec(t));)(i=n.index)>a&&(i=t.slice(a,i),l[o]?l[o]+=i:l[++o]=i),(r=r[0])===(n=n[0])?l[o]?l[o]+=n:l[++o]=n:(l[++o]=null,c.push({i:o,x:ws(r,n)})),a=Es.lastIndex;return a<t.length&&(i=t.slice(a),l[o]?l[o]+=i:l[++o]=i),l.length<2?c[0]?function(e){return function(t){return e(t)+""}}(c[0].x):function(e){return function(){return e}}(t):(t=c.length,function(e){for(var r,n=0;n<t;++n)l[(r=c[n]).i]=r.x(e);return l.join("")})}function js(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}}function Ss(e,t){var r,n=typeof t;return null==t||"boolean"===n?ps(t):("number"===n?ws:"string"===n?(r=Gc(t))?(t=r,ms):As:t instanceof Gc?ms:t instanceof Date?xs:function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}(t)?js:Array.isArray(t)?bs:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?Os:ws)(e,t)}function ks(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function Ms(e){return+e}var Ts=[0,1];function Ds(e){return e}function Cs(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:function(e){return function(){return e}}(isNaN(t)?NaN:.5)}function Is(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Cs(i,n),a=r(o,a)):(n=Cs(n,i),a=r(a,o)),function(e){return a(n(e))}}function Ns(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Cs(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=Dc(e,t,1,n)-1;return a[r](i[r](t))}}function _s(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Ls(){var e,t,r,n,i,a,o=Ts,l=Ts,c=Ss,s=Ds;function u(){var e=Math.min(o.length,l.length);return s!==Ds&&(s=function(e,t){var r;return e>t&&(r=e,e=t,t=r),function(r){return Math.max(e,Math.min(t,r))}}(o[0],o[e-1])),n=e>2?Ns:Is,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),l,c)))(e(s(t)))}return f.invert=function(r){return s(t((a||(a=n(l,o.map(e),ws)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,Ms),u()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=ks,u()},f.clamp=function(e){return arguments.length?(s=!!e||Ds,u()):s!==Ds},f.interpolate=function(e){return arguments.length?(c=e,u()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function Rs(){return Ls()(Ds,Ds)}var Ks,zs=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Bs(e){if(!(t=zs.exec(e)))throw new Error("invalid format: "+e);var t;return new Fs({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function Fs(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function Ws(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Us(e){return(e=Ws(Math.abs(e)))?e[1]:NaN}function Xs(e,t){var r=Ws(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}Bs.prototype=Fs.prototype,Fs.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Vs={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Xs(100*e,t),r:Xs,s:function(e,t){var r=Ws(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Ks=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Ws(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function $s(e){return e}var Hs,qs,Ys,Gs=Array.prototype.map,Zs=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Js(e){var t,r,n=void 0===e.grouping||void 0===e.thousands?$s:(t=Gs.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",o=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?$s:function(e){return function(t){return t.replace(/[0-9]/g,(function(t){return e[+t]}))}}(Gs.call(e.numerals,String)),c=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function f(e){var t=(e=Bs(e)).fill,r=e.align,f=e.sign,d=e.symbol,p=e.zero,h=e.width,y=e.comma,v=e.precision,m=e.trim,g=e.type;"n"===g?(y=!0,g="g"):Vs[g]||(void 0===v&&(v=12),m=!0,g="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===d?a:/[%p]/.test(g)?c:"",w=Vs[g],O=/[defgprs%]/.test(g);function P(e){var i,a,c,d=b,P=x;if("c"===g)P=w(e)+P,e="";else{var E=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),v),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),E&&0==+e&&"+"!==f&&(E=!1),d=(E?"("===f?f:s:"-"===f||"("===f?"":f)+d,P=("s"===g?Zs[8+Ks/3]:"")+P+(E&&"("===f?")":""),O)for(i=-1,a=e.length;++i<a;)if(48>(c=e.charCodeAt(i))||c>57){P=(46===c?o+e.slice(i+1):e.slice(i))+P,e=e.slice(0,i);break}}y&&!p&&(e=n(e,1/0));var A=d.length+e.length+P.length,j=A<h?new Array(h-A+1).join(t):"";switch(y&&p&&(e=n(j+e,j.length?h-P.length:1/0),j=""),r){case"<":e=d+e+P+j;break;case"=":e=d+j+e+P;break;case"^":e=j.slice(0,A=j.length>>1)+d+e+P+j.slice(A);break;default:e=j+d+e+P}return l(e)}return v=void 0===v?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),P.toString=function(){return e+""},P}return{format:f,formatPrefix:function(e,t){var r=f(((e=Bs(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(Us(t)/3))),i=Math.pow(10,-n),a=Zs[8+n/3];return function(e){return r(i*e)+a}}}}function Qs(e,t,r,n){var i,a=Pc(e,t,r);switch((n=Bs(null==n?",f":n)).type){case"s":var o=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=function(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Us(t)/3)))-Us(Math.abs(e)))}(a,o))||(n.precision=i),Ys(n,o);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=function(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Us(t)-Us(e))+1}(a,Math.max(Math.abs(e),Math.abs(t))))||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=function(e){return Math.max(0,-Us(Math.abs(e)))}(a))||(n.precision=i-2*("%"===n.type))}return qs(n)}function eu(e){var t=e.domain;return e.ticks=function(e){var r=t();return wc(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return Qs(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],s=a[l],u=10;for(s<c&&(i=c,c=s,s=i,i=o,o=l,l=i);u-- >0;){if((i=Oc(c,s,r))===n)return a[o]=c,a[l]=s,t(a);if(i>0)c=Math.floor(c/i)*i,s=Math.ceil(s/i)*i;else{if(!(i<0))break;c=Math.ceil(c*i)/i,s=Math.floor(s*i)/i}n=i}return e},e}function tu(){var e=Rs();return e.copy=function(){return _s(e,tu())},ac.apply(e,arguments),eu(e)}function ru(e){var t;function r(e){return null==e||isNaN(e=+e)?t:e}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(e=Array.from(t,Ms),r):e.slice()},r.unknown=function(e){return arguments.length?(t=e,r):t},r.copy=function(){return ru(e).unknown(t)},e=arguments.length?Array.from(e,Ms):[0,1],eu(r)}function nu(e,t){var r,n=0,i=(e=e.slice()).length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function iu(e){return Math.log(e)}function au(e){return Math.exp(e)}function ou(e){return-Math.log(-e)}function lu(e){return-Math.exp(-e)}function cu(e){return isFinite(e)?+("1e"+e):e<0?0:e}function su(e){return(t,r)=>-e(-t,r)}function uu(e){const t=e(iu,au),r=t.domain;let n,i,a=10;function o(){return n=function(e){return e===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}(a),i=function(e){return 10===e?cu:e===Math.E?Math.exp:t=>Math.pow(e,t)}(a),r()[0]<0?(n=su(n),i=su(i),e(ou,lu)):e(iu,au),t}return t.base=function(e){return arguments.length?(a=+e,o()):a},t.domain=function(e){return arguments.length?(r(e),o()):r()},t.ticks=e=>{const t=r();let o=t[0],l=t[t.length-1];const c=l<o;c&&([o,l]=[l,o]);let s,u,f=n(o),d=n(l);const p=null==e?10:+e;let h=[];if(!(a%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),o>0){for(;f<=d;++f)for(s=1;s<a;++s)if(u=f<0?s/i(-f):s*i(f),!(u<o)){if(u>l)break;h.push(u)}}else for(;f<=d;++f)for(s=a-1;s>=1;--s)if(u=f>0?s/i(-f):s*i(f),!(u<o)){if(u>l)break;h.push(u)}2*h.length<p&&(h=wc(o,l,p))}else h=wc(f,d,Math.min(d-f,p)).map(i);return c?h.reverse():h},t.tickFormat=(e,r)=>{if(null==e&&(e=10),null==r&&(r=10===a?"s":","),"function"!=typeof r&&(a%1||null!=(r=Bs(r)).precision||(r.trim=!0),r=qs(r)),e===1/0)return r;const o=Math.max(1,a*e/t.ticks().length);return e=>{let t=e/i(Math.round(n(e)));return t*a<a-.5&&(t*=a),t<=o?r(e):""}},t.nice=()=>r(nu(r(),{floor:e=>i(Math.floor(n(e))),ceil:e=>i(Math.ceil(n(e)))})),t}function fu(){const e=uu(Ls()).domain([1,10]);return e.copy=()=>_s(e,fu()).base(e.base()),ac.apply(e,arguments),e}function du(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function pu(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function hu(e){var t=1,r=e(du(t),pu(t));return r.constant=function(r){return arguments.length?e(du(t=+r),pu(t)):t},eu(r)}function yu(){var e=hu(Ls());return e.copy=function(){return _s(e,yu()).constant(e.constant())},ac.apply(e,arguments)}function vu(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function mu(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function gu(e){return e<0?-e*e:e*e}function bu(e){var t=e(Ds,Ds),r=1;return t.exponent=function(t){return arguments.length?1===(r=+t)?e(Ds,Ds):.5===r?e(mu,gu):e(vu(r),vu(1/r)):r},eu(t)}function xu(){var e=bu(Ls());return e.copy=function(){return _s(e,xu()).exponent(e.exponent())},ac.apply(e,arguments),e}function wu(){return xu.apply(null,arguments).exponent(.5)}function Ou(e){return Math.sign(e)*e*e}function Pu(){var e,t=Rs(),r=[0,1],n=!1;function i(r){var i=function(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}(t(r));return isNaN(i)?e:n?Math.round(i):i}return i.invert=function(e){return t.invert(Ou(e))},i.domain=function(e){return arguments.length?(t.domain(e),i):t.domain()},i.range=function(e){return arguments.length?(t.range((r=Array.from(e,Ms)).map(Ou)),i):r.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(n=!!e,i):n},i.clamp=function(e){return arguments.length?(t.clamp(e),i):t.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return Pu(t.domain(),r).round(n).clamp(t.clamp()).unknown(e)},ac.apply(i,arguments),eu(i)}function Eu(e,t){let r;if(void 0===t)for(const t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function Au(e,t){let r;if(void 0===t)for(const t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function ju(e=Ec){if(e===Ec)return Su;if("function"!=typeof e)throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}function Su(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function ku(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=void 0===i?Su:ju(i);n>r;){if(n-r>600){const a=n-r+1,o=t-r+1,l=Math.log(a),c=.5*Math.exp(2*l/3),s=.5*Math.sqrt(l*c*(a-c)/a)*(o-a/2<0?-1:1);ku(e,t,Math.max(r,Math.floor(t-o*c/a+s)),Math.min(n,Math.floor(t+(a-o)*c/a+s)),i)}const a=e[t];let o=r,l=n;for(Mu(e,r,t),i(e[n],a)>0&&Mu(e,r,n);o<l;){for(Mu(e,o,l),++o,--l;i(e[o],a)<0;)++o;for(;i(e[l],a)>0;)--l}0===i(e[r],a)?Mu(e,r,l):(++l,Mu(e,l,n)),l<=t&&(r=l+1),t<=l&&(n=l-1)}return e}function Mu(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function Tu(e,t,r){if(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,r)),(n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return Au(e);if(t>=1)return Eu(e);var n,i=(n-1)*t,a=Math.floor(i),o=Eu(ku(e,a).subarray(0,a+1));return o+(Au(e.subarray(a+1))-o)*(i-a)}}function Du(e,t,r=kc){if((n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}function Cu(){var e,t=[],r=[],n=[];function i(){var e=0,i=Math.max(1,r.length);for(n=new Array(i-1);++e<i;)n[e-1]=Du(t,e/i);return a}function a(t){return null==t||isNaN(t=+t)?e:r[Dc(n,t)]}return a.invertExtent=function(e){var i=r.indexOf(e);return i<0?[NaN,NaN]:[i>0?n[i-1]:t[0],i<n.length?n[i]:t[t.length-1]]},a.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(Ec),i()},a.range=function(e){return arguments.length?(r=Array.from(e),i()):r.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return n.slice()},a.copy=function(){return Cu().domain(t).range(r).unknown(e)},ac.apply(a,arguments)}function Iu(){var e,t=0,r=1,n=1,i=[.5],a=[0,1];function o(t){return null!=t&&t<=t?a[Dc(i,t,0,n)]:e}function l(){var e=-1;for(i=new Array(n);++e<n;)i[e]=((e+1)*r-(e-n)*t)/(n+1);return o}return o.domain=function(e){return arguments.length?([t,r]=e,t=+t,r=+r,l()):[t,r]},o.range=function(e){return arguments.length?(n=(a=Array.from(e)).length-1,l()):a.slice()},o.invertExtent=function(e){var o=a.indexOf(e);return o<0?[NaN,NaN]:o<1?[t,i[0]]:o>=n?[i[n-1],r]:[i[o-1],i[o]]},o.unknown=function(t){return arguments.length?(e=t,o):o},o.thresholds=function(){return i.slice()},o.copy=function(){return Iu().domain([t,r]).range(a).unknown(e)},ac.apply(eu(o),arguments)}function Nu(){var e,t=[.5],r=[0,1],n=1;function i(i){return null!=i&&i<=i?r[Dc(t,i,0,n)]:e}return i.domain=function(e){return arguments.length?(t=Array.from(e),n=Math.min(t.length,r.length-1),i):t.slice()},i.range=function(e){return arguments.length?(r=Array.from(e),n=Math.min(t.length,r.length-1),i):r.slice()},i.invertExtent=function(e){var n=r.indexOf(e);return[t[n-1],t[n]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return Nu().domain(t).range(r).unknown(e)},ac.apply(i,arguments)}Hs=Js({thousands:",",grouping:[3],currency:["$",""]}),qs=Hs.format,Ys=Hs.formatPrefix;const _u=1e3,Lu=6e4,Ru=36e5,Ku=864e5,zu=6048e5,Bu=2592e6,Fu=31536e6,Wu=new Date,Uu=new Date;function Xu(e,t,r,n){function i(t){return e(t=0===arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{const t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{const o=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n&&a>0))return o;let l;do{o.push(l=new Date(+r)),t(r,a),e(r)}while(l<r&&r<n);return o},i.filter=r=>Xu((t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)}),((e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););})),r&&(i.count=(t,n)=>(Wu.setTime(+t),Uu.setTime(+n),e(Wu),e(Uu),Math.floor(r(Wu,Uu))),i.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null)),i}const Vu=Xu((()=>{}),((e,t)=>{e.setTime(+e+t)}),((e,t)=>t-e));Vu.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?Xu((t=>{t.setTime(Math.floor(t/e)*e)}),((t,r)=>{t.setTime(+t+r*e)}),((t,r)=>(r-t)/e)):Vu:null);Vu.range;const $u=Xu((e=>{e.setTime(e-e.getMilliseconds())}),((e,t)=>{e.setTime(+e+t*_u)}),((e,t)=>(t-e)/_u),(e=>e.getUTCSeconds())),Hu=($u.range,Xu((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*_u)}),((e,t)=>{e.setTime(+e+t*Lu)}),((e,t)=>(t-e)/Lu),(e=>e.getMinutes()))),qu=(Hu.range,Xu((e=>{e.setUTCSeconds(0,0)}),((e,t)=>{e.setTime(+e+t*Lu)}),((e,t)=>(t-e)/Lu),(e=>e.getUTCMinutes()))),Yu=(qu.range,Xu((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*_u-e.getMinutes()*Lu)}),((e,t)=>{e.setTime(+e+t*Ru)}),((e,t)=>(t-e)/Ru),(e=>e.getHours()))),Gu=(Yu.range,Xu((e=>{e.setUTCMinutes(0,0,0)}),((e,t)=>{e.setTime(+e+t*Ru)}),((e,t)=>(t-e)/Ru),(e=>e.getUTCHours()))),Zu=(Gu.range,Xu((e=>e.setHours(0,0,0,0)),((e,t)=>e.setDate(e.getDate()+t)),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Lu)/Ku),(e=>e.getDate()-1))),Ju=(Zu.range,Xu((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/Ku),(e=>e.getUTCDate()-1))),Qu=(Ju.range,Xu((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/Ku),(e=>Math.floor(e/Ku))));Qu.range;function ef(e){return Xu((t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)}),((e,t)=>{e.setDate(e.getDate()+7*t)}),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Lu)/zu))}const tf=ef(0),rf=ef(1),nf=ef(2),af=ef(3),of=ef(4),lf=ef(5),cf=ef(6);tf.range,rf.range,nf.range,af.range,of.range,lf.range,cf.range;function sf(e){return Xu((t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)}),((e,t)=>(t-e)/zu))}const uf=sf(0),ff=sf(1),df=sf(2),pf=sf(3),hf=sf(4),yf=sf(5),vf=sf(6),mf=(uf.range,ff.range,df.range,pf.range,hf.range,yf.range,vf.range,Xu((e=>{e.setDate(1),e.setHours(0,0,0,0)}),((e,t)=>{e.setMonth(e.getMonth()+t)}),((e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear())),(e=>e.getMonth()))),gf=(mf.range,Xu((e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)}),((e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear())),(e=>e.getUTCMonth()))),bf=(gf.range,Xu((e=>{e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,t)=>{e.setFullYear(e.getFullYear()+t)}),((e,t)=>t.getFullYear()-e.getFullYear()),(e=>e.getFullYear())));bf.every=e=>isFinite(e=Math.floor(e))&&e>0?Xu((t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,r)=>{t.setFullYear(t.getFullYear()+r*e)})):null;bf.range;const xf=Xu((e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)}),((e,t)=>t.getUTCFullYear()-e.getUTCFullYear()),(e=>e.getUTCFullYear()));xf.every=e=>isFinite(e=Math.floor(e))&&e>0?Xu((t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)})):null;xf.range;function wf(e,t,r,n,i,a){const o=[[$u,1,_u],[$u,5,5e3],[$u,15,15e3],[$u,30,3e4],[a,1,Lu],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,Ru],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,Ku],[n,2,1728e5],[r,1,zu],[t,1,Bu],[t,3,7776e6],[e,1,Fu]];function l(t,r,n){const i=Math.abs(r-t)/n,a=jc((([,,e])=>e)).right(o,i);if(a===o.length)return e.every(Pc(t/Fu,r/Fu,n));if(0===a)return Vu.every(Math.max(Pc(t,r,n),1));const[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){const n=t<e;n&&([e,t]=[t,e]);const i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}const[Of,Pf]=wf(xf,gf,uf,Qu,Gu,qu),[Ef,Af]=wf(bf,mf,tf,Zu,Yu,Hu);function jf(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Sf(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function kf(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var Mf,Tf,Df,Cf={"-":"",_:" ",0:"0"},If=/^\s*\d+/,Nf=/^%/,_f=/[\\^$*+?|[\]().{}]/g;function Lf(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function Rf(e){return e.replace(_f,"\\$&")}function Kf(e){return new RegExp("^(?:"+e.map(Rf).join("|")+")","i")}function zf(e){return new Map(e.map(((e,t)=>[e.toLowerCase(),t])))}function Bf(e,t,r){var n=If.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function Ff(e,t,r){var n=If.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function Wf(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function Uf(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function Xf(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Vf(e,t,r){var n=If.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function $f(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function Hf(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function qf(e,t,r){var n=If.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function Yf(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Gf(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function Zf(e,t,r){var n=If.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Jf(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function Qf(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function ed(e,t,r){var n=If.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function td(e,t,r){var n=If.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function rd(e,t,r){var n=If.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function nd(e,t,r){var n=Nf.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function id(e,t,r){var n=If.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function ad(e,t,r){var n=If.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function od(e,t){return Lf(e.getDate(),t,2)}function ld(e,t){return Lf(e.getHours(),t,2)}function cd(e,t){return Lf(e.getHours()%12||12,t,2)}function sd(e,t){return Lf(1+Zu.count(bf(e),e),t,3)}function ud(e,t){return Lf(e.getMilliseconds(),t,3)}function fd(e,t){return ud(e,t)+"000"}function dd(e,t){return Lf(e.getMonth()+1,t,2)}function pd(e,t){return Lf(e.getMinutes(),t,2)}function hd(e,t){return Lf(e.getSeconds(),t,2)}function yd(e){var t=e.getDay();return 0===t?7:t}function vd(e,t){return Lf(tf.count(bf(e)-1,e),t,2)}function md(e){var t=e.getDay();return t>=4||0===t?of(e):of.ceil(e)}function gd(e,t){return e=md(e),Lf(of.count(bf(e),e)+(4===bf(e).getDay()),t,2)}function bd(e){return e.getDay()}function xd(e,t){return Lf(rf.count(bf(e)-1,e),t,2)}function wd(e,t){return Lf(e.getFullYear()%100,t,2)}function Od(e,t){return Lf((e=md(e)).getFullYear()%100,t,2)}function Pd(e,t){return Lf(e.getFullYear()%1e4,t,4)}function Ed(e,t){var r=e.getDay();return Lf((e=r>=4||0===r?of(e):of.ceil(e)).getFullYear()%1e4,t,4)}function Ad(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Lf(t/60|0,"0",2)+Lf(t%60,"0",2)}function jd(e,t){return Lf(e.getUTCDate(),t,2)}function Sd(e,t){return Lf(e.getUTCHours(),t,2)}function kd(e,t){return Lf(e.getUTCHours()%12||12,t,2)}function Md(e,t){return Lf(1+Ju.count(xf(e),e),t,3)}function Td(e,t){return Lf(e.getUTCMilliseconds(),t,3)}function Dd(e,t){return Td(e,t)+"000"}function Cd(e,t){return Lf(e.getUTCMonth()+1,t,2)}function Id(e,t){return Lf(e.getUTCMinutes(),t,2)}function Nd(e,t){return Lf(e.getUTCSeconds(),t,2)}function _d(e){var t=e.getUTCDay();return 0===t?7:t}function Ld(e,t){return Lf(uf.count(xf(e)-1,e),t,2)}function Rd(e){var t=e.getUTCDay();return t>=4||0===t?hf(e):hf.ceil(e)}function Kd(e,t){return e=Rd(e),Lf(hf.count(xf(e),e)+(4===xf(e).getUTCDay()),t,2)}function zd(e){return e.getUTCDay()}function Bd(e,t){return Lf(ff.count(xf(e)-1,e),t,2)}function Fd(e,t){return Lf(e.getUTCFullYear()%100,t,2)}function Wd(e,t){return Lf((e=Rd(e)).getUTCFullYear()%100,t,2)}function Ud(e,t){return Lf(e.getUTCFullYear()%1e4,t,4)}function Xd(e,t){var r=e.getUTCDay();return Lf((e=r>=4||0===r?hf(e):hf.ceil(e)).getUTCFullYear()%1e4,t,4)}function Vd(){return"+0000"}function $d(){return"%"}function Hd(e){return+e}function qd(e){return Math.floor(+e/1e3)}function Yd(e){return new Date(e)}function Gd(e){return e instanceof Date?+e:+new Date(+e)}function Zd(e,t,r,n,i,a,o,l,c,s){var u=Rs(),f=u.invert,d=u.domain,p=s(".%L"),h=s(":%S"),y=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),g=s("%b %d"),b=s("%B"),x=s("%Y");function w(e){return(c(e)<e?p:l(e)<e?h:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?m:g:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,Gd)):d().map(Yd)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:s(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(nu(r,e)):u},u.copy=function(){return _s(u,Zd(e,t,r,n,i,a,o,l,c,s))},u}function Jd(){return ac.apply(Zd(Ef,Af,bf,mf,tf,Zu,Yu,Hu,$u,Tf).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function Qd(){return ac.apply(Zd(Of,Pf,xf,gf,uf,Ju,Gu,qu,$u,Df).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function ep(){var e,t,r,n,i,a=0,o=1,l=Ds,c=!1;function s(t){return null==t||isNaN(t=+t)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),s):[l(0),l(1)]}}return s.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),s):[a,o]},s.clamp=function(e){return arguments.length?(c=!!e,s):c},s.interpolator=function(e){return arguments.length?(l=e,s):l},s.range=u(Ss),s.rangeRound=u(ks),s.unknown=function(e){return arguments.length?(i=e,s):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),s}}function tp(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function rp(){var e=eu(ep()(Ds));return e.copy=function(){return tp(e,rp())},oc.apply(e,arguments)}function np(){var e=uu(ep()).domain([1,10]);return e.copy=function(){return tp(e,np()).base(e.base())},oc.apply(e,arguments)}function ip(){var e=hu(ep());return e.copy=function(){return tp(e,ip()).constant(e.constant())},oc.apply(e,arguments)}function ap(){var e=bu(ep());return e.copy=function(){return tp(e,ap()).exponent(e.exponent())},oc.apply(e,arguments)}function op(){return ap.apply(null,arguments).exponent(.5)}function lp(){var e=[],t=Ds;function r(r){if(null!=r&&!isNaN(r=+r))return t((Dc(e,r,1)-1)/(e.length-1))}return r.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(Ec),r},r.interpolator=function(e){return arguments.length?(t=e,r):t},r.range=function(){return e.map(((r,n)=>t(n/(e.length-1))))},r.quantiles=function(t){return Array.from({length:t+1},((r,n)=>Tu(e,n/t)))},r.copy=function(){return lp(t).domain(e)},oc.apply(r,arguments)}function cp(){var e,t,r,n,i,a,o,l=0,c=.5,s=1,u=1,f=Ds,d=!1;function p(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(u*e<u*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=Ss);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([l,c,s]=o,e=a(l=+l),t=a(c=+c),r=a(s=+s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,p):[l,c,s]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(Ss),p.rangeRound=h(ks),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(l),t=o(c),r=o(s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,p}}function sp(){var e=eu(cp()(Ds));return e.copy=function(){return tp(e,sp())},oc.apply(e,arguments)}function up(){var e=uu(cp()).domain([.1,1,10]);return e.copy=function(){return tp(e,up()).base(e.base())},oc.apply(e,arguments)}function fp(){var e=hu(cp());return e.copy=function(){return tp(e,fp()).constant(e.constant())},oc.apply(e,arguments)}function dp(){var e=bu(cp());return e.copy=function(){return tp(e,dp()).exponent(e.exponent())},oc.apply(e,arguments)}function pp(){return dp.apply(null,arguments).exponent(.5)}!function(e){Mf=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,s=Kf(i),u=zf(i),f=Kf(a),d=zf(a),p=Kf(o),h=zf(o),y=Kf(l),v=zf(l),m=Kf(c),g=zf(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:od,e:od,f:fd,g:Od,G:Ed,H:ld,I:cd,j:sd,L:ud,m:dd,M:pd,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:Hd,s:qd,S:hd,u:yd,U:vd,V:gd,w:bd,W:xd,x:null,X:null,y:wd,Y:Pd,Z:Ad,"%":$d},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:jd,e:jd,f:Dd,g:Wd,G:Xd,H:Sd,I:kd,j:Md,L:Td,m:Cd,M:Id,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:Hd,s:qd,S:Nd,u:_d,U:Ld,V:Kd,w:zd,W:Bd,x:null,X:null,y:Fd,Y:Ud,Z:Vd,"%":$d},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return E(e,t,r,n)},d:Gf,e:Gf,f:rd,g:$f,G:Vf,H:Jf,I:Jf,j:Zf,L:td,m:Yf,M:Qf,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:qf,Q:id,s:ad,S:ed,u:Ff,U:Wf,V:Uf,w:Bf,W:Xf,x:function(e,t,n){return E(e,r,t,n)},X:function(e,t,r){return E(e,n,t,r)},y:$f,Y:Vf,Z:Hf,"%":nd};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++l<s;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=Cf[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function P(e,t){return function(r){var n,i,a=kf(1900,void 0,1);if(E(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(t&&!("Z"in a)&&(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(i=(n=Sf(kf(a.y,0,1))).getUTCDay(),n=i>4||0===i?ff.ceil(n):ff(n),n=Ju.offset(n,7*(a.V-1)),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(i=(n=jf(kf(a.y,0,1))).getDay(),n=i>4||0===i?rf.ceil(n):rf(n),n=Zu.offset(n,7*(a.V-1)),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?Sf(kf(a.y,0,1)).getUTCDay():jf(kf(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,Sf(a)):jf(a)}}function E(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return-1;if(37===(i=t.charCodeAt(o++))){if(i=t.charAt(o++),!(a=w[i in Cf?t.charAt(o++):i])||(n=a(e,r,n))<0)return-1}else if(i!=r.charCodeAt(n++))return-1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=P(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=P(e+="",!0);return t.toString=function(){return e},t}}}(e),Tf=Mf.format,Mf.parse,Df=Mf.utcFormat,Mf.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var hp=e=>e.chartData,yp=et([hp],(e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}})),vp=(e,t,r,n)=>n?yp(e):hp(e);function mp(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(Ho(t)&&Ho(r))return!0}return!1}function gp(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var bp=a(8351),xp=a.n(bp),wp=e=>e,Op={"@@functional/placeholder":!0},Pp=e=>e===Op,Ep=e=>function t(){return 0===arguments.length||1===arguments.length&&Pp(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},Ap=(e,t)=>1===e?t:Ep((function(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter((e=>e!==Op)).length;return a>=e?t(...n):Ap(e-a,Ep((function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];var a=n.map((e=>Pp(e)?r.shift():e));return t(...a,...r)})))})),jp=e=>Ap(e.length,e),Sp=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},kp=jp(((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map((e=>t[e])).map(e))),Mp=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),Tp=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every(((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])}))?r:(t=i,r=e(...i))}};function Dp(e){return 0===e?1:Math.floor(new(xp())(e).abs().log(10).toNumber())+1}function Cp(e,t,r){for(var n=new(xp())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}jp(((e,t,r)=>{var n=+e;return n+r*(+t-n)})),jp(((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)})),jp(((e,t,r)=>{var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))}));var Ip=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},Np=(e,t,r)=>{if(e.lte(0))return new(xp())(0);var n=Dp(e.toNumber()),i=new(xp())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(xp())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return t?new(xp())(l.toNumber()):new(xp())(Math.ceil(l.toNumber()))},_p=(e,t,r)=>{var n=new(xp())(1),i=new(xp())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(xp())(10).pow(Dp(e)-1),i=new(xp())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(xp())(Math.floor(e)))}else 0===e?i=new(xp())(Math.floor((t-1)/2)):r||(i=new(xp())(Math.floor(e)));var o=Math.floor((t-1)/2),l=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return wp;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce(((e,t)=>t(e)),i(...arguments))}}(kp((e=>i.add(new(xp())(e-o).mul(n)).toNumber())),Sp);return l(0,t)},Lp=function(e,t,r,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(xp())(0),tickMin:new(xp())(0),tickMax:new(xp())(0)};var a,o=Np(new(xp())(t).sub(e).div(r-1),n,i);a=e<=0&&t>=0?new(xp())(0):(a=new(xp())(e).add(t).div(2)).sub(new(xp())(a).mod(o));var l=Math.ceil(a.sub(e).div(o).toNumber()),c=Math.ceil(new(xp())(t).sub(a).div(o).toNumber()),s=l+c+1;return s>r?Lp(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,l=t>0?l:l+(r-s)),{step:o,tickMin:a.sub(new(xp())(l).mul(o)),tickMax:a.add(new(xp())(c).mul(o))})};var Rp=Tp((function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(n,2),[o,l]=Ip([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...Sp(0,n-1).map((()=>1/0))]:[...Sp(0,n-1).map((()=>-1/0)),l];return t>r?Mp(c):c}if(o===l)return _p(o,n,i);var{step:s,tickMin:u,tickMax:f}=Lp(o,l,a,i,0),d=Cp(u,f.add(new(xp())(.1).mul(s)),s);return t>r?Mp(d):d})),Kp=Tp((function(e,t){var[r,n]=e,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],[a,o]=Ip([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=Np(new(xp())(o).sub(a).div(l-1),i,0),s=[...Cp(new(xp())(a),new(xp())(o),c),o];return!1===i&&(s=s.map((e=>Math.round(e)))),r>n?Mp(s):s})),zp=e=>e.rootProps.maxBarSize,Bp=e=>e.rootProps.barGap,Fp=e=>e.rootProps.barCategoryGap,Wp=e=>e.rootProps.barSize,Up=e=>e.rootProps.stackOffset,Xp=e=>e.options.chartName,Vp=e=>e.rootProps.syncId,$p=e=>e.rootProps.syncMethod,Hp=e=>e.options.eventEmitter,qp={allowDuplicatedCategory:!0,angleAxisId:0,axisLine:!0,cx:0,cy:0,orientation:"outer",reversed:!1,scale:"auto",tick:!0,tickLine:!0,tickSize:8,type:"category"},Yp={allowDataOverflow:!1,allowDuplicatedCategory:!0,angle:0,axisLine:!0,cx:0,cy:0,orientation:"right",radiusAxisId:0,scale:"auto",stroke:"#ccc",tick:!0,tickCount:5,type:"number"},Gp=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},Zp={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:qp.angleAxisId,includeHidden:!1,name:void 0,reversed:qp.reversed,scale:qp.scale,tick:qp.tick,tickCount:void 0,ticks:void 0,type:qp.type,unit:void 0},Jp={allowDataOverflow:Yp.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Yp.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Yp.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Yp.scale,tick:Yp.tick,tickCount:Yp.tickCount,ticks:void 0,type:Yp.type,unit:void 0},Qp={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:qp.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:qp.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:qp.scale,tick:qp.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},eh={allowDataOverflow:Yp.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Yp.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Yp.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Yp.scale,tick:Yp.tick,tickCount:Yp.tickCount,ticks:void 0,type:"category",unit:void 0},th=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?Qp:Zp,rh=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?eh:Jp,nh=e=>e.polarOptions,ih=et([ji,Si,Ki],Qn),ah=et([nh,ih],((e,t)=>{if(null!=e)return v(e.innerRadius,t,0)})),oh=et([nh,ih],((e,t)=>{if(null!=e)return v(e.outerRadius,t,.8*t)})),lh=et([nh],(e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]})),ch=et([th,lh],Gp),sh=et([ih,ah,oh],((e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]})),uh=et([rh,sh],Gp),fh=et([Ji,nh,ah,oh,ji,Si],((e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:s}=t;return{cx:v(o,i,i/2),cy:v(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})),dh=(e,t)=>t,ph=(e,t,r)=>r;function hh(e){return null==e?void 0:e.id}var yh=e=>{var t=Ji(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},vh=e=>e.tooltip.settings.axisId,mh=e=>{var t=yh(e),r=vh(e);return Dh(e,t,r)};function gh(e,t,r){var{chartData:n=[]}=t,i=null==r?void 0:r.dataKey,a=new Map;return e.forEach((e=>{var t,r=null!==(t=e.data)&&void 0!==t?t:n;if(null!=r&&0!==r.length){var o=hh(e);r.forEach(((t,r)=>{var n,l=null==i?r:String(ci(t,i,null)),c=ci(t,e.dataKey,0);n=a.has(l)?a.get(l):{},Object.assign(n,{[o]:c}),a.set(l,n)}))}})),Array.from(a.values())}function bh(e){return null!=e.stackId&&null!=e.dataKey}function xh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function wh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xh(Object(r),!0).forEach((function(t){Oh(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xh(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Oh(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ph=[0,"auto"],Eh={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},Ah=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?Eh:r},jh={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:Ph,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},Sh=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?jh:r},kh={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},Mh=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?kh:r},Th=(e,t,r)=>{switch(t){case"xAxis":return Ah(e,r);case"yAxis":return Sh(e,r);case"zAxis":return Mh(e,r);case"angleAxis":return th(e,r);case"radiusAxis":return rh(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Dh=(e,t,r)=>{switch(t){case"xAxis":return Ah(e,r);case"yAxis":return Sh(e,r);case"angleAxis":return th(e,r);case"radiusAxis":return rh(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Ch=e=>e.graphicalItems.cartesianItems.some((e=>"bar"===e.type))||e.graphicalItems.polarItems.some((e=>"radialBar"===e.type));function Ih(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var Nh=e=>e.graphicalItems.cartesianItems,_h=et([dh,ph],Ih),Lh=(e,t,r)=>e.filter(r).filter((e=>!0===(null==t?void 0:t.includeHidden)||!e.hide)),Rh=et([Nh,Th,_h],Lh),Kh=et([Rh],(e=>e.filter((e=>"area"===e.type||"bar"===e.type)).filter(bh))),zh=e=>e.filter((e=>!("stackId"in e)||void 0===e.stackId)),Bh=et([Rh],zh),Fh=e=>e.map((e=>e.data)).filter(Boolean).flat(1),Wh=et([Rh],Fh),Uh=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},Xh=et([Wh,vp],Uh),Vh=(e,t,r)=>null!=(null==t?void 0:t.dataKey)?e.map((e=>({value:ci(e,t.dataKey)}))):r.length>0?r.map((e=>e.dataKey)).flatMap((t=>e.map((e=>({value:ci(e,t)}))))):e.map((e=>({value:e}))),$h=et([Xh,Th,Rh],Vh);function Hh(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function qh(e){return e.filter((e=>p(e)||e instanceof Date)).map(Number).filter((e=>!1===u(e)))}function Yh(e,t,r){return!r||"number"!=typeof t||u(t)?[]:r.length?qh(r.flatMap((r=>{var n,i,a=ci(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,Ho(n)&&Ho(i))return[t-n,t+i]}))):[]}var Gh=et([Kh,vp,mh],gh),Zh=(e,t,r)=>{var n=t.reduce(((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e)),{});return Object.fromEntries(Object.entries(n).map((t=>{var[n,i]=t,a=i.map(hh);return[n,{stackedData:yi(e,a,r),graphicalItems:i}]})))},Jh=et([Gh,Kh,Up],Zh),Qh=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=xi(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ey=et([Jh,hp,dh],Qh),ty=(e,t,r,n,i)=>r.length>0?e.flatMap((e=>r.flatMap((r=>{var a,o,l=null===(a=n[r.id])||void 0===a?void 0:a.filter((e=>Hh(i,e))),c=ci(e,null!==(o=t.dataKey)&&void 0!==o?o:r.dataKey);return{value:c,errorDomain:Yh(e,c,l)}})))).filter(Boolean):null!=(null==t?void 0:t.dataKey)?e.map((e=>({value:ci(e,t.dataKey),errorDomain:[]}))):e.map((e=>({value:e,errorDomain:[]}))),ry=e=>e.errorBars,ny=(e,t,r)=>e.flatMap((e=>t[e.id])).filter(Boolean).filter((e=>Hh(r,e))),iy=(et([Bh,ry,dh],ny),et([Xh,Th,Bh,ry,dh],ty));function ay(e){var{value:t}=e;if(p(t)||t instanceof Date)return t}var oy=e=>{var t=e.flatMap((e=>[e.value,e.errorDomain])).flat(1),r=qh(t);if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ly=e=>{var t;if(null==e||!("domain"in e))return Ph;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=qh(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null==e?void 0:e.domain)&&void 0!==t?t:Ph},cy=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},sy=e=>e.referenceElements.dots,uy=(e,t,r)=>e.filter((e=>"extendDomain"===e.ifOverflow)).filter((e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r)),fy=et([sy,dh,ph],uy),dy=e=>e.referenceElements.areas,py=et([dy,dh,ph],uy),hy=e=>e.referenceElements.lines,yy=et([hy,dh,ph],uy),vy=(e,t)=>{var r=qh(e.map((e=>"xAxis"===t?e.x:e.y)));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},my=et(fy,dh,vy),gy=(e,t)=>{var r=qh(e.flatMap((e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2])));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},by=et([py,dh],gy),xy=(e,t)=>{var r=qh(e.map((e=>"xAxis"===t?e.x:e.y)));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},wy=et(yy,dh,xy),Oy=et(my,wy,by,((e,t,r)=>cy(e,r,t))),Py=et([Th],ly),Ey=(e,t,r,n,i,a,o)=>{var l=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(Ho(i))r=i;else if("function"==typeof i)return;if(Ho(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(mp(o))return o}}(t,e.allowDataOverflow);return null!=l?l:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(mp(n))return gp(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(d(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&wi.test(o)){var c=wi.exec(o);if(null==c||null==t)i=void 0;else{var s=+c[1];i=t[0]-s}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(d(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&Oi.test(l)){var u=Oi.exec(l);if(null==u||null==t)a=void 0;else{var f=+u[1];a=t[1]+f}}else a=null==t?void 0:t[1];var p=[i,a];if(mp(p))return null==t?p:gp(p,t,r)}}}(t,"vertical"===a&&"xAxis"===o||"horizontal"===a&&"yAxis"===o?cy(r,i,oy(n)):cy(i,oy(n)),e.allowDataOverflow)},Ay=et([Th,Py,ey,iy,Oy,Ji,dh],Ey),jy=[0,1],Sy=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length||void 0!==o){var{dataKey:l,type:c}=e,s=si(t,a);return s&&null==l?ic()(0,r.length):"category"===c?((e,t,r)=>{var n=e.map(ay).filter((e=>null!=e));return r&&(null==t.dataKey||t.allowDuplicatedCategory&&m(n))?ic()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,s):"expand"===i?jy:o}},ky=et([Th,Ji,Xh,$h,Up,dh,Ay],Sy),My=(t,r,n,i,a)=>{if(null!=t){var{scale:o,type:l}=t;if("auto"===o)return"radial"===r&&"radiusAxis"===a?"band":"radial"===r&&"angleAxis"===a?"linear":"category"===l&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!n)?"point":"category"===l?"band":"linear";if("string"==typeof o){var c="scale".concat(P(o));return c in e?c:"point"}}},Ty=et([Th,Ji,Ch,Xp,dh],My);function Dy(t,r,n,i){if(null!=n&&null!=i){if("function"==typeof t.scale)return t.scale.copy().domain(n).range(i);var a=function(t){if(null!=t){if(t in e)return e[t]();var r="scale".concat(P(t));return r in e?e[r]():void 0}}(r);if(null!=a){var o=a.domain(n).range(i);return(e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-di,a=Math.max(n[0],n[1])+di,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}})(o),o}}}var Cy=(e,t,r)=>{var n=ly(t);if("auto"===r||"linear"===r)return null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&mp(e)?Rp(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&mp(e)?Kp(e,t.tickCount,t.allowDecimals):void 0},Iy=et([ky,Dh,Ty],Cy),Ny=(e,t,r,n)=>{if("angleAxis"!==n&&"number"===(null==e?void 0:e.type)&&mp(t)&&Array.isArray(r)&&r.length>0){var i=t[0],a=r[0],o=t[1],l=r[r.length-1];return[Math.min(i,a),Math.max(o,l)]}return t},_y=et([Th,ky,Iy,dh],Ny),Ly=et($h,Th,((e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(qh(e.map((e=>e.value)))).sort(((e,t)=>e-t));if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++){var o=n[a+1]-n[a];r=Math.min(r,o)}return r/i}})),Ry=et(Ly,Ji,Fp,Ki,((e,t,r,n)=>n),((e,t,r,n,i)=>{if(!Ho(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=v(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0})),Ky=et(Ah,((e,t)=>{var r=Ah(e,t);return null==r||"string"!=typeof r.padding?0:Ry(e,"xAxis",t,r.padding)}),((e,t)=>{var r,n;if(null==e)return{left:0,right:0};var{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+t,right:(null!==(n=i.right)&&void 0!==n?n:0)+t}})),zy=et(Sh,((e,t)=>{var r=Sh(e,t);return null==r||"string"!=typeof r.padding?0:Ry(e,"yAxis",t,r.padding)}),((e,t)=>{var r,n;if(null==e)return{top:0,bottom:0};var{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+t}})),By=et([Ki,Ky,Vi,Xi,(e,t,r)=>r],((e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]})),Fy=et([Ki,Ji,zy,Vi,Xi,(e,t,r)=>r],((e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]})),Wy=(e,t,r,n)=>{var i;switch(t){case"xAxis":return By(e,r,n);case"yAxis":return Fy(e,r,n);case"zAxis":return null===(i=Mh(e,r))||void 0===i?void 0:i.range;case"angleAxis":return lh(e);case"radiusAxis":return sh(e,r);default:return}},Uy=et([Th,Wy],Gp),Xy=et([Th,Ty,_y,Uy],Dy);et([Rh,ry,dh],ny);function Vy(e,t){return e.id<t.id?-1:e.id>t.id?1:0}var $y=(e,t)=>t,Hy=(e,t,r)=>r,qy=et(Ti,$y,Hy,((e,t,r)=>e.filter((e=>e.orientation===t)).filter((e=>e.mirror===r)).sort(Vy))),Yy=et(Di,$y,Hy,((e,t,r)=>e.filter((e=>e.orientation===t)).filter((e=>e.mirror===r)).sort(Vy))),Gy=(e,t)=>({width:e.width,height:t.height}),Zy=et(Ki,Ah,Gy),Jy=et(Si,Ki,qy,$y,Hy,((e,t,r,n,i)=>{var a,o={};return r.forEach((r=>{var l=Gy(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height})),o})),Qy=et(ji,Ki,Yy,$y,Hy,((e,t,r,n,i)=>{var a,o={};return r.forEach((r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width})),o})),ev=et(Ki,Sh,((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))),tv=(e,t,r)=>{switch(t){case"xAxis":return Zy(e,r).width;case"yAxis":return ev(e,r).height;default:return}},rv=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=si(e,n),c=t.map((e=>e.value));return o&&l&&"category"===a&&i&&m(c)?c:void 0}},nv=et([Ji,$h,Th,dh],rv),iv=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;return!si(e,n)||"number"!==i&&"auto"===a?void 0:t.map((e=>e.value))}},av=et([Ji,$h,Dh,dh],iv),ov=et([Ji,(e,t,r)=>{switch(t){case"xAxis":return Ah(e,r);case"yAxis":return Sh(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Ty,Xy,nv,av,Wy,Iy,dh],((e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var s=si(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:s,niceTicks:l,range:o,realScaleType:r,scale:n}})),lv=(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var f=si(e,c),{type:d,ticks:p,tickCount:h}=t,y="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,v="category"===d&&n.bandwidth?n.bandwidth()/y:0;v="angleAxis"===c&&null!=a&&a.length>=2?2*s(a[0]-a[1])*v:v;var m=p||i;return m?m.map(((e,t)=>{var r=o?o.indexOf(e):e;return{index:t,coordinate:n(r)+v,value:e,offset:v}})).filter((e=>!u(e.coordinate))):f&&l?l.map(((e,t)=>({coordinate:n(e)+v,value:e,index:t,offset:v}))):n.ticks?n.ticks(h).map((e=>({coordinate:n(e)+v,value:e,offset:v}))):n.domain().map(((e,t)=>({coordinate:n(e)+v,value:o?o[e]:e,index:t,offset:v})))}},cv=et([Ji,Dh,Ty,Xy,Iy,Wy,nv,av,dh],lv),sv=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=si(e,o),{tickCount:c}=t,u=0;return u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*s(n[0]-n[1])*u:u,l&&a?a.map(((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u}))):r.ticks?r.ticks(c).map((e=>({coordinate:r(e)+u,value:e,offset:u}))):r.domain().map(((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u})))}},uv=et([Ji,Dh,Xy,Wy,nv,av,dh],sv),fv=et(Th,Xy,((e,t)=>{if(null!=e&&null!=t)return wh(wh({},e),{},{scale:t})})),dv=et([Th,Ty,ky,Uy],Dy),pv=et(((e,t,r)=>Mh(e,r)),dv,((e,t)=>{if(null!=e&&null!=t)return wh(wh({},e),{},{scale:t})})),hv=et([Ji,Ti,Di],((e,t,r)=>{switch(e){case"horizontal":return t.some((e=>e.reversed))?"right-to-left":"left-to-right";case"vertical":return r.some((e=>e.reversed))?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})),yv=e=>e.options.defaultTooltipEventType,vv=e=>e.options.validateTooltipEventTypes;function mv(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function gv(e,t){return mv(t,yv(e),vv(e))}var bv=(e,t)=>{var r,n=Number(t);if(!u(n)&&null!=t)return n>=0?null==e||null===(r=e[n])||void 0===r?void 0:r.value:void 0},xv={active:!1,index:null,dataKey:void 0,coordinate:void 0},wv=Gr({name:"tooltip",initialState:{itemInteraction:{click:xv,hover:xv},axisInteraction:{click:xv,hover:xv},keyboardInteraction:xv,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=Ft(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:Ov,removeTooltipEntrySettings:Pv,setTooltipSettingsState:Ev,setActiveMouseOverItemIndex:Av,mouseLeaveItem:jv,mouseLeaveChart:Sv,setActiveClickItemIndex:kv,setMouseOverAxisIndex:Mv,setMouseClickAxisIndex:Tv,setSyncInteraction:Dv,setKeyboardInteraction:Cv}=wv.actions,Iv=wv.reducer;function Nv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Nv(Object(r),!0).forEach((function(t){Lv(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nv(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Lv(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Rv=(e,t,r,n)=>{if(null==t)return xv;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return xv;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return _v(_v({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return _v(_v({},xv),{},{coordinate:i.coordinate})},Kv=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!Ho(n))return r;var i=1/0;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},zv=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],s=null==c?void 0:l(c.positions,a);if(null!=s)return s;var u=null==i?void 0:i[Number(a)];if(u)return"horizontal"===r?{x:u.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:u.coordinate}}},Bv=(e,t,r,n)=>{return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter((e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===i}));var i},Fv=e=>e.options.tooltipPayloadSearcher,Wv=e=>e.tooltip;function Uv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Xv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Uv(Object(r),!0).forEach((function(t){Vv(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uv(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Vv(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var $v=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:c,dataStartIndex:s,dataEndIndex:u}=r;return e.reduce(((e,r)=>{var f,d,p,{dataDefinedOnItem:h,settings:y}=r,v=function(e,t){return null!=e?e:t}(h,l),m=Array.isArray(v)?ii(v,s,u):v,g=null!==(f=null==y?void 0:y.dataKey)&&void 0!==f?f:null==n?void 0:n.dataKey,b=null==y?void 0:y.nameKey;(d=null!=n&&n.dataKey&&Array.isArray(m)&&!Array.isArray(m[0])&&"axis"===o?x(m,n.dataKey,i):a(m,t,c,b),Array.isArray(d))?d.forEach((t=>{var r=Xv(Xv({},y),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(Ei({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:ci(t.payload,t.dataKey),name:t.name}))})):e.push(Ei({tooltipEntrySettings:y,dataKey:g,payload:d,value:ci(d,g),name:null!==(p=ci(d,b))&&void 0!==p?p:null==y?void 0:y.name}));return e}),[])}},Hv=et([mh,Ji,Ch,Xp,yh],My),qv=et([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],((e,t)=>[...e,...t])),Yv=et([yh,vh],Ih),Gv=et([qv,mh,Yv],Lh),Zv=et([Gv],(e=>e.filter(bh))),Jv=et([Gv],Fh),Qv=et([Jv,hp],Uh),em=et([Zv,hp,mh],gh),tm=et([Qv,mh,Gv],Vh),rm=et([mh],ly),nm=et([Gv],(e=>e.filter(bh))),im=et([em,nm,Up],Zh),am=et([im,hp,yh],Qh),om=et([Gv],zh),lm=et([Qv,mh,om,ry,yh],ty),cm=et([sy,yh,vh],uy),sm=et([cm,yh],vy),um=et([dy,yh,vh],uy),fm=et([um,yh],gy),dm=et([hy,yh,vh],uy),pm=et([dm,yh],xy),hm=et([sm,pm,fm],cy),ym=et([mh,rm,am,lm,hm,Ji,yh],Ey),vm=et([mh,Ji,Qv,tm,Up,yh,ym],Sy),mm=et([vm,mh,Hv],Cy),gm=et([mh,vm,mm,yh],Ny),bm=e=>{var t=yh(e),r=vh(e);return Wy(e,t,r,!1)},xm=et([mh,bm],Gp),wm=et([mh,Hv,gm,xm],Dy),Om=et([Ji,tm,mh,yh],rv),Pm=et([Ji,tm,mh,yh],iv),Em=et([Ji,mh,Hv,wm,bm,Om,Pm,yh],((e,t,r,n,i,a,o,l)=>{if(t){var{type:c}=t,u=si(e,l);if(n){var f="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,d="category"===c&&n.bandwidth?n.bandwidth()/f:0;return d="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*s(i[0]-i[1])*d:d,u&&o?o.map(((e,t)=>({coordinate:n(e)+d,value:e,index:t,offset:d}))):n.domain().map(((e,t)=>({coordinate:n(e)+d,value:a?a[e]:e,index:t,offset:d})))}}})),Am=et([yv,vv,e=>e.tooltip.settings],((e,t,r)=>mv(r.shared,e,t))),jm=e=>e.tooltip.settings.trigger,Sm=e=>e.tooltip.settings.defaultIndex,km=et([Wv,Am,jm,Sm],Rv),Mm=et([km,Qv],Kv),Tm=et([Em,Mm],bv),Dm=et([km],(e=>{if(e)return e.dataKey})),Cm=et([Wv,Am,jm,Sm],Bv),Im=et([ji,Si,Ji,Ki,Em,Sm,Cm,Fv],zv),Nm=et([km,Im],((e,t)=>null!=e&&e.coordinate?e.coordinate:t)),_m=et([km],(e=>e.active)),Lm=et([Cm,Mm,hp,mh,Tm,Fv,Am],$v),Rm=et([Lm],(e=>{if(null!=e){var t=e.map((e=>e.payload)).filter((e=>null!=e));return Array.from(new Set(t))}}));function Km(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function zm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Km(Object(r),!0).forEach((function(t){Bm(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Km(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Bm(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Fm=()=>{var e=He(mh),t=He(Em),r=He(wm);return Pi(zm(zm({},e),{},{scale:r}),t)},Wm=()=>He(Xp),Um=(e,t)=>t,Xm=(e,t,r)=>r,Vm=(e,t,r,n)=>n,$m=et(Em,(e=>nt()(e,(e=>e.coordinate)))),Hm=et([Wv,Um,Xm,Vm],Rv),qm=et([Hm,Qv],Kv),Ym=(e,t,r)=>{if(null!=t){var n=Wv(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},Gm=et([Wv,Um,Xm,Vm],Bv),Zm=et([ji,Si,Ji,Ki,Em,Vm,Gm,Fv],zv),Jm=et([Hm,Zm],((e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t})),Qm=et(Em,qm,bv),eg=et([Gm,qm,hp,mh,Qm,Fv,Um],$v),tg=et([Hm],(e=>({isActive:e.active,activeIndex:e.index})));function rg(){return rg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rg.apply(null,arguments)}function ng(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ig(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ng(Object(r),!0).forEach((function(t){ag(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ng(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ag(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function og(e){var r,i,{coordinate:a,payload:o,index:l,offset:c,tooltipAxisBandSize:s,layout:u,cursor:f,tooltipEventType:d,chartName:p}=e,h=a,y=o,v=l;if(!f||!h||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=h,i=ul;else if("BarChart"===p)r=function(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-i:r.left+.5,y:"horizontal"===e?r.top+.5:t.y-i,width:"horizontal"===e?n:r.width-1,height:"horizontal"===e?r.height-1:n}}(u,h,c,s),i=Yl;else if("radial"===u){var{cx:m,cy:g,radius:b,startAngle:x,endAngle:w}=Gl(h);r={cx:m,cy:g,startAngle:x,endAngle:w,innerRadius:b,outerRadius:b},i=tc}else r={points:rc(u,h,c)},i=il;var O="object"==typeof f&&"className"in f?f.className:void 0,P=ig(ig(ig(ig({stroke:"#ccc",pointerEvents:"none"},c),r),z(f,!1)),{},{payload:y,payloadIndex:v,className:n("recharts-tooltip-cursor",O)});return(0,t.isValidElement)(f)?(0,t.cloneElement)(f,P):(0,t.createElement)(i,P)}function lg(e){var r=Fm(),n=qi(),i=Qi(),a=Wm();return t.createElement(og,rg({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:n,layout:i,tooltipAxisBandSize:r,chartName:a}))}var cg=(0,t.createContext)(null),sg=()=>(0,t.useContext)(cg);var ug=new(a(228)),fg="recharts.syncEvent.tooltip",dg="recharts.syncEvent.brush";function pg(e,t){if(t){var r=Number.parseInt(t,10);if(!u(r))return null==e?void 0:e[r]}}var hg=Gr({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),yg=hg.reducer,{createEventEmitter:vg}=hg.actions;function mg(e){return e.tooltip.syncInteraction}var gg=Gr({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload)return e.dataStartIndex=0,void(e.dataEndIndex=0);t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:bg,setDataStartEndIndexes:xg,setComputedData:wg}=gg.actions,Og=gg.reducer,Pg=()=>{};function Eg(){var e=Ue();(0,t.useEffect)((()=>{e(vg())}),[e]),function(){var e=He(Vp),r=He(Hp),n=Ue(),i=He($p),a=He(Em),o=Qi(),l=$i(),c=He((e=>e.rootProps.className));(0,t.useEffect)((()=>{if(null==e)return Pg;var t=(t,c,s)=>{if(r!==s&&e===t)if("index"!==i){if(null!=a){var u;if("function"==typeof i){var f={activeTooltipIndex:null==c.payload.index?void 0:Number(c.payload.index),isTooltipActive:c.payload.active,activeIndex:null==c.payload.index?void 0:Number(c.payload.index),activeLabel:c.payload.label,activeDataKey:c.payload.dataKey,activeCoordinate:c.payload.coordinate},d=i(a,f);u=a[d]}else"value"===i&&(u=a.find((e=>String(e.value)===c.payload.label)));var{coordinate:p}=c.payload;if(null!=u&&!1!==c.payload.active&&null!=p&&null!=l){var{x:h,y}=p,v=Math.min(h,l.x+l.width),m=Math.min(y,l.y+l.height),g={x:"horizontal"===o?u.coordinate:v,y:"horizontal"===o?m:u.coordinate},b=Dv({active:c.payload.active,coordinate:g,dataKey:c.payload.dataKey,index:String(u.index),label:c.payload.label});n(b)}else n(Dv({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}))}}else n(c)};return ug.on(fg,t),()=>{ug.off(fg,t)}}),[c,n,r,e,i,a,o,l])}(),function(){var e=He(Vp),r=He(Hp),n=Ue();(0,t.useEffect)((()=>{if(null==e)return Pg;var t=(t,i,a)=>{r!==a&&e===t&&n(xg(i))};return ug.on(dg,t),()=>{ug.off(dg,t)}}),[n,r,e])}()}function Ag(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ag(Object(r),!0).forEach((function(t){Sg(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ag(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Sg(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kg(e){return e.dataKey}var Mg=[],Tg={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!Oo.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function Dg(e){var r=pl(e,Tg),{active:n,allowEscapeViewBox:i,animationDuration:a,animationEasing:o,content:l,filterNull:c,isAnimationActive:s,offset:u,payloadUniqBy:f,position:d,reverseDirection:p,useTranslate3d:h,wrapperStyle:y,cursor:v,shared:m,trigger:g,defaultIndex:b,portal:x,axisId:w}=r,O=Ue(),P="number"==typeof b?String(b):b;(0,t.useEffect)((()=>{O(Ev({shared:m,trigger:g,axisId:w,active:n,defaultIndex:P}))}),[O,m,g,w,n,P]);var E=$i(),A=Po(),j=function(e){return He((t=>gv(t,e)))}(m),{activeIndex:S,isActive:k}=He((e=>tg(e,j,g,P))),M=He((e=>eg(e,j,g,P))),T=He((e=>Qm(e,j,g,P))),D=He((e=>Jm(e,j,g,P))),C=M,I=sg(),N=null!=n?n:k,[_,L]=lt([C,N]),R="axis"===j?T:void 0;!function(e,r,n,i,a,o){var l=He((t=>Ym(t,e,r))),c=He(Hp),s=He(Vp),u=He($p),f=He(mg),d=null==f?void 0:f.active;(0,t.useEffect)((()=>{if(!d&&null!=s&&null!=c){var e=Dv({active:o,coordinate:n,dataKey:l,index:a,label:"number"==typeof i?String(i):i});ug.emit(fg,s,e,c)}}),[d,n,l,a,i,c,s,u,o])}(j,g,D,R,S,N);var K=null!=x?x:I;if(null==K)return null;var z=null!=C?C:Mg;N||(z=Mg),c&&z.length&&(z=ze(C.filter((e=>null!=e.value&&(!0!==e.hide||r.includeHidden))),f,kg));var B=z.length>0,F=t.createElement(wo,{allowEscapeViewBox:i,animationDuration:a,animationEasing:o,isAnimationActive:s,active:N,coordinate:D,hasPayload:B,offset:u,position:d,reverseDirection:p,useTranslate3d:h,viewBox:E,wrapperStyle:y,lastBoundingBox:_,innerRef:L,hasPortalFromProps:Boolean(x)},function(e,r){return t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?t.createElement(e,r):t.createElement(po,r)}(l,jg(jg({},r),{},{payload:z,label:R,active:N,coordinate:D,accessibilityLayer:A})));return t.createElement(t.Fragment,null,(0,$.createPortal)(F,K),N&&t.createElement(lg,{cursor:v,tooltipEventType:j,coordinate:D,payload:C,index:S}))}var Cg=a(4297),Ig=a.n(Cg),Ng=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function _g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Lg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_g(Object(r),!0).forEach((function(t){Rg(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_g(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Rg(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Kg=(0,t.forwardRef)(((e,r)=>{var{aspect:i,initialDimension:a={width:-1,height:-1},width:o="100%",height:l="100%",minWidth:c=0,minHeight:s,maxHeight:u,children:d,debounce:p=0,id:h,className:y,onResize:v,style:m={}}=e,g=(0,t.useRef)(null),b=(0,t.useRef)();b.current=v,(0,t.useImperativeHandle)(r,(()=>g.current));var[x,w]=(0,t.useState)({containerWidth:a.width,containerHeight:a.height}),O=(0,t.useCallback)(((e,t)=>{w((r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}}))}),[]);(0,t.useEffect)((()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;O(r,n),null===(t=b.current)||void 0===t||t.call(b,r,n)};p>0&&(e=Ig()(e,p,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=g.current.getBoundingClientRect();return O(r,n),t.observe(g.current),()=>{t.disconnect()}}),[O,p]);var P=(0,t.useMemo)((()=>{var{containerWidth:e,containerHeight:r}=x;if(e<0||r<0)return null;Ng(f(o)||f(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",o,l),Ng(!i||i>0,"The aspect(%s) must be greater than zero.",i);var n=f(o)?e:o,a=f(l)?r:l;return i&&i>0&&(n?a=n/i:a&&(n=a*i),u&&a>u&&(a=u)),Ng(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,o,l,c,s,i),t.Children.map(d,(e=>(0,t.cloneElement)(e,{width:n,height:a,style:Lg({width:n,height:a},e.props.style)})))}),[i,d,l,u,s,c,x,o]);return t.createElement("div",{id:h?"".concat(h):void 0,className:n("recharts-responsive-container",y),style:Lg(Lg({},m),{},{width:o,height:l,minWidth:c,minHeight:s,maxHeight:u}),ref:g},t.createElement("div",{style:{width:0,height:0,overflow:"visible"}},P))})),zg=e=>null;function Bg(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}zg.displayName="Cell";function Fg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Wg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fg(Object(r),!0).forEach((function(t){Ug(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fg(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ug(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Xg=Wg({},{cacheSize:2e3,enableCache:!0}),Vg=new class{constructor(e){Bg(this,"cache",new Map),this.maxSize=e}get(e){var t=this.cache.get(e);return void 0!==t&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){if(this.cache.has(e))this.cache.delete(e);else if(this.cache.size>=this.maxSize){var r=this.cache.keys().next().value;this.cache.delete(r)}this.cache.set(e,t)}clear(){this.cache.clear()}size(){return this.cache.size}}(Xg.cacheSize),$g={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Hg="recharts_measurement_span";var qg=(e,t)=>{try{var r=document.getElementById(Hg);r||((r=document.createElement("span")).setAttribute("id",Hg),r.setAttribute("aria-hidden","true"),document.body.appendChild(r)),Object.assign(r.style,$g,t),r.textContent="".concat(e);var n=r.getBoundingClientRect();return{width:n.width,height:n.height}}catch(e){return{width:0,height:0}}},Yg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||Oo.isSsr)return{width:0,height:0};if(!Xg.enableCache)return qg(e,t);var r=function(e,t){var r=t.fontSize||"",n=t.fontFamily||"",i=t.fontWeight||"",a=t.fontStyle||"",o=t.letterSpacing||"",l=t.textTransform||"";return"".concat(e,"|").concat(r,"|").concat(n,"|").concat(i,"|").concat(a,"|").concat(o,"|").concat(l)}(e,t),n=Vg.get(r);if(n)return n;var i=qg(e,t);return Vg.set(r,i),i},Gg=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Zg=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Jg=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Qg=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,eb={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},tb=Object.keys(eb),rb="NaN";class nb{static parse(e){var t,[,r,n]=null!==(t=Qg.exec(e))&&void 0!==t?t:[];return new nb(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,u(e)&&(this.unit=""),""===t||Jg.test(t)||(this.num=NaN,this.unit=""),tb.includes(t)&&(this.num=function(e,t){return e*eb[t]}(e,t),this.unit="px")}add(e){return this.unit!==e.unit?new nb(NaN,""):new nb(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new nb(NaN,""):new nb(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new nb(NaN,""):new nb(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new nb(NaN,""):new nb(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return u(this.num)}}function ib(e){if(e.includes(rb))return rb;for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!==(r=Gg.exec(t))&&void 0!==r?r:[],o=nb.parse(null!=n?n:""),l=nb.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return rb;t=t.replace(Gg,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,[,u,f,d]=null!==(s=Zg.exec(t))&&void 0!==s?s:[],p=nb.parse(null!=u?u:""),h=nb.parse(null!=d?d:""),y="+"===f?p.add(h):p.subtract(h);if(y.isNaN())return rb;t=t.replace(Zg,y.toString())}return t}var ab=/\(([^()]*)\)/;function ob(e){var t=e.replace(/\s+/g,"");return t=function(e){for(var t,r=e;null!=(t=ab.exec(r));){var[,n]=t;r=r.replace(ab,ib(n))}return r}(t),t=ib(t)}function lb(e){var t=function(e){try{return ob(e)}catch(e){return rb}}(e.slice(5,-1));return t===rb?"":t}var cb=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],sb=["dx","dy","angle","className","breakAll"];function ub(){return ub=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ub.apply(null,arguments)}function fb(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var db=/[ \f\n\r\t\v\u2028\u2029]+/,pb=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];return O(t)||(i=r?t.toString().split(""):t.toString().split(db)),{wordsWithComputedWidth:i.map((e=>({word:e,width:Yg(e,n).width}))),spaceWidth:r?0:Yg(" ",n).width}}catch(e){return null}},hb=e=>[{words:O(e)?[]:e.toString().split(db)}],yb=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!Oo.isSsr){var l=pb({breakAll:a,children:n,style:i});if(!l)return hb(n);var{wordsWithComputedWidth:c,spaceWidth:s}=l;return((e,t,r,n,i)=>{var{maxLines:a,children:o,style:l,breakAll:c}=e,s=d(a),u=o,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];if(l&&(null==n||i||l.width+o+r<Number(n)))l.words.push(a),l.width+=o+r;else{var c={words:[a],width:o};e.push(c)}return e}),[])},p=f(t),h=e=>e.reduce(((e,t)=>e.width>t.width?e:t));if(!s||i)return p;if(!(p.length>a||h(p).width>Number(n)))return p;for(var y,v=e=>{var t=u.slice(0,e),r=pb({breakAll:c,style:l,children:t+"…"}).wordsWithComputedWidth,i=f(r);return[i.length>a||h(i).width>Number(n),i]},m=0,g=u.length-1,b=0;m<=g&&b<=u.length-1;){var x=Math.floor((m+g)/2),w=x-1,[O,P]=v(w),[E]=v(x);if(O||E||(m=x+1),O&&E&&(g=x-1),!O&&E){y=P;break}b++}return y||p})({breakAll:a,children:n,maxLines:o,style:i},c,s,t,r)}return hb(n)},vb="#808080",mb=(0,t.forwardRef)(((e,r)=>{var{x:i=0,y:a=0,lineHeight:o="1em",capHeight:l="0.71em",scaleToFit:c=!1,textAnchor:s="start",verticalAnchor:u="end",fill:f=vb}=e,h=fb(e,cb),y=(0,t.useMemo)((()=>yb({breakAll:h.breakAll,children:h.children,maxLines:h.maxLines,scaleToFit:c,style:h.style,width:h.width})),[h.breakAll,h.children,h.maxLines,c,h.style,h.width]),{dx:v,dy:m,angle:g,className:b,breakAll:x}=h,w=fb(h,sb);if(!p(i)||!p(a))return null;var O,P=i+(d(v)?v:0),E=a+(d(m)?m:0);switch(u){case"start":O=lb("calc(".concat(l,")"));break;case"middle":O=lb("calc(".concat((y.length-1)/2," * -").concat(o," + (").concat(l," / 2))"));break;default:O=lb("calc(".concat(y.length-1," * -").concat(o,")"))}var A=[];if(c){var j=y[0].width,{width:S}=h;A.push("scale(".concat(d(S)?S/j:1,")"))}return g&&A.push("rotate(".concat(g,", ").concat(P,", ").concat(E,")")),A.length&&(w.transform=A.join(" ")),t.createElement("text",ub({},z(w,!0),{ref:r,x:P,y:E,className:n("recharts-text",b),textAnchor:s,fill:f.includes("url")?vb:f}),y.map(((e,r)=>{var n=e.words.join(x?"":" ");return t.createElement("tspan",{x:P,dy:0===r?O:o,key:"".concat(n,"-").concat(r)},n)})))}));mb.displayName="Text";var gb=["offset"],bb=["labelRef"];function xb(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function wb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ob(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wb(Object(r),!0).forEach((function(t){Pb(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wb(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Pb(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Eb(){return Eb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Eb.apply(null,arguments)}var Ab=e=>{var{value:t,formatter:r}=e,n=O(e.children)?t:e.children;return"function"==typeof r?r(n):n},jb=e=>null!=e&&"function"==typeof e,Sb=(e,r,i,a)=>{var o,l,{position:c,offset:u,className:f}=e,{cx:d,cy:p,innerRadius:h,outerRadius:v,startAngle:m,endAngle:g,clockWise:b}=a,x=(h+v)/2,w=((e,t)=>s(t-e)*Math.min(Math.abs(t-e),360))(m,g),P=w>=0?1:-1;"insideStart"===c?(o=m+P*u,l=b):"insideEnd"===c?(o=g-P*u,l=!b):"end"===c&&(o=g+P*u,l=b),l=w<=0?l:!l;var E=Jn(d,p,x,o),A=Jn(d,p,x,o+359*(l?1:-1)),j="M".concat(E.x,",").concat(E.y,"\n    A").concat(x,",").concat(x,",0,1,").concat(l?0:1,",\n    ").concat(A.x,",").concat(A.y),S=O(e.id)?y("recharts-radial-line-"):e.id;return t.createElement("text",Eb({},i,{dominantBaseline:"central",className:n("recharts-radial-bar-label",f)}),t.createElement("defs",null,t.createElement("path",{id:S,d:j})),t.createElement("textPath",{xlinkHref:"#".concat(S)},r))},kb=(e,t,r)=>{var{cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:l,endAngle:c}=e,s=(l+c)/2;if("outside"===r){var{x:u,y:f}=Jn(n,i,o+t,s);return{x:u,y:f,textAnchor:u>=n?"start":"end",verticalAnchor:"middle"}}if("center"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"end"};var d=(a+o)/2,{x:p,y:h}=Jn(n,i,d,s);return{x:p,y:h,textAnchor:"middle",verticalAnchor:"middle"}},Mb=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:c}=t,s=c>=0?1:-1,u=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=l>=0?1:-1,m=y*n,g=y>0?"end":"start",b=y>0?"start":"end";if("top"===i)return Ob(Ob({},{x:a+l/2,y:o-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return Ob(Ob({},{x:a+l/2,y:o+c+u,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===i){var x={x:a-m,y:o+c/2,textAnchor:g,verticalAnchor:"middle"};return Ob(Ob({},x),r?{width:Math.max(x.x-r.x,0),height:c}:{})}if("right"===i){var w={x:a+l+m,y:o+c/2,textAnchor:b,verticalAnchor:"middle"};return Ob(Ob({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:c}:{})}var O=r?{width:l,height:c}:{};return"insideLeft"===i?Ob({x:a+m,y:o+c/2,textAnchor:b,verticalAnchor:"middle"},O):"insideRight"===i?Ob({x:a+l-m,y:o+c/2,textAnchor:g,verticalAnchor:"middle"},O):"insideTop"===i?Ob({x:a+l/2,y:o+u,textAnchor:"middle",verticalAnchor:h},O):"insideBottom"===i?Ob({x:a+l/2,y:o+c-u,textAnchor:"middle",verticalAnchor:p},O):"insideTopLeft"===i?Ob({x:a+m,y:o+u,textAnchor:b,verticalAnchor:h},O):"insideTopRight"===i?Ob({x:a+l-m,y:o+u,textAnchor:g,verticalAnchor:h},O):"insideBottomLeft"===i?Ob({x:a+m,y:o+c-u,textAnchor:b,verticalAnchor:p},O):"insideBottomRight"===i?Ob({x:a+l-m,y:o+c-u,textAnchor:g,verticalAnchor:p},O):i&&"object"==typeof i&&(d(i.x)||f(i.x))&&(d(i.y)||f(i.y))?Ob({x:a+v(i.x,l),y:o+v(i.y,c),textAnchor:"end",verticalAnchor:"end"},O):Ob({x:a+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},O)},Tb=e=>"cx"in e&&d(e.cx);function Db(e){var{offset:r=5}=e,i=Ob({offset:r},xb(e,gb)),{viewBox:a,position:o,value:l,children:c,content:s,className:u="",textBreakAll:f,labelRef:d}=i,p=He(fh),h=$i(),y=a||("center"===o?h:null!=p?p:h);if(!y||O(l)&&O(c)&&!(0,t.isValidElement)(s)&&"function"!=typeof s)return null;var v,m=Ob(Ob({},i),{},{viewBox:y});if((0,t.isValidElement)(s)){var{labelRef:g}=m,b=xb(m,bb);return(0,t.cloneElement)(s,b)}if("function"==typeof s){if(v=(0,t.createElement)(s,m),(0,t.isValidElement)(v))return v}else v=Ab(i);var x=Tb(y),w=z(i,!0);if(x&&("insideStart"===o||"insideEnd"===o||"end"===o))return Sb(i,v,w,y);var P=x?kb(y,i.offset,i.position):Mb(i,y);return t.createElement(mb,Eb({ref:d,className:n("recharts-label",u)},w,P,{breakAll:f}),v)}Db.displayName="Label";var Cb=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:c,outerRadius:s,x:u,y:f,top:p,left:h,width:y,height:v,clockWise:m,labelViewBox:g}=e;if(g)return g;if(d(y)&&d(v)){if(d(u)&&d(f))return{x:u,y:f,width:y,height:v};if(d(p)&&d(h))return{x:p,y:h,width:y,height:v}}return d(u)&&d(f)?{x:u,y:f,width:0,height:0}:d(t)&&d(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||l||o||0,clockWise:m}:e.viewBox?e.viewBox:void 0};Db.parseViewBox=Cb,Db.renderCallByParent=function(e,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i,labelRef:a}=e,o=Cb(e),l=R(i,Db).map(((e,n)=>(0,t.cloneElement)(e,{viewBox:r||o,key:"label-".concat(n)})));if(!n)return l;var c=((e,r,n)=>{if(!e)return null;var i={viewBox:r,labelRef:n};return!0===e?t.createElement(Db,Eb({key:"label-implicit"},i)):p(e)?t.createElement(Db,Eb({key:"label-implicit",value:e},i)):(0,t.isValidElement)(e)?e.type===Db?(0,t.cloneElement)(e,Ob({key:"label-implicit"},i)):t.createElement(Db,Eb({key:"label-implicit",content:e},i)):jb(e)?t.createElement(Db,Eb({key:"label-implicit",content:e},i)):e&&"object"==typeof e?t.createElement(Db,Eb({},e,{key:"label-implicit"},i)):null})(e.label,r||o,a);return[c,...l]};var Ib=a(25),Nb=a.n(Ib),_b=["valueAccessor"],Lb=["data","dataKey","clockWise","id","textBreakAll"];function Rb(){return Rb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rb.apply(null,arguments)}function Kb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function zb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Kb(Object(r),!0).forEach((function(t){Bb(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kb(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Bb(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fb(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Wb=e=>Array.isArray(e.value)?Nb()(e.value):e.value;function Ub(e){var{valueAccessor:r=Wb}=e,n=Fb(e,_b),{data:i,dataKey:a,clockWise:o,id:l,textBreakAll:c}=n,s=Fb(n,Lb);return i&&i.length?t.createElement(V,{className:"recharts-label-list"},i.map(((e,n)=>{var i=O(a)?r(e,n):ci(e&&e.payload,a),u=O(l)?{}:{id:"".concat(l,"-").concat(n)};return t.createElement(Db,Rb({},z(e,!0),s,u,{parentViewBox:e.parentViewBox,value:i,textBreakAll:c,viewBox:Db.parseViewBox(O(o)?e:zb(zb({},e),{},{clockWise:o})),key:"label-".concat(n),index:n}))}))):null}Ub.displayName="LabelList",Ub.renderCallByParent=function(e,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i}=e,a=R(i,Ub).map(((e,n)=>(0,t.cloneElement)(e,{data:r,key:"labelList-".concat(n)})));return n?[function(e,r){return e?!0===e?t.createElement(Ub,{key:"labelList-implicit",data:r}):t.isValidElement(e)||jb(e)?t.createElement(Ub,{key:"labelList-implicit",data:r,content:e}):"object"==typeof e?t.createElement(Ub,Rb({data:r},e,{key:"labelList-implicit"})):null:null}(e.label,r),...a]:a};var Xb=["component"];function Vb(e){var r,{component:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Xb);return(0,t.isValidElement)(n)?r=(0,t.cloneElement)(n,i):"function"==typeof n?r=(0,t.createElement)(n,i):Ng(!1,"Customized's props `component` must be React.element or Function, but got %s.",typeof n),t.createElement(V,{className:"recharts-customized-wrapper"},r)}Vb.displayName="Customized";var $b=["points","className","baseLinePoints","connectNulls"];function Hb(){return Hb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hb.apply(null,arguments)}var qb=e=>e&&e.x===+e.x&&e.y===+e.y,Yb=(e,t)=>{var r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach((e=>{qb(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])})),qb(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t}(e);t&&(r=[r.reduce(((e,t)=>[...e,...t]),[])]);var n=r.map((e=>e.reduce(((e,t,r)=>"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)),""))).join("");return 1===r.length?"".concat(n,"Z"):n},Gb=e=>{var{points:r,className:i,baseLinePoints:a,connectNulls:o}=e,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,$b);if(!r||!r.length)return null;var c=n("recharts-polygon",i);if(a&&a.length){var s=l.stroke&&"none"!==l.stroke,u=((e,t,r)=>{var n=Yb(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(Yb(t.reverse(),r).slice(1))})(r,a,o);return t.createElement("g",{className:c},t.createElement("path",Hb({},z(l,!0),{fill:"Z"===u.slice(-1)?l.fill:"none",stroke:"none",d:u})),s?t.createElement("path",Hb({},z(l,!0),{fill:"none",d:Yb(r,o)})):null,s?t.createElement("path",Hb({},z(l,!0),{fill:"none",d:Yb(a,o)})):null)}var f=Yb(r,o);return t.createElement("path",Hb({},z(l,!0),{fill:"Z"===f.slice(-1)?l.fill:"none",className:c,d:f}))};function Zb(){return Zb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zb.apply(null,arguments)}var Jb=e=>{var{cx:r,cy:i,r:a,className:o}=e,l=n("recharts-dot",o);return r===+r&&i===+i&&a===+a?t.createElement("circle",Zb({},C(e),k(e),{className:l,cx:r,cy:i,r:a})):null},Qb=e=>e.graphicalItems.polarItems,ex=et([dh,ph],Ih),tx=et([Qb,Th,ex],Lh),rx=et([tx],Fh),nx=et([rx,yp],Uh),ix=et([nx,Th,tx],Vh),ax=et([nx,Th,tx],((e,t,r)=>r.length>0?e.flatMap((e=>r.flatMap((r=>{var n;return{value:ci(e,null!==(n=t.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})))).filter(Boolean):null!=(null==t?void 0:t.dataKey)?e.map((e=>({value:ci(e,t.dataKey),errorDomain:[]}))):e.map((e=>({value:e,errorDomain:[]}))))),ox=()=>{},lx=et([Th,Py,ox,ax,ox,Ji,dh],Ey),cx=et([Th,Ji,nx,ix,Up,dh,lx],Sy),sx=et([cx,Th,Ty],Cy),ux=et([Th,cx,sx,dh],Ny),fx=(e,t,r)=>{switch(t){case"angleAxis":return th(e,r);case"radiusAxis":return rh(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},dx=(e,t,r)=>{switch(t){case"angleAxis":return ch(e,r);case"radiusAxis":return uh(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},px=et([fx,Ty,ux,dx],Dy),hx=et([Ji,ix,Dh,dh],iv),yx=et([Ji,fx,Ty,px,sx,dx,nv,hx,dh],lv),vx=et([Ji,fx,px,dx,nv,hx,dh],sv),mx=et([(e,t)=>yx(e,"angleAxis",t,!1)],(e=>{if(e)return e.map((e=>e.coordinate))})),gx=et([(e,t)=>yx(e,"radiusAxis",t,!1)],(e=>{if(e)return e.map((e=>e.coordinate))})),bx=["gridType","radialLines","angleAxisId","radiusAxisId","cx","cy","innerRadius","outerRadius"];function xx(){return xx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xx.apply(null,arguments)}function wx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ox(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wx(Object(r),!0).forEach((function(t){Px(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wx(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Px(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ex=(e,t,r,n)=>{var i="";return n.forEach(((n,a)=>{var o=Jn(t,r,e,n);i+=a?"L ".concat(o.x,",").concat(o.y):"M ".concat(o.x,",").concat(o.y)})),i+="Z"},Ax=e=>{var{cx:r,cy:n,innerRadius:i,outerRadius:a,polarAngles:o,radialLines:l}=e;if(!o||!o.length||!l)return null;var c=Ox({stroke:"#ccc"},C(e));return t.createElement("g",{className:"recharts-polar-grid-angle"},o.map((e=>{var o=Jn(r,n,i,e),l=Jn(r,n,a,e);return t.createElement("line",xx({},c,{key:"line-".concat(e),x1:o.x,y1:o.y,x2:l.x,y2:l.y}))})))},jx=e=>{var{cx:r,cy:i,radius:a,index:o}=e,l=Ox(Ox({stroke:"#ccc"},C(e)),{},{fill:"none"});return t.createElement("circle",xx({},l,{className:n("recharts-polar-grid-concentric-circle",e.className),key:"circle-".concat(o),cx:r,cy:i,r:a}))},Sx=e=>{var{radius:r,index:i}=e,a=Ox(Ox({stroke:"#ccc"},C(e)),{},{fill:"none"});return t.createElement("path",xx({},a,{className:n("recharts-polar-grid-concentric-polygon",e.className),key:"path-".concat(i),d:Ex(r,e.cx,e.cy,e.polarAngles)}))},kx=e=>{var{polarRadius:r,gridType:n}=e;return r&&r.length?t.createElement("g",{className:"recharts-polar-grid-concentric"},r.map(((r,i)=>{var a=i;return"circle"===n?t.createElement(jx,xx({key:a},e,{radius:r,index:i})):t.createElement(Sx,xx({key:a},e,{radius:r,index:i}))}))):null},Mx=e=>{var r,n,i,a,o,l,c,s,{gridType:u="polygon",radialLines:f=!0,angleAxisId:d=0,radiusAxisId:p=0,cx:h,cy:y,innerRadius:v,outerRadius:m}=e,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,bx),b=He(fh),x=Ox({cx:null!==(r=null!==(n=null==b?void 0:b.cx)&&void 0!==n?n:h)&&void 0!==r?r:0,cy:null!==(i=null!==(a=null==b?void 0:b.cy)&&void 0!==a?a:y)&&void 0!==i?i:0,innerRadius:null!==(o=null!==(l=null==b?void 0:b.innerRadius)&&void 0!==l?l:v)&&void 0!==o?o:0,outerRadius:null!==(c=null!==(s=null==b?void 0:b.outerRadius)&&void 0!==s?s:m)&&void 0!==c?c:0},g),{polarAngles:w,polarRadius:O,cx:P,cy:E,innerRadius:A,outerRadius:j}=x,S=He((e=>mx(e,d))),k=He((e=>gx(e,p))),M=Array.isArray(w)?w:S,T=Array.isArray(O)?O:k;return j<=0||null==M||null==T?null:t.createElement("g",{className:"recharts-polar-grid"},t.createElement(Ax,xx({cx:P,cy:E,innerRadius:A,outerRadius:j,gridType:u,radialLines:f},x,{polarAngles:M,polarRadius:T})),t.createElement(kx,xx({cx:P,cy:E,innerRadius:A,outerRadius:j,gridType:u,radialLines:f},x,{polarAngles:M,polarRadius:T})))};Mx.displayName="PolarGrid";var Tx=a(4338),Dx=a.n(Tx),Cx=a(2972),Ix=a.n(Cx),Nx=Gr({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:_x,removeRadiusAxis:Lx,addAngleAxis:Rx,removeAngleAxis:Kx}=Nx.actions,zx=Nx.reducer,Bx=["cx","cy","angle","axisLine"],Fx=["angle","tickFormatter","stroke","tick"];function Wx(){return Wx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wx.apply(null,arguments)}function Ux(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Xx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ux(Object(r),!0).forEach((function(t){Vx(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ux(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Vx(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $x(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Hx="radiusAxis";function qx(e){var r=Ue();return(0,t.useEffect)((()=>(r(_x(e)),()=>{r(Lx(e))}))),null}var Yx=(e,r)=>{var{angle:i,tickFormatter:a,stroke:o,tick:l}=e,c=$x(e,Fx),s=(e=>{var t;switch(e){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t})(e.orientation),u=C(c),f=z(l,!1),d=r.map(((r,c)=>{var d=((e,t,r,n)=>{var{coordinate:i}=e;return Jn(r,n,i,t)})(r,e.angle,e.cx,e.cy),p=Xx(Xx(Xx(Xx({textAnchor:s,transform:"rotate(".concat(90-i,", ").concat(d.x,", ").concat(d.y,")")},u),{},{stroke:"none",fill:o},f),{},{index:c},d),{},{payload:r});return t.createElement(V,Wx({className:n("recharts-polar-radius-axis-tick",ni(l)),key:"tick-".concat(r.coordinate)},M(e,r,c)),((e,r,n)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(mb,Wx({},r,{className:"recharts-polar-radius-axis-tick-value"}),n))(l,p,a?a(r.value,c):r.value))}));return t.createElement(V,{className:"recharts-polar-radius-axis-ticks"},d)},Gx=e=>{var{radiusAxisId:r}=e,i=He(fh),a=He((e=>px(e,"radiusAxis",r))),o=He((e=>yx(e,"radiusAxis",r,!1)));if(null==i||!o||!o.length)return null;var l=Xx(Xx(Xx({},e),{},{scale:a},i),{},{radius:i.outerRadius}),{tick:c,axisLine:s}=l;return t.createElement(V,{className:n("recharts-polar-radius-axis",Hx,l.className)},s&&((e,r)=>{var{cx:n,cy:i,angle:a,axisLine:o}=e,l=$x(e,Bx),c=r.reduce(((e,t)=>[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]),[1/0,-1/0]),s=Jn(n,i,c[0],a),u=Jn(n,i,c[1],a),f=Xx(Xx(Xx({},C(l)),{},{fill:"none"},z(o,!1)),{},{x1:s.x,y1:s.y,x2:u.x,y2:u.y});return t.createElement("line",Wx({className:"recharts-polar-radius-axis-line"},f))})(l,o),c&&Yx(l,o),Db.renderCallByParent(l,((e,t,r,n)=>{var i=Dx()(n,(e=>e.coordinate||0));return{cx:t,cy:r,startAngle:e,endAngle:e,innerRadius:Ix()(n,(e=>e.coordinate||0)).coordinate||0,outerRadius:i.coordinate||0}})(l.angle,l.cx,l.cy,o)))};class Zx extends t.PureComponent{render(){return t.createElement(t.Fragment,null,t.createElement(qx,{domain:this.props.domain,id:this.props.radiusAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:this.props.allowDuplicatedCategory,allowDataOverflow:this.props.allowDataOverflow,reversed:this.props.reversed,includeHidden:this.props.includeHidden,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick}),t.createElement(Gx,this.props))}}Vx(Zx,"displayName","PolarRadiusAxis"),Vx(Zx,"axisType",Hx),Vx(Zx,"defaultProps",Yp);var Jx=["children"];function Qx(){return Qx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qx.apply(null,arguments)}function ew(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function tw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ew(Object(r),!0).forEach((function(t){rw(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ew(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function rw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var nw=Math.PI/180,iw=1e-5,aw="angleAxis";function ow(e){var r=Ue(),n=(0,t.useMemo)((()=>{var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Jx);return r}),[e]),i=He((e=>th(e,n.id))),a=n===i;return(0,t.useEffect)((()=>(r(Rx(n)),()=>{r(Kx(n))})),[r,n]),a?e.children:null}var lw=e=>{var{cx:r,cy:n,radius:i,axisLineType:a,axisLine:o,ticks:l}=e;if(!o)return null;var c=tw(tw({},C(e)),{},{fill:"none"},z(o,!1));if("circle"===a)return t.createElement(Jb,Qx({className:"recharts-polar-angle-axis-line"},c,{cx:r,cy:n,r:i}));var s=l.map((e=>Jn(r,n,i,e.coordinate)));return t.createElement(Gb,Qx({className:"recharts-polar-angle-axis-line"},c,{points:s}))},cw=e=>{var{tick:r,tickProps:n,value:i}=e;return r?t.isValidElement(r)?t.cloneElement(r,n):"function"==typeof r?r(n):t.createElement(mb,Qx({},n,{className:"recharts-polar-angle-axis-tick-value"}),i):null},sw=e=>{var{tick:r,tickLine:i,tickFormatter:a,stroke:o,ticks:l}=e,c=C(e),s=z(r,!1),u=tw(tw({},c),{},{fill:"none"},z(i,!1)),f=l.map(((l,f)=>{var d=((e,t)=>{var{cx:r,cy:n,radius:i,orientation:a,tickSize:o}=t,l=o||8,c=Jn(r,n,i,e.coordinate),s=Jn(r,n,i+("inner"===a?-1:1)*l,e.coordinate);return{x1:c.x,y1:c.y,x2:s.x,y2:s.y}})(l,e),p=((e,t)=>{var r=Math.cos(-e.coordinate*nw);return r>iw?"outer"===t?"start":"end":r<-iw?"outer"===t?"end":"start":"middle"})(l,e.orientation),h=tw(tw(tw({},c),{},{textAnchor:p,stroke:"none",fill:o},s),{},{index:f,payload:l,x:d.x2,y:d.y2});return t.createElement(V,Qx({className:n("recharts-polar-angle-axis-tick",ni(r)),key:"tick-".concat(l.coordinate)},M(e,l,f)),i&&t.createElement("line",Qx({className:"recharts-polar-angle-axis-tick-line"},u,d)),t.createElement(cw,{tick:r,tickProps:h,value:a?a(l.value,f):l.value}))}));return t.createElement(V,{className:"recharts-polar-angle-axis-ticks"},f)},uw=e=>{var{angleAxisId:r}=e,i=He(fh),a=He((e=>px(e,"angleAxis",r))),o=Wi(),l=He((e=>yx(e,"angleAxis",r,o)));if(null==i||!l||!l.length)return null;var c=tw(tw(tw({},e),{},{scale:a},i),{},{radius:i.outerRadius});return t.createElement(V,{className:n("recharts-polar-angle-axis",aw,c.className)},t.createElement(lw,Qx({},c,{ticks:l})),t.createElement(sw,Qx({},c,{ticks:l})))};class fw extends t.PureComponent{render(){return this.props.radius<=0?null:t.createElement(ow,{id:this.props.angleAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:!1,allowDataOverflow:!1,reversed:this.props.reversed,includeHidden:!1,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick,domain:this.props.domain},t.createElement(uw,this.props))}}function dw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function pw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dw(Object(r),!0).forEach((function(t){hw(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function hw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}rw(fw,"displayName","PolarAngleAxis"),rw(fw,"axisType",aw),rw(fw,"defaultProps",qp);var yw=et([Qb,(e,t)=>t],((e,t)=>e.filter((e=>"pie"===e.type)).find((e=>e.id===t)))),vw=[],mw=(e,t,r)=>0===(null==r?void 0:r.length)?vw:r,gw=et([yp,yw,mw],((e,t,r)=>{var n,{chartData:i}=e;if(null!=t&&((n=null!=(null==t?void 0:t.data)&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map((e=>pw(pw({},t.presentationProps),e.props)))),null!=n))return n})),bw=et([gw,yw,mw],((e,t,r)=>{if(null!=e&&null!=t)return e.map(((e,n)=>{var i,a,o=ci(e,t.nameKey,t.name);return a=null!=r&&null!==(i=r[n])&&void 0!==i&&null!==(i=i.props)&&void 0!==i&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:Ai(o,t.dataKey),color:a,payload:e,type:t.legendType}}))})),xw=et([gw,yw,mw,Ki],((e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:c,startAngle:u,endAngle:f,dataKey:p,nameKey:h,tooltipType:y}=i,v=Math.abs(i.minAngle),m=bO(u,f),g=Math.abs(m),b=a.length<=1?0:null!==(t=i.paddingAngle)&&void 0!==t?t:0,x=a.filter((e=>0!==ci(e,p,0))).length,w=g-x*v-(g>=360?x:x-1)*b,O=a.reduce(((e,t)=>{var r=ci(t,p,0);return e+(d(r)?r:0)}),0);O>0&&(r=a.map(((e,t)=>{var r,a=ci(e,p,0),f=ci(e,h,t),g=gO(i,l,e),x=(d(a)?a:0)/O,P=dO(dO({},e),o&&o[t]&&o[t].props),E=(r=t?n.endAngle+s(m)*b*(0!==a?1:0):u)+s(m)*((0!==a?v:0)+x*w),A=(r+E)/2,j=(g.innerRadius+g.outerRadius)/2,S=[{name:f,value:a,payload:P,dataKey:p,type:y}],k=Jn(g.cx,g.cy,j,A);return n=dO(dO(dO(dO({},i.presentationProps),{},{percent:x,cornerRadius:c,name:f,tooltipPayload:S,midAngle:A,middleRadius:j,tooltipPosition:k},P),g),{},{value:ci(e,p),startAngle:r,endAngle:E,payload:P,paddingAngle:s(m)*b})})));return r}({offset:n,pieSettings:t,displayedData:e,cells:r})})),ww=a(2938),Ow=a.n(ww);function Pw(){return Pw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pw.apply(null,arguments)}var Ew=(e,t,r,n,i)=>{var a,o=r-n;return a="M ".concat(e,",").concat(t),a+="L ".concat(e+r,",").concat(t),a+="L ".concat(e+r-o/2,",").concat(t+i),a+="L ".concat(e+r-o/2-n,",").concat(t+i),a+="L ".concat(e,",").concat(t," Z")},Aw={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},jw=e=>{var r=pl(e,Aw),i=(0,t.useRef)(),[a,o]=(0,t.useState)(-1);(0,t.useEffect)((()=>{if(i.current&&i.current.getTotalLength)try{var e=i.current.getTotalLength();e&&o(e)}catch(e){}}),[]);var{x:l,y:c,upperWidth:s,lowerWidth:u,height:f,className:d}=r,{animationEasing:p,animationDuration:h,animationBegin:y,isUpdateAnimationActive:v}=r;if(l!==+l||c!==+c||s!==+s||u!==+u||f!==+f||0===s&&0===u||0===f)return null;var m=n("recharts-trapezoid",d);return v?t.createElement(Vl,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:f,x:l,y:c},to:{upperWidth:s,lowerWidth:u,height:f,x:l,y:c},duration:h,animationEasing:p,isActive:v},(e=>{var{upperWidth:n,lowerWidth:o,height:l,x:c,y:s}=e;return t.createElement(Vl,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,easing:p},t.createElement("path",Pw({},z(r,!0),{className:m,d:Ew(c,s,n,o,l),ref:i})))})):t.createElement("g",null,t.createElement("path",Pw({},z(r,!0),{className:m,d:Ew(l,c,s,u,f)})))},Sw=["option","shapeType","propTransformer","activeClassName","isActive"];function kw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Mw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?kw(Object(r),!0).forEach((function(t){Tw(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Tw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Dw(e,t){return Mw(Mw({},t),e)}function Cw(e){var{shapeType:r,elementProps:n}=e;switch(r){case"rectangle":return t.createElement(Yl,n);case"trapezoid":return t.createElement(jw,n);case"sector":return t.createElement(tc,n);case"symbols":if(function(e){return"symbols"===e}(r))return t.createElement(De,n);break;default:return null}}function Iw(e){return(0,t.isValidElement)(e)?e.props:e}function Nw(e){var r,{option:n,shapeType:i,propTransformer:a=Dw,activeClassName:o="recharts-active-shape",isActive:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Sw);if((0,t.isValidElement)(n))r=(0,t.cloneElement)(n,Mw(Mw({},c),Iw(n)));else if("function"==typeof n)r=n(c);else if(Ow()(n)&&"boolean"!=typeof n){var s=a(n,c);r=t.createElement(Cw,{shapeType:i,elementProps:s})}else{var u=c;r=t.createElement(Cw,{shapeType:i,elementProps:u})}return l?t.createElement(V,{className:o},r):r}var _w=(e,t)=>{var r=Ue();return(n,i)=>a=>{null==e||e(n,i,a),r(Av({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},Lw=e=>{var t=Ue();return(r,n)=>i=>{null==e||e(r,n,i),t(jv())}},Rw=(e,t)=>{var r=Ue();return(n,i)=>a=>{null==e||e(n,i,a),r(kv({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function Kw(e){var{fn:r,args:n}=e,i=Ue(),a=Wi();return(0,t.useEffect)((()=>{if(!a){var e=r(n);return i(Ov(e)),()=>{i(Pv(e))}}}),[r,n,i,a]),null}var zw,Bw=()=>{};function Fw(e){var{legendPayload:r}=e,n=Ue(),i=Wi();return(0,t.useEffect)((()=>i?Bw:(n(Ha(r)),()=>{n(qa(r))})),[n,i,r]),null}function Ww(e){var{legendPayload:r}=e,n=Ue(),i=He(Ji);return(0,t.useEffect)((()=>"centric"!==i&&"radial"!==i?Bw:(n(Ha(r)),()=>{n(qa(r))})),[n,i,r]),null}function Uw(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",n=(0,t.useRef)(y(r)),i=(0,t.useRef)(e);return i.current!==e&&(n.current=y(r),i.current=e),n.current}var Xw=null!==(zw=t["useId".toString()])&&void 0!==zw?zw:()=>{var[e]=t.useState((()=>y("uid-")));return e};var Vw=(0,t.createContext)(void 0),$w=e=>{var{id:r,type:n,children:i}=e,a=function(e,t){var r=Xw();return t||(e?"".concat(e,"-").concat(r):r)}("recharts-".concat(n),r);return t.createElement(Vw.Provider,{value:a},i(a))};var Hw=Gr({name:"graphicalItems",initialState:{cartesianItems:[],polarItems:[]},reducers:{addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=Ft(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=Ft(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=Ft(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addCartesianGraphicalItem:qw,replaceCartesianGraphicalItem:Yw,removeCartesianGraphicalItem:Gw,addPolarGraphicalItem:Zw,removePolarGraphicalItem:Jw}=Hw.actions,Qw=Hw.reducer;function eO(e){var r=Ue(),n=(0,t.useRef)(null);return(0,t.useEffect)((()=>{null===n.current?r(qw(e)):n.current!==e&&r(Yw({prev:n.current,next:e})),n.current=e}),[r,e]),(0,t.useEffect)((()=>()=>{n.current&&(r(Gw(n.current)),n.current=null)}),[r]),null}function tO(e){var r=Ue();return(0,t.useEffect)((()=>(r(Zw(e)),()=>{r(Jw(e))})),[r,e]),null}function rO(){}var nO={begin:0,duration:1e3,easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}},iO={t:0},aO={t:1};function oO(e){var r=pl(e,nO),{isActive:n,canBegin:i,duration:a,easing:o,begin:l,onAnimationEnd:c,onAnimationStart:s,children:u}=r,f=Kl("JavascriptAnimate",r.animationManager),[d,p]=(0,t.useState)(n?iO:aO),h=(0,t.useRef)(null);return(0,t.useEffect)((()=>{n||p(aO)}),[n]),(0,t.useEffect)((()=>{if(!n||!i)return rO;var e=Nl(iO,aO,wl(o),a,p,f.getTimeoutController());return f.start([s,l,()=>{h.current=e()},a,c]),()=>{f.stop(),h.current&&h.current(),c()}}),[n,i,a,o,l,s,c,f]),u(d.t)}var lO=["onMouseEnter","onClick","onMouseLeave"],cO=["id"],sO=["id"];function uO(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function fO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function dO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fO(Object(r),!0).forEach((function(t){pO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function pO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hO(){return hO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hO.apply(null,arguments)}function yO(e){var r=(0,t.useMemo)((()=>R(e.children,zg)),[e.children]),n=He((t=>bw(t,e.id,r)));return null==n?null:t.createElement(Ww,{legendPayload:n})}function vO(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:c,tooltipType:s}=e;return{dataDefinedOnItem:null==n?void 0:n.map((e=>e.tooltipPayload)),positions:null==n?void 0:n.map((e=>e.tooltipPosition)),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:Ai(l,t),hide:c,type:s,color:o,unit:""}}}var mO=(e,t)=>e>t?"start":e<t?"end":"middle",gO=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=Qn(a,o),c=i+v(e.cx,a,a/2),s=n+v(e.cy,o,o/2),u=v(e.innerRadius,l,0),f=((e,t,r)=>"function"==typeof t?t(e):v(t,r,.8*r))(r,e.outerRadius,l);return{cx:c,cy:s,innerRadius:u,outerRadius:f,maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},bO=(e,t)=>s(t-e)*Math.min(Math.abs(t-e),360),xO=(e,r)=>{if(t.isValidElement(e))return t.cloneElement(e,r);if("function"==typeof e)return e(r);var i=n("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return t.createElement(il,hO({},r,{type:"linear",className:i}))},wO=(e,r,i)=>{if(t.isValidElement(e))return t.cloneElement(e,r);var a=i;if("function"==typeof e&&(a=e(r),t.isValidElement(a)))return a;var o=n("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return t.createElement(mb,hO({},r,{alignmentBaseline:"middle",className:o}),a)};function OO(e){var{sectors:r,props:n,showLabels:i}=e,{label:a,labelLine:o,dataKey:l}=n;if(!i||!a||!r)return null;var c=C(n),s=z(a,!1),u=z(o,!1),f="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,d=r.map(((e,r)=>{var n=(e.startAngle+e.endAngle)/2,i=Jn(e.cx,e.cy,e.outerRadius+f,n),d=dO(dO(dO(dO({},c),e),{},{stroke:"none"},s),{},{index:r,textAnchor:mO(i.x,e.cx)},i),p=dO(dO(dO(dO({},c),e),{},{fill:"none",stroke:e.fill},u),{},{index:r,points:[Jn(e.cx,e.cy,e.outerRadius,n),i],key:"line"});return t.createElement(V,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(r)},o&&xO(o,p),wO(a,d,ci(e,l)))}));return t.createElement(V,{className:"recharts-pie-labels"},d)}function PO(e){var{sectors:r,activeShape:n,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=He(Mm),{onMouseEnter:c,onClick:s,onMouseLeave:u}=a,f=uO(a,lO),d=_w(c,a.dataKey),p=Lw(u),h=Rw(s,a.dataKey);return null==r?null:t.createElement(t.Fragment,null,r.map(((e,o)=>{if(0===(null==e?void 0:e.startAngle)&&0===(null==e?void 0:e.endAngle)&&1!==r.length)return null;var c=n&&String(o)===l,s=c?n:l?i:null,u=dO(dO({},e),{},{stroke:e.stroke,tabIndex:-1,[Ii]:o,[Ni]:a.dataKey});return t.createElement(V,hO({tabIndex:-1,className:"recharts-pie-sector"},M(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:p(e,o),onClick:h(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),t.createElement(Nw,hO({option:s,isActive:c,shapeType:"sector"},u)))})),t.createElement(OO,{sectors:r,props:a,showLabels:o}))}function EO(e){var{props:r,previousSectorsRef:n}=e,{sectors:i,isAnimationActive:a,animationBegin:o,animationDuration:c,animationEasing:s,activeShape:u,inactiveShape:f,onAnimationStart:d,onAnimationEnd:p}=r,h=Uw(r,"recharts-pie-"),y=n.current,[v,m]=(0,t.useState)(!0),b=(0,t.useCallback)((()=>{"function"==typeof p&&p(),m(!1)}),[p]),x=(0,t.useCallback)((()=>{"function"==typeof d&&d(),m(!0)}),[d]);return t.createElement(oO,{begin:o,duration:c,isActive:a,easing:s,onAnimationStart:x,onAnimationEnd:b,key:h},(e=>{var a=[],o=(i&&i[0]).startAngle;return i.forEach(((t,r)=>{var n=y&&y[r],i=r>0?l()(t,"paddingAngle",0):0;if(n){var c=g(n.endAngle-n.startAngle,t.endAngle-t.startAngle),s=dO(dO({},t),{},{startAngle:o+i,endAngle:o+c(e)+i});a.push(s),o=s.endAngle}else{var{endAngle:u,startAngle:f}=t,d=g(0,u-f)(e),p=dO(dO({},t),{},{startAngle:o+i,endAngle:o+d+i});a.push(p),o=p.endAngle}})),n.current=a,t.createElement(V,null,t.createElement(PO,{sectors:a,activeShape:u,inactiveShape:f,allOtherPieProps:r,showLabels:!v}))}))}function AO(e){var{sectors:r,isAnimationActive:n,activeShape:i,inactiveShape:a}=e,o=(0,t.useRef)(null),l=o.current;return n&&r&&r.length&&(!l||l!==r)?t.createElement(EO,{props:e,previousSectorsRef:o}):t.createElement(PO,{sectors:r,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function jO(e){var{hide:r,className:i,rootTabIndex:a}=e,o=n("recharts-pie",i);return r?null:t.createElement(V,{tabIndex:a,className:o},t.createElement(AO,e))}var SO={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!Oo.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function kO(e){var{id:r}=e,n=uO(e,cO),i=(0,t.useMemo)((()=>R(e.children,zg)),[e.children]),a=He((e=>xw(e,r,i)));return t.createElement(t.Fragment,null,t.createElement(Kw,{fn:vO,args:dO(dO({},e),{},{sectors:a})}),t.createElement(jO,hO({},n,{sectors:a})))}function MO(e){var r=pl(e,SO),{id:n}=r,i=uO(r,sO),a=C(i);return t.createElement($w,{id:n,type:"pie"},(e=>t.createElement(t.Fragment,null,t.createElement(tO,{type:"pie",id:e,data:i.data,dataKey:i.dataKey,hide:i.hide,angleAxisId:0,radiusAxisId:0,name:i.name,nameKey:i.nameKey,tooltipType:i.tooltipType,legendType:i.legendType,fill:i.fill,cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,paddingAngle:i.paddingAngle,minAngle:i.minAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius,cornerRadius:i.cornerRadius,presentationProps:a}),t.createElement(yO,hO({},i,{id:e})),t.createElement(kO,hO({},i,{id:e})),i.children)))}MO.displayName="Pie";var TO=et([Ki],(e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}})),DO=et([TO,ji,Si],((e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}})),CO=()=>He(Tm),IO=()=>He(TO),NO=()=>He(DO),_O=()=>He(Rm);function LO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function RO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?LO(Object(r),!0).forEach((function(t){KO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):LO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function KO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zO(e){var{points:r,mainColor:n,activeDot:i,itemDataKey:a}=e,o=He(Mm),l=_O();if(null==r||null==l)return null;var c=r.find((e=>l.includes(e.payload)));return O(c)?null:(e=>{var{point:r,childIndex:n,mainColor:i,activeDot:a,dataKey:o}=e;if(!1===a||null==r.x||null==r.y)return null;var l,c=RO(RO({index:n,dataKey:o,cx:r.x,cy:r.y,r:4,fill:null!=i?i:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},z(a,!1)),k(a));return l=(0,t.isValidElement)(a)?(0,t.cloneElement)(a,c):"function"==typeof a?a(c):t.createElement(Jb,c),t.createElement(V,{className:"recharts-active-dot"},l)})({point:c,childIndex:Number(o),mainColor:n,dataKey:a,activeDot:i})}function BO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function FO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?BO(Object(r),!0).forEach((function(t){WO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):BO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function WO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var UO=(e,t)=>px(e,"radiusAxis",t),XO=et([UO],(e=>{if(null!=e)return{scale:e}})),VO=et([rh,UO],((e,t)=>{if(null!=e&&null!=t)return FO(FO({},e),{},{scale:t})})),$O=(e,t,r)=>th(e,r),HO=(e,t,r)=>px(e,"angleAxis",r),qO=et([$O,HO],((e,t)=>{if(null!=e&&null!=t)return FO(FO({},e),{},{scale:t})})),YO=et([$O,HO,fh],((e,t,r)=>{if(null!=r&&null!=t)return{scale:t,type:e.type,dataKey:e.dataKey,cx:r.cx,cy:r.cy}})),GO=et([Ji,VO,(e,t,r,n)=>yx(e,"radiusAxis",t,n),qO,(e,t,r,n)=>yx(e,"angleAxis",r,n)],((e,t,r,n,i)=>si(e,"radiusAxis")?Pi(t,r,!1):Pi(n,i,!1))),ZO=et([Qb,(e,t,r,n,i)=>i],((e,t)=>{if(e.some((e=>"radar"===e.type&&t===e.dataKey)))return t})),JO=et([XO,YO,yp,ZO,GO],((e,t,r,n,i)=>{var{chartData:a,dataStartIndex:o,dataEndIndex:l}=r;if(null!=e&&null!=t&&null!=a&&null!=i&&null!=n)return function(e){var{radiusAxis:t,angleAxis:r,displayedData:n,dataKey:i,bandSize:a}=e,{cx:o,cy:l}=r,c=!1,s=[],u="number"!==r.type&&null!=a?a:0;n.forEach(((e,n)=>{var a=ci(e,r.dataKey,n),f=ci(e,i),d=r.scale(a)+u,p=Array.isArray(f)?Nb()(f):f,h=O(p)?void 0:t.scale(p);Array.isArray(f)&&f.length>=2&&(c=!0),s.push(tP(tP({},Jn(o,l,h,d)),{},{name:a,value:f,cx:o,cy:l,radius:h,angle:d,payload:e}))}));var f=[];c&&s.forEach((e=>{if(Array.isArray(e.value)){var r=e.value[0],n=O(r)?void 0:t.scale(r);f.push(tP(tP({},e),{},{radius:n},Jn(o,l,n,e.angle)))}else f.push(e)}));return{points:s,isRange:c,baseLinePoints:f}}({radiusAxis:e,angleAxis:t,displayedData:a.slice(o,l+1),dataKey:n,bandSize:i})})),QO=["id"];function eP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function tP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eP(Object(r),!0).forEach((function(t){rP(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function rP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nP(){return nP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nP.apply(null,arguments)}function iP(e,t){return e&&"none"!==e?e:t}var aP=e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:iP(n,i),value:Ai(r,t),payload:e}]};function oP(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,tooltipType:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,nameKey:void 0,dataKey:t,name:Ai(a,t),hide:o,type:l,color:iP(r,i),unit:""}}}function lP(e){var{points:r,props:i}=e,{dot:a,dataKey:o}=i;if(!a)return null;var{id:l}=i,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,QO),s=C(c),u=z(a,!0),f=r.map(((e,r)=>{var i=tP(tP(tP({key:"dot-".concat(r),r:3},s),u),{},{dataKey:o,cx:e.x,cy:e.y,index:r,payload:e});return function(e,r){return t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Jb,nP({},r,{className:n("recharts-radar-dot","boolean"!=typeof e?e.className:"")}))}(a,i)}));return t.createElement(V,{className:"recharts-radar-dots"},f)}function cP(e){var{points:r,props:n,showLabels:i}=e;if(null==r)return null;var a,{shape:o,isRange:l,baseLinePoints:c,connectNulls:s}=n;return a=t.isValidElement(o)?t.cloneElement(o,tP(tP({},n),{},{points:r})):"function"==typeof o?o(tP(tP({},n),{},{points:r})):t.createElement(Gb,nP({},z(n,!0),{onMouseEnter:e=>{var{onMouseEnter:t}=n;t&&t(n,e)},onMouseLeave:e=>{var{onMouseLeave:t}=n;t&&t(n,e)},points:r,baseLinePoints:l?c:null,connectNulls:s})),t.createElement(V,{className:"recharts-radar-polygon"},a,t.createElement(lP,{props:n,points:r}),i&&Ub.renderCallByParent(n,r))}function sP(e){var{props:r,previousPointsRef:n}=e,{points:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c,onAnimationEnd:s,onAnimationStart:u}=r,f=n.current,d=Uw(r,"recharts-radar-"),[p,h]=(0,t.useState)(!0),y=(0,t.useCallback)((()=>{"function"==typeof s&&s(),h(!1)}),[s]),v=(0,t.useCallback)((()=>{"function"==typeof u&&u(),h(!0)}),[u]);return t.createElement(oO,{begin:o,duration:l,isActive:a,easing:c,key:"radar-".concat(d),onAnimationEnd:y,onAnimationStart:v},(e=>{var a=f&&f.length/i.length,o=1===e?i:i.map(((t,r)=>{var n=f&&f[Math.floor(r*a)];if(n){var i=g(n.x,t.x),o=g(n.y,t.y);return tP(tP({},t),{},{x:i(e),y:o(e)})}var l=g(t.cx,t.x),c=g(t.cy,t.y);return tP(tP({},t),{},{x:l(e),y:c(e)})}));return e>0&&(n.current=o),t.createElement(cP,{points:o,props:r,showLabels:!p})}))}function uP(e){var{points:r,isAnimationActive:n,isRange:i}=e,a=(0,t.useRef)(void 0),o=a.current;return!(n&&r&&r.length)||i||o&&o===r?t.createElement(cP,{points:r,props:e,showLabels:!0}):t.createElement(sP,{props:e,previousPointsRef:a})}var fP={angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!Oo.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"};class dP extends t.PureComponent{render(){var{hide:e,className:r,points:i}=this.props;if(e)return null;var a=n("recharts-radar",r);return t.createElement(t.Fragment,null,t.createElement(V,{className:a},t.createElement(uP,this.props)),t.createElement(zO,{points:i,mainColor:iP(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}function pP(e){var r=Wi(),n=He((t=>JO(t,e.radiusAxisId,e.angleAxisId,r,e.dataKey)));return t.createElement(dP,nP({},e,{points:null==n?void 0:n.points,baseLinePoints:null==n?void 0:n.baseLinePoints,isRange:null==n?void 0:n.isRange}))}class hP extends t.PureComponent{render(){return t.createElement($w,{id:this.props.id,type:"radar"},(e=>t.createElement(t.Fragment,null,t.createElement(tO,{type:"radar",id:e,data:void 0,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:this.props.angleAxisId,radiusAxisId:this.props.radiusAxisId}),t.createElement(Ww,{legendPayload:aP(this.props)}),t.createElement(Kw,{fn:oP,args:this.props}),t.createElement(pP,nP({},this.props,{id:e})))))}}function yP(){return yP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yP.apply(null,arguments)}function vP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function mP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vP(Object(r),!0).forEach((function(t){gP(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function gP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bP(e){return"string"==typeof e?parseInt(e,10):e}function xP(e,t){var r="".concat(t.cx||e.cx),n=Number(r),i="".concat(t.cy||e.cy),a=Number(i);return mP(mP(mP({},t),e),{},{cx:n,cy:a})}function wP(e){return t.createElement(Nw,yP({shapeType:"sector",propTransformer:xP},e))}rP(hP,"displayName","Radar"),rP(hP,"defaultProps",fP);var OP="Invariant failed";var PP=["x","y"];function EP(){return EP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},EP.apply(null,arguments)}function AP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?AP(Object(r),!0).forEach((function(t){SP(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):AP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function SP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kP(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,PP),a="".concat(r),o=parseInt(a,10),l="".concat(n),c=parseInt(l,10),s="".concat(t.height||i.height),u=parseInt(s,10),f="".concat(t.width||i.width),d=parseInt(f,10);return jP(jP(jP(jP(jP({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:u,width:d,name:t.name,radius:t.radius})}function MP(e){return t.createElement(Nw,EP({shapeType:"rectangle",propTransformer:kP,activeClassName:"recharts-active-bar"},e))}var TP=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(d(e))return e;var i=d(r)||O(r);return i?e(r,n):(i||function(e){if(!e)throw new Error(OP)}(!1),t)}},DP=Gr({name:"errorBars",initialState:{},reducers:{addErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]||(e[r]=[]),e[r].push(n)},removeErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]&&(e[r]=e[r].filter((e=>e.dataKey!==n.dataKey||e.direction!==n.direction)))}}}),{addErrorBar:CP,removeErrorBar:IP}=DP.actions,NP=DP.reducer,_P=["children"];var LP=()=>{},RP={data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0},KP=(0,t.createContext)(RP);function zP(e){var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,_P);return t.createElement(KP.Provider,{value:n},r)}function BP(e){var r=Ue(),n=(0,t.useContext)(Vw);return(0,t.useEffect)((()=>{if(null==n)return LP;var t={itemId:n,errorBar:e};return r(CP(t)),()=>{r(IP(t))}}),[r,n,e]),null}function FP(e,t){var r,n,i=He((t=>Ah(t,e))),a=He((e=>Sh(e,t))),o=null!==(r=null==i?void 0:i.allowDataOverflow)&&void 0!==r?r:Eh.allowDataOverflow,l=null!==(n=null==a?void 0:a.allowDataOverflow)&&void 0!==n?n:jh.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function WP(e){var{xAxisId:r,yAxisId:n,clipPathId:i}=e,a=NO(),{needClipX:o,needClipY:l,needClip:c}=FP(r,n);if(!c)return null;var{x:s,y:u,width:f,height:d}=a;return t.createElement("clipPath",{id:"clipPath-".concat(i)},t.createElement("rect",{x:o?s:s-f/2,y:l?u:u-d/2,width:o?f:2*f,height:l?d:2*d}))}var UP=["onMouseEnter","onMouseLeave","onClick"],XP=["value","background","tooltipPosition"],VP=["id"],$P=["onMouseEnter","onClick","onMouseLeave"];function HP(){return HP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},HP.apply(null,arguments)}function qP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function YP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?qP(Object(r),!0).forEach((function(t){GP(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function GP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZP(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var JP=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:Ai(r,t),payload:e}]};function QP(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:Ai(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function eE(e){var r=He(Mm),{data:n,dataKey:i,background:a,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:c,onClick:s}=o,u=ZP(o,UP),f=_w(l,i),d=Lw(c),p=Rw(s,i);if(!a||null==n)return null;var h=z(a,!1);return t.createElement(t.Fragment,null,n.map(((e,n)=>{var{value:o,background:l,tooltipPosition:c}=e,s=ZP(e,XP);if(!l)return null;var y=f(e,n),v=d(e,n),m=p(e,n),g=YP(YP(YP(YP(YP({option:a,isActive:String(n)===r},s),{},{fill:"#eee"},l),h),M(u,e,n)),{},{onMouseEnter:y,onMouseLeave:v,onClick:m,dataKey:i,index:n,className:"recharts-bar-background-rectangle"});return t.createElement(MP,HP({key:"background-bar-".concat(n)},g))})))}function tE(e){var{data:r,props:n,showLabels:i}=e,a=C(n),{id:o}=a,l=ZP(a,VP),{shape:c,dataKey:s,activeBar:u}=n,f=He(Mm),d=He(Dm),{onMouseEnter:p,onClick:h,onMouseLeave:y}=n,v=ZP(n,$P),m=_w(p,s),g=Lw(y),b=Rw(h,s);return r?t.createElement(t.Fragment,null,r.map(((e,r)=>{var n=u&&String(r)===f&&(null==d||s===d),i=n?u:c,a=YP(YP(YP({},l),e),{},{isActive:n,option:i,index:r,dataKey:s});return t.createElement(V,HP({className:"recharts-bar-rectangle"},M(v,e,r),{onMouseEnter:m(e,r),onMouseLeave:g(e,r),onClick:b(e,r),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),t.createElement(MP,a))})),i&&Ub.renderCallByParent(n,r)):null}function rE(e){var{props:r,previousRectanglesRef:n}=e,{data:i,layout:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s,onAnimationEnd:u,onAnimationStart:f}=r,d=n.current,p=Uw(r,"recharts-bar-"),[h,y]=(0,t.useState)(!1),v=(0,t.useCallback)((()=>{"function"==typeof u&&u(),y(!1)}),[u]),m=(0,t.useCallback)((()=>{"function"==typeof f&&f(),y(!0)}),[f]);return t.createElement(oO,{begin:l,duration:c,isActive:o,easing:s,onAnimationEnd:v,onAnimationStart:m,key:p},(e=>{var o=1===e?i:null==i?void 0:i.map(((t,r)=>{var n=d&&d[r];if(n)return YP(YP({},t),{},{x:b(n.x,t.x,e),y:b(n.y,t.y,e),width:b(n.width,t.width,e),height:b(n.height,t.height,e)});if("horizontal"===a){var i=b(0,t.height,e);return YP(YP({},t),{},{y:t.y+t.height-i,height:i})}var o=b(0,t.width,e);return YP(YP({},t),{},{width:o})}));return e>0&&(n.current=null!=o?o:null),null==o?null:t.createElement(V,null,t.createElement(tE,{props:r,data:o,showLabels:!h}))}))}function nE(e){var{data:r,isAnimationActive:n}=e,i=(0,t.useRef)(null);return n&&r&&r.length&&(null==i.current||i.current!==r)?t.createElement(rE,{previousRectanglesRef:i,props:e}):t.createElement(tE,{props:e,data:r,showLabels:!0})}var iE=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:ci(e,t)}};class aE extends t.PureComponent{render(){var{hide:e,data:r,dataKey:i,className:a,xAxisId:o,yAxisId:l,needClip:c,background:s,id:u}=this.props;if(e)return null;var f=n("recharts-bar",a),d=u;return t.createElement(V,{className:f,id:u},c&&t.createElement("defs",null,t.createElement(WP,{clipPathId:d,xAxisId:o,yAxisId:l})),t.createElement(V,{className:"recharts-bar-rectangles",clipPath:c?"url(#clipPath-".concat(d,")"):void 0},t.createElement(eE,{data:r,dataKey:i,background:s,allOtherBarProps:this.props}),t.createElement(nE,this.props)),this.props.children)}}var oE={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!Oo.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function lE(e){var r,{xAxisId:n,yAxisId:i,hide:a,legendType:o,minPointSize:l,activeBar:c,animationBegin:s,animationDuration:u,animationEasing:f,isAnimationActive:d}=e,{needClip:p}=FP(n,i),h=Qi(),y=Wi(),v=R(e.children,zg),m=He((t=>EE(t,n,i,y,e.id,v)));if("vertical"!==h&&"horizontal"!==h)return null;var g=null==m?void 0:m[0];return r=null==g||null==g.height||null==g.width?0:"vertical"===h?g.height/2:g.width/2,t.createElement(zP,{xAxisId:n,yAxisId:i,data:m,dataPointFormatter:iE,errorBarOffset:r},t.createElement(aE,HP({},e,{layout:h,needClip:p,data:m,xAxisId:n,yAxisId:i,hide:a,legendType:o,minPointSize:l,activeBar:c,animationBegin:s,animationDuration:u,animationEasing:f,isAnimationActive:d})))}function cE(e){var r=pl(e,oE),n=Wi();return t.createElement($w,{id:r.id,type:"bar"},(e=>t.createElement(t.Fragment,null,t.createElement(Fw,{legendPayload:JP(r)}),t.createElement(Kw,{fn:QP,args:r}),t.createElement(eO,{type:"bar",id:e,data:void 0,xAxisId:r.xAxisId,yAxisId:r.yAxisId,zAxisId:0,dataKey:r.dataKey,stackId:vi(r.stackId),hide:r.hide,barSize:r.barSize,minPointSize:r.minPointSize,maxBarSize:r.maxBarSize,isPanorama:n}),t.createElement(lE,HP({},r,{id:e})))))}function sE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function uE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sE(Object(r),!0).forEach((function(t){fE(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sE(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function fE(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}cE.displayName="Bar";var dE=et([Nh,(e,t,r,n,i)=>i],((e,t)=>e.filter((e=>"bar"===e.type)).find((e=>e.id===t)))),pE=et([dE],(e=>null==e?void 0:e.maxBarSize)),hE=(e,t,r)=>{var n=null!=r?r:e;if(!O(n))return v(n,t,0)},yE=et([Ji,Nh,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],((e,t,r,n,i)=>t.filter((t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n)).filter((e=>e.isPanorama===i)).filter((e=>!1===e.hide)).filter((e=>"bar"===e.type)))),vE=(e,t,r)=>{var n=e.filter(bh),i=e.filter((e=>null==e.stackId)),a=n.reduce(((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e)),{}),o=Object.entries(a).map((e=>{var[n,i]=e,a=i.map((e=>e.dataKey));return{stackId:n,dataKeys:a,barSize:hE(t,r,i[0].barSize)}})),l=i.map((e=>({stackId:void 0,dataKeys:[e.dataKey].filter((e=>null!=e)),barSize:hE(t,r,e.barSize)})));return[...o,...l]},mE=et([yE,Wp,(e,t,r)=>"horizontal"===Ji(e)?tv(e,"xAxis",t):tv(e,"yAxis",r)],vE),gE=(e,t,r,n)=>{var i,a;return"horizontal"===Ji(e)?(i=fv(e,"xAxis",t,n),a=uv(e,"xAxis",t,n)):(i=fv(e,"yAxis",r,n),a=uv(e,"yAxis",r,n)),Pi(i,a)};var bE=(e,t,r,n,i,a,o)=>{var l=O(o)?t:o,c=function(e,t,r,n,i){var a=n.length;if(!(a<1)){var o,l=v(e,r,0,!0),c=[];if(Ho(n[0].barSize)){var s=!1,u=r/a,f=n.reduce(((e,t)=>e+(t.barSize||0)),0);(f+=(a-1)*l)>=r&&(f-=(a-1)*l,l=0),f>=r&&u>0&&(s=!0,f=a*(u*=.9));var d={offset:((r-f)/2|0)-l,size:0};o=n.reduce(((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:s?u:null!==(r=t.barSize)&&void 0!==r?r:0}}];return d=n[n.length-1].position,n}),c)}else{var p=v(t,r,0,!0);r-2*p-(a-1)*l<=0&&(l=0);var h=(r-2*p-(a-1)*l)/a;h>1&&(h>>=0);var y=Ho(i)?Math.min(h,i):h;o=n.reduce(((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p+(h+l)*r+(h-y)/2,size:y}}]),c)}return o}}(r,n,i!==a?i:a,e,l);return i!==a&&null!=c&&(c=c.map((e=>uE(uE({},e),{},{position:uE(uE({},e.position),{},{offset:e.position.offset-i/2})})))),c},xE=et([mE,zp,Bp,Fp,(e,t,r,n,i)=>{var a,o,l=dE(e,t,r,n,i);if(null!=l){var c,s,u=Ji(e),f=zp(e),{maxBarSize:d}=l,p=O(d)?f:d;return"horizontal"===u?(c=fv(e,"xAxis",t,n),s=uv(e,"xAxis",t,n)):(c=fv(e,"yAxis",r,n),s=uv(e,"yAxis",r,n)),null!==(a=null!==(o=Pi(c,s,!0))&&void 0!==o?o:p)&&void 0!==a?a:0}},gE,pE],bE),wE=et([xE,dE],((e,t)=>{if(null!=e&&null!=t){var r=e.find((e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey)));if(null!=r)return r.position}})),OE=(e,t)=>{var r=hh(t);if(e&&null!=r&&null!=t){var{stackId:n}=t;if(null!=n){var i=e[n];if(i){var{stackedData:a}=i;if(a)return a.find((e=>e.key===r))}}}},PE=et([(e,t,r,n)=>"horizontal"===Ji(e)?Jh(e,"yAxis",r,n):Jh(e,"xAxis",t,n),dE],OE),EE=et([Ki,(e,t,r,n)=>fv(e,"xAxis",t,n),(e,t,r,n)=>fv(e,"yAxis",r,n),(e,t,r,n)=>uv(e,"xAxis",t,n),(e,t,r,n)=>uv(e,"yAxis",r,n),wE,Ji,vp,gE,PE,dE,(e,t,r,n,i,a)=>a],((e,t,r,n,i,a,o,l,c,f,d,p)=>{var{chartData:h,dataStartIndex:y,dataEndIndex:v}=l;if(null!=d&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var m,{data:g}=d;if(null!=(m=null!=g&&g.length>0?g:null==h?void 0:h.slice(y,v+1)))return function(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:f,stackedData:d,displayedData:p,offset:h,cells:y}=e,v="horizontal"===t?l:o,m=d?v.scale.domain():null,g=bi({numericAxis:v});return p.map(((e,p)=>{var v,b,x,w,O,P;d?v=pi(d[p],m):(v=ci(e,r),Array.isArray(v)||(v=[g,v]));var E=TP(n,0)(v[1],p);if("horizontal"===t){var A,[j,S]=[l.scale(v[0]),l.scale(v[1])];b=gi({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:p}),x=null!==(A=null!=S?S:j)&&void 0!==A?A:void 0,w=i.size;var k=j-S;if(O=u(k)?0:k,P={x:b,y:h.top,width:w,height:h.height},Math.abs(E)>0&&Math.abs(O)<Math.abs(E)){var M=s(O||E)*(Math.abs(E)-Math.abs(O));x-=M,O+=M}}else{var[T,D]=[o.scale(v[0]),o.scale(v[1])];b=T,x=gi({axis:l,ticks:f,bandSize:a,offset:i.offset,entry:e,index:p}),w=D-T,O=i.size,P={x:h.left,y:x,width:h.width,height:O},Math.abs(E)>0&&Math.abs(w)<Math.abs(E)&&(w+=s(w||E)*(Math.abs(E)-Math.abs(w)))}return null==b||null==x||null==w||null==O?null:YP(YP({},e),{},{x:b,y:x,width:w,height:O,value:d?v:v[1],payload:e,background:P,tooltipPosition:{x:b+w/2,y:x+O/2}},y&&y[p]&&y[p].props)})).filter(Boolean)}({layout:o,barSettings:d,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:f,displayedData:m,offset:e,cells:p})}}));function AE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?AE(Object(r),!0).forEach((function(t){SE(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):AE(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function SE(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var kE=et([(e,t)=>rh(e,t),(e,t)=>px(e,"radiusAxis",t)],((e,t)=>{if(null!=e&&null!=t)return jE(jE({},e),{},{scale:t})})),ME=(e,t,r,n)=>vx(e,"radiusAxis",t,n),TE=et([(e,t,r)=>th(e,r),(e,t,r)=>px(e,"angleAxis",r)],((e,t)=>{if(null!=e&&null!=t)return jE(jE({},e),{},{scale:t})})),DE=(e,t,r,n)=>yx(e,"angleAxis",r,n),CE=et([Qb,(e,t,r,n)=>n],((e,t)=>{if(e.some((e=>"radialBar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId)))return t})),IE=et([Ji,kE,ME,TE,DE],((e,t,r,n,i)=>si(e,"radiusAxis")?Pi(t,r,!1):Pi(n,i,!1))),NE=et([TE,kE,Ji],((e,t,r)=>{var n="radial"===r?e:t;if(null!=n&&null!=n.scale)return bi({numericAxis:n})})),_E=(e,t,r,n,i)=>n.maxBarSize,LE=et([Ji,Qb,(e,t,r,n,i)=>r,(e,t,r,n,i)=>t],((e,t,r,n)=>t.filter((t=>"centric"===e?t.angleAxisId===r:t.radiusAxisId===n)).filter((e=>!1===e.hide)).filter((e=>"radialBar"===e.type)))),RE=et([LE,Wp,()=>{}],vE),KE=et([Ji,zp,TE,DE,kE,ME,_E],((e,t,r,n,i,a,o)=>{var l,c,s,u,f=O(o)?t:o;return"centric"===e?null!==(s=null!==(u=Pi(r,n,!0))&&void 0!==u?u:f)&&void 0!==s?s:0:null!==(l=null!==(c=Pi(i,a,!0))&&void 0!==c?c:f)&&void 0!==l?l:0})),zE=et([RE,zp,Bp,Fp,KE,IE,_E],bE),BE=et([zE,CE],((e,t)=>{if(null!=e&&null!=t){var r=e.find((e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey)));if(null!=r)return r.position}})),FE=et([tx],(e=>e.filter((e=>"radialBar"===e.type)).filter(bh))),WE=et([FE,yp,mh],gh),UE=et([WE,FE,Up],Zh),XE=et([(e,t,r)=>"centric"===Ji(e)?UE(e,"radiusAxis",t):UE(e,"angleAxis",r),CE],OE),VE=et([TE,DE,kE,ME,hp,CE,IE,Ji,NE,fh,(e,t,r,n,i)=>i,BE,XE],((e,t,r,n,i,a,o,l,c,u,f,d,p)=>{var{chartData:h,dataStartIndex:y,dataEndIndex:v}=i;if(null==a||null==r||null==e||null==h||null==o||null==d||"centric"!==l&&"radial"!==l||null==n)return[];var{dataKey:m,minPointSize:g}=a,{cx:b,cy:x,startAngle:w,endAngle:O}=u;return function(e){var{displayedData:t,stackedData:r,dataStartIndex:n,stackedDomain:i,dataKey:a,baseValue:o,layout:l,radiusAxis:c,radiusAxisTicks:u,bandSize:f,pos:d,angleAxis:p,minPointSize:h,cx:y,cy:v,angleAxisTicks:m,cells:g,startAngle:b,endAngle:x}=e;return(null!=t?t:[]).map(((e,t)=>{var w,O,P,E,A,j;if(r?w=pi(r[n+t],i):(w=ci(e,a),Array.isArray(w)||(w=[o,w])),"radial"===l){O=gi({axis:c,ticks:u,bandSize:f,offset:d.offset,entry:e,index:t}),A=p.scale(w[1]),E=p.scale(w[0]),P=(null!=O?O:0)+d.size;var S=A-E;if(Math.abs(h)>0&&Math.abs(S)<Math.abs(h))A+=s(S||h)*(Math.abs(h)-Math.abs(S));j={background:{cx:y,cy:v,innerRadius:O,outerRadius:P,startAngle:b,endAngle:x}}}else{O=c.scale(w[0]),P=c.scale(w[1]),A=(null!=(E=gi({axis:p,ticks:m,bandSize:f,offset:d.offset,entry:e,index:t}))?E:0)+d.size;var k=P-O;if(Math.abs(h)>0&&Math.abs(k)<Math.abs(h))P+=s(k||h)*(Math.abs(h)-Math.abs(k))}return JE(JE(JE({},e),j),{},{payload:e,value:r?w:w[1],cx:y,cy:v,innerRadius:O,outerRadius:P,startAngle:E,endAngle:A},g&&g[t]&&g[t].props)}))}({angleAxis:e,angleAxisTicks:t,bandSize:o,baseValue:c,cells:f,cx:b,cy:x,dataKey:m,dataStartIndex:y,displayedData:h.slice(y,v+1),endAngle:O,layout:l,minPointSize:g,pos:d,radiusAxis:r,radiusAxisTicks:n,stackedData:p,stackedDomain:p?("centric"===l?r:e).scale.domain():null,startAngle:w})})),$E=et([yp,(e,t)=>t],((e,t)=>{var{chartData:r,dataStartIndex:n,dataEndIndex:i}=e;if(null==r)return[];var a=r.slice(n,i+1);return 0===a.length?[]:a.map((e=>({type:t,value:e.name,color:e.fill,payload:e})))})),HE=["shape","activeShape","cornerRadius","id"],qE=["onMouseEnter","onClick","onMouseLeave"],YE=["value","background"];function GE(){return GE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},GE.apply(null,arguments)}function ZE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function JE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ZE(Object(r),!0).forEach((function(t){QE(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ZE(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function QE(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eA(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var tA=[];function rA(e){var{sectors:r,allOtherRadialBarProps:n,showLabels:i}=e,{shape:a,activeShape:o,cornerRadius:l,id:c}=n,s=eA(n,HE),u=C(s),f=He(Mm),{onMouseEnter:d,onClick:p,onMouseLeave:h}=n,y=eA(n,qE),v=_w(d,n.dataKey),m=Lw(h),g=Rw(p,n.dataKey);return null==r?null:t.createElement(t.Fragment,null,r.map(((e,r)=>{var n=o&&f===String(r),i=v(e,r),c=m(e,r),d=g(e,r),p=JE(JE(JE(JE({},u),{},{cornerRadius:bP(l)},e),M(y,e,r)),{},{onMouseEnter:i,onMouseLeave:c,onClick:d,key:"sector-".concat(r),className:"recharts-radial-bar-sector ".concat(e.className),forceCornerRadius:s.forceCornerRadius,cornerIsExternal:s.cornerIsExternal,isActive:n,option:n?o:a});return t.createElement(wP,p)})),i&&Ub.renderCallByParent(n,r))}function nA(e){var{props:r,previousSectorsRef:n}=e,{data:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c,onAnimationEnd:s,onAnimationStart:u}=r,f=Uw(r,"recharts-radialbar-"),d=n.current,[p,h]=(0,t.useState)(!0),y=(0,t.useCallback)((()=>{"function"==typeof s&&s(),h(!1)}),[s]),v=(0,t.useCallback)((()=>{"function"==typeof u&&u(),h(!0)}),[u]);return t.createElement(oO,{begin:o,duration:l,isActive:a,easing:c,onAnimationStart:v,onAnimationEnd:y,key:f},(e=>{var a=1===e?i:(null!=i?i:tA).map(((t,r)=>{var n=d&&d[r];if(n){var i=g(n.startAngle,t.startAngle),a=g(n.endAngle,t.endAngle);return JE(JE({},t),{},{startAngle:i(e),endAngle:a(e)})}var{endAngle:o,startAngle:l}=t,c=g(l,o);return JE(JE({},t),{},{endAngle:c(e)})}));return e>0&&(n.current=null!=a?a:null),t.createElement(V,null,t.createElement(rA,{sectors:null!=a?a:tA,allOtherRadialBarProps:r,showLabels:!p}))}))}function iA(e){var{data:r=[],isAnimationActive:n}=e,i=(0,t.useRef)(null),a=i.current;return n&&r&&r.length&&(!a||a!==r)?t.createElement(nA,{props:e,previousSectorsRef:i}):t.createElement(rA,{sectors:r,allOtherRadialBarProps:e,showLabels:!0})}function aA(e){var r=He((t=>$E(t,e.legendType)));return t.createElement(Ww,{legendPayload:null!=r?r:[]})}function oA(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,name:a,hide:o,fill:l,tooltipType:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:l,nameKey:void 0,dataKey:t,name:Ai(a,t),hide:o,type:c,color:l,unit:""}}}class lA extends t.PureComponent{renderBackground(e){if(null==e)return null;var{cornerRadius:r}=this.props,i=z(this.props.background,!1);return e.map(((e,a)=>{var{value:o,background:l}=e,c=eA(e,YE);if(!l)return null;var s=JE(JE(JE(JE(JE({cornerRadius:bP(r)},c),{},{fill:"#eee"},l),i),M(this.props,e,a)),{},{index:a,key:"sector-".concat(a),className:n("recharts-radial-bar-background-sector",null==i?void 0:i.className),option:l,isActive:!1});return t.createElement(wP,s)}))}render(){var{hide:e,data:r,className:i,background:a}=this.props;if(e)return null;var o=n("recharts-area",i);return t.createElement(V,{className:o},a&&t.createElement(V,{className:"recharts-radial-bar-background"},this.renderBackground(r)),t.createElement(V,{className:"recharts-radial-bar-sectors"},t.createElement(iA,this.props)))}}function cA(e){var r,n=R(e.children,zg),i={data:void 0,hide:!1,id:e.id,dataKey:e.dataKey,minPointSize:e.minPointSize,stackId:vi(e.stackId),maxBarSize:e.maxBarSize,barSize:e.barSize,type:"radialBar",angleAxisId:e.angleAxisId,radiusAxisId:e.radiusAxisId},a=null!==(r=He((t=>VE(t,e.radiusAxisId,e.angleAxisId,i,n))))&&void 0!==r?r:tA;return t.createElement(t.Fragment,null,t.createElement(Kw,{fn:oA,args:JE(JE({},e),{},{data:a})}),t.createElement(lA,GE({},e,{data:a})))}var sA={angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!Oo.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1};class uA extends t.PureComponent{render(){return t.createElement($w,{id:this.props.id,type:"radialBar"},(e=>{var r,n,i;return t.createElement(t.Fragment,null,t.createElement(tO,{type:"radialBar",id:e,data:void 0,dataKey:this.props.dataKey,hide:null!==(r=this.props.hide)&&void 0!==r?r:sA.hide,angleAxisId:null!==(n=this.props.angleAxisId)&&void 0!==n?n:sA.angleAxisId,radiusAxisId:null!==(i=this.props.radiusAxisId)&&void 0!==i?i:sA.radiusAxisId,stackId:vi(this.props.stackId),barSize:this.props.barSize,minPointSize:this.props.minPointSize,maxBarSize:this.props.maxBarSize}),t.createElement(aA,this.props),t.createElement(cA,GE({},this.props,{id:e})))}))}}function fA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function dA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fA(Object(r),!0).forEach((function(t){pA(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fA(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function pA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}QE(uA,"displayName","RadialBar"),QE(uA,"defaultProps",sA);var hA=["Webkit","Moz","O","ms"],yA=e=>{var{chartData:r}=e,n=Ue(),i=Wi();return(0,t.useEffect)((()=>i?()=>{}:(n(bg(r)),()=>{n(bg(void 0))})),[r,n,i]),null},vA=e=>{var{computedData:r}=e,n=Ue();return(0,t.useEffect)((()=>(n(wg(r)),()=>{n(bg(void 0))})),[r,n]),null},mA=e=>e.chartData.chartData,gA=()=>He(mA),bA=e=>{var{dataStartIndex:t,dataEndIndex:r}=e.chartData;return{startIndex:t,endIndex:r}},xA=()=>He(bA),wA=(0,t.createContext)((()=>{})),OA={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},PA=Gr({name:"brush",initialState:OA,reducers:{setBrushSettings:(e,t)=>null==t.payload?OA:t.payload}}),{setBrushSettings:EA}=PA.actions,AA=PA.reducer;function jA(){return jA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jA.apply(null,arguments)}function SA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function kA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?SA(Object(r),!0).forEach((function(t){MA(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):SA(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function MA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TA(e){var{x:r,y:n,width:i,height:a,stroke:o}=e,l=Math.floor(n+a/2)-1;return t.createElement(t.Fragment,null,t.createElement("rect",{x:r,y:n,width:i,height:a,fill:o,stroke:"none"}),t.createElement("line",{x1:r+1,y1:l,x2:r+i-1,y2:l,fill:"none",stroke:"#fff"}),t.createElement("line",{x1:r+1,y1:l+2,x2:r+i-1,y2:l+2,fill:"none",stroke:"#fff"}))}function DA(e){var{travellerProps:r,travellerType:n}=e;return t.isValidElement(n)?t.cloneElement(n,r):"function"==typeof n?n(r):t.createElement(TA,r)}function CA(e){var r,n,{otherProps:i,travellerX:a,id:o,onMouseEnter:l,onMouseLeave:c,onMouseDown:s,onTouchStart:u,onTravellerMoveKeyboard:f,onFocus:d,onBlur:p}=e,{y:h,x:y,travellerWidth:v,height:m,traveller:g,ariaLabel:b,data:x,startIndex:w,endIndex:O}=i,P=Math.max(a,y),E=kA(kA({},C(i)),{},{x:P,y:h,width:v,height:m}),A=b||"Min value: ".concat(null===(r=x[w])||void 0===r?void 0:r.name,", Max value: ").concat(null===(n=x[O])||void 0===n?void 0:n.name);return t.createElement(V,{tabIndex:0,role:"slider","aria-label":A,"aria-valuenow":a,className:"recharts-brush-traveller",onMouseEnter:l,onMouseLeave:c,onMouseDown:s,onTouchStart:u,onKeyDown:e=>{["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),f("ArrowRight"===e.key?1:-1,o))},onFocus:d,onBlur:p,style:{cursor:"col-resize"}},t.createElement(DA,{travellerType:g,travellerProps:E}))}function IA(e){var{index:t,data:r,tickFormatter:n,dataKey:i}=e,a=ci(r[t],i,t);return"function"==typeof n?n(a,t):a}function NA(e,t){for(var r=0,n=e.length-1;n-r>1;){var i=Math.floor((r+n)/2);e[i]>t?n=i:r=i}return t>=e[n]?n:r}function _A(e){var{startX:t,endX:r,scaleValues:n,gap:i,data:a}=e,o=a.length-1,l=Math.min(t,r),c=Math.max(t,r),s=NA(n,l),u=NA(n,c);return{startIndex:s-s%i,endIndex:u===o?o:u-u%i}}function LA(e){var{x:r,y:n,width:i,height:a,fill:o,stroke:l}=e;return t.createElement("rect",{stroke:l,fill:o,x:r,y:n,width:i,height:a})}function RA(e){var{startIndex:r,endIndex:n,y:i,height:a,travellerWidth:o,stroke:l,tickFormatter:c,dataKey:s,data:u,startX:f,endX:d}=e,p={pointerEvents:"none",fill:l};return t.createElement(V,{className:"recharts-brush-texts"},t.createElement(mb,jA({textAnchor:"end",verticalAnchor:"middle",x:Math.min(f,d)-5,y:i+a/2},p),IA({index:r,tickFormatter:c,dataKey:s,data:u})),t.createElement(mb,jA({textAnchor:"start",verticalAnchor:"middle",x:Math.max(f,d)+o+5,y:i+a/2},p),IA({index:n,tickFormatter:c,dataKey:s,data:u})))}function KA(e){var{y:r,height:n,stroke:i,travellerWidth:a,startX:o,endX:l,onMouseEnter:c,onMouseLeave:s,onMouseDown:u,onTouchStart:f}=e,d=Math.min(o,l)+a,p=Math.max(Math.abs(l-o)-a,0);return t.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:c,onMouseLeave:s,onMouseDown:u,onTouchStart:f,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:d,y:r,width:p,height:n})}function zA(e){var{x:r,y:n,width:i,height:a,data:o,children:l,padding:c}=e;if(!(1===t.Children.count(l)))return null;var s=t.Children.only(l);return s?t.cloneElement(s,{x:r,y:n,width:i,height:a,margin:c,compact:!0,data:o}):null}var BA=e=>e.changedTouches&&!!e.changedTouches.length;class FA extends t.PureComponent{constructor(e){super(e),MA(this,"handleDrag",(e=>{this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.state.isTravellerMoving?this.handleTravellerMove(e):this.state.isSlideMoving&&this.handleSlideDrag(e)})),MA(this,"handleTouchMove",(e=>{null!=e.changedTouches&&e.changedTouches.length>0&&this.handleDrag(e.changedTouches[0])})),MA(this,"handleDragEnd",(()=>{this.setState({isTravellerMoving:!1,isSlideMoving:!1},(()=>{var{endIndex:e,onDragEnd:t,startIndex:r}=this.props;null==t||t({endIndex:e,startIndex:r})})),this.detachDragEndListener()})),MA(this,"handleLeaveWrapper",(()=>{(this.state.isTravellerMoving||this.state.isSlideMoving)&&(this.leaveTimer=window.setTimeout(this.handleDragEnd,this.props.leaveTimeOut))})),MA(this,"handleEnterSlideOrTraveller",(()=>{this.setState({isTextActive:!0})})),MA(this,"handleLeaveSlideOrTraveller",(()=>{this.setState({isTextActive:!1})})),MA(this,"handleSlideDragStart",(e=>{var t=BA(e)?e.changedTouches[0]:e;this.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:t.pageX}),this.attachDragEndListener()})),MA(this,"handleTravellerMoveKeyboard",((e,t)=>{var{data:r,gap:n}=this.props,{scaleValues:i,startX:a,endX:o}=this.state;if(null!=i){var l=this.state[t],c=i.indexOf(l);if(-1!==c){var s=c+e;if(!(-1===s||s>=i.length)){var u=i[s];"startX"===t&&u>=o||"endX"===t&&u<=a||this.setState({[t]:u},(()=>{this.props.onChange(_A({startX:this.state.startX,endX:this.state.endX,data:r,gap:n,scaleValues:i}))}))}}}})),this.travellerDragStartHandlers={startX:this.handleTravellerDragStart.bind(this,"startX"),endX:this.handleTravellerDragStart.bind(this,"endX")},this.state={brushMoveStartX:0,movingTravellerId:void 0,endX:0,startX:0,slideMoveStartX:0}}static getDerivedStateFromProps(e,t){var{data:r,width:n,x:i,travellerWidth:a,startIndex:o,endIndex:l,startIndexControlledFromProps:c,endIndexControlledFromProps:s}=e;if(r!==t.prevData)return kA({prevData:r,prevTravellerWidth:a,prevX:i,prevWidth:n},r&&r.length?(e=>{var{data:t,startIndex:r,endIndex:n,x:i,width:a,travellerWidth:o}=e;if(!t||!t.length)return{};var l=t.length,c=vc().domain(ic()(0,l)).range([i,i+a-o]),s=c.domain().map((e=>c(e)));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}})({data:r,width:n,x:i,travellerWidth:a,startIndex:o,endIndex:l}):{scale:void 0,scaleValues:void 0});var u=t.scale;if(u&&(n!==t.prevWidth||i!==t.prevX||a!==t.prevTravellerWidth)){u.range([i,i+n-a]);var f=u.domain().map((e=>u(e))).filter(Boolean);return{prevData:r,prevTravellerWidth:a,prevX:i,prevWidth:n,startX:u(e.startIndex),endX:u(e.endIndex),scaleValues:f}}if(t.scale&&!t.isSlideMoving&&!t.isTravellerMoving&&!t.isTravellerFocused&&!t.isTextActive){if(null!=c&&t.prevStartIndexControlledFromProps!==c)return{startX:t.scale(c),prevStartIndexControlledFromProps:c};if(null!=s&&t.prevEndIndexControlledFromProps!==s)return{endX:t.scale(s),prevEndIndexControlledFromProps:s}}return null}componentWillUnmount(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}attachDragEndListener(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}detachDragEndListener(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}handleSlideDrag(e){var{slideMoveStartX:t,startX:r,endX:n,scaleValues:i}=this.state;if(null!=i){var{x:a,width:o,travellerWidth:l,startIndex:c,endIndex:s,onChange:u,data:f,gap:d}=this.props,p=e.pageX-t;p>0?p=Math.min(p,a+o-l-n,a+o-l-r):p<0&&(p=Math.max(p,a-r,a-n));var h=_A({startX:r+p,endX:n+p,data:f,gap:d,scaleValues:i});h.startIndex===c&&h.endIndex===s||!u||u(h),this.setState({startX:r+p,endX:n+p,slideMoveStartX:e.pageX})}}handleTravellerDragStart(e,t){var r=BA(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}handleTravellerMove(e){var{brushMoveStartX:t,movingTravellerId:r,endX:n,startX:i,scaleValues:a}=this.state;if(null!=r){var o=this.state[r],{x:l,width:c,travellerWidth:s,onChange:u,gap:f,data:d}=this.props,p={startX:this.state.startX,endX:this.state.endX,data:d,gap:f,scaleValues:a},h=e.pageX-t;h>0?h=Math.min(h,l+c-s-o):h<0&&(h=Math.max(h,l-o)),p[r]=o+h;var y=_A(p),{startIndex:v,endIndex:m}=y;this.setState({[r]:o+h,brushMoveStartX:e.pageX},(()=>{var e;u&&(e=d.length-1,("startX"===r&&(n>i?v%f==0:m%f==0)||n<i&&m===e||"endX"===r&&(n>i?m%f==0:v%f==0)||n>i&&m===e)&&u(y))}))}}render(){var{data:e,className:r,children:i,x:a,y:o,dy:l,width:c,height:s,alwaysShowText:u,fill:f,stroke:p,startIndex:h,endIndex:y,travellerWidth:v,tickFormatter:m,dataKey:g,padding:b}=this.props,{startX:x,endX:w,isTextActive:O,isSlideMoving:P,isTravellerMoving:E,isTravellerFocused:A}=this.state;if(!e||!e.length||!d(a)||!d(o)||!d(c)||!d(s)||c<=0||s<=0)return null;var j=n("recharts-brush",r),S=((e,t)=>{if(e){var r=e.replace(/(\w)/,(e=>e.toUpperCase())),n=hA.reduce(((e,n)=>dA(dA({},e),{},{[n+r]:t})),{});return n[e]=t,n}})("userSelect","none"),k=o+(null!=l?l:0);return t.createElement(V,{className:j,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:S},t.createElement(LA,{x:a,y:k,width:c,height:s,fill:f,stroke:p}),t.createElement(Ui,null,t.createElement(zA,{x:a,y:k,width:c,height:s,data:e,padding:b},i)),t.createElement(KA,{y:k,height:s,stroke:p,travellerWidth:v,startX:x,endX:w,onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart}),t.createElement(CA,{travellerX:x,id:"startX",otherProps:kA(kA({},this.props),{},{y:k}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.startX,onTouchStart:this.travellerDragStartHandlers.startX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),t.createElement(CA,{travellerX:w,id:"endX",otherProps:kA(kA({},this.props),{},{y:k}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.endX,onTouchStart:this.travellerDragStartHandlers.endX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),(O||P||E||A||u)&&t.createElement(RA,{startIndex:h,endIndex:y,y:k,height:s,travellerWidth:v,stroke:p,tickFormatter:m,dataKey:g,data:e,startX:x,endX:w}))}}function WA(e){var r,n,i,a,o=Ue(),l=gA(),c=xA(),s=(0,t.useContext)(wA),u=e.onChange,{startIndex:f,endIndex:d}=e;(0,t.useEffect)((()=>{o(xg({startIndex:f,endIndex:d}))}),[o,d,f]),r=He(Vp),n=He(Hp),i=He((e=>e.chartData.dataStartIndex)),a=He((e=>e.chartData.dataEndIndex)),(0,t.useEffect)((()=>{if(null!=r&&null!=i&&null!=a&&null!=n){var e={startIndex:i,endIndex:a};ug.emit(dg,r,e,n)}}),[a,i,n,r]);var p=(0,t.useCallback)((e=>{if(null!=c){var{startIndex:t,endIndex:r}=c;e.startIndex===t&&e.endIndex===r||(null==s||s(e),null==u||u(e),o(xg(e)))}}),[u,s,o,c]),h=He(Vi);if(null==h||null==c||null==l||!l.length)return null;var{startIndex:y,endIndex:v}=c,{x:m,y:g,width:b}=h,x={data:l,x:m,y:g,width:b,startIndex:y,endIndex:v,onChange:p};return t.createElement(FA,jA({},e,x,{startIndexControlledFromProps:null!=f?f:void 0,endIndexControlledFromProps:null!=d?d:void 0}))}function UA(e){var r=Ue();return(0,t.useEffect)((()=>(r(EA(e)),()=>{r(EA(null))})),[r,e]),null}var XA={height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1};function VA(e){var r=pl(e,XA);return t.createElement(t.Fragment,null,t.createElement(UA,{height:r.height,x:r.x,y:r.y,width:r.width,padding:r.padding}),t.createElement(WA,r))}function $A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function HA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$A(Object(r),!0).forEach((function(t){qA(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$A(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function qA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}VA.displayName="Brush";var YA=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return{x:Math.min(r,i),y:Math.min(n,a),width:Math.abs(i-r),height:Math.abs(a-n)}};class GA{static create(e){return new GA(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}qA(GA,"EPS",1e-4);var ZA=e=>{var t=Object.keys(e).reduce(((t,r)=>HA(HA({},t),{},{[r]:GA.create(e[r])})),{});return HA(HA({},t),{},{apply(e){var{bandAware:r,position:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.fromEntries(Object.entries(e).map((e=>{var[i,a]=e;return[i,t[i].apply(a,{bandAware:r,position:n})]})))},isInRange:e=>Object.keys(e).every((r=>t[r].isInRange(e[r])))})};var JA=Gr({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=Ft(e).dots.findIndex((e=>e===t.payload));-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=Ft(e).areas.findIndex((e=>e===t.payload));-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=Ft(e).lines.findIndex((e=>e===t.payload));-1!==r&&e.lines.splice(r,1)}}}),{addDot:QA,removeDot:ej,addArea:tj,removeArea:rj,addLine:nj,removeLine:ij}=JA.actions,aj=JA.reducer,oj=(0,t.createContext)(void 0),lj=e=>{var{children:r}=e,[n]=(0,t.useState)("".concat(y("recharts"),"-clip")),i=NO();if(null==i)return null;var{x:a,y:o,width:l,height:c}=i;return t.createElement(oj.Provider,{value:n},t.createElement("defs",null,t.createElement("clipPath",{id:n},t.createElement("rect",{x:a,y:o,height:c,width:l}))),r)},cj=()=>(0,t.useContext)(oj);function sj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function uj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sj(Object(r),!0).forEach((function(t){fj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function fj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dj(){return dj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},dj.apply(null,arguments)}function pj(e){var r=Ue();return(0,t.useEffect)((()=>(r(nj(e)),()=>{r(ij(e))}))),null}function hj(e){var{x:r,y:i,segment:a,xAxisId:o,yAxisId:l,shape:c,className:s,ifOverflow:f}=e,d=Wi(),h=cj(),y=He((e=>Ah(e,o))),v=He((e=>Sh(e,l))),m=He((e=>Xy(e,"xAxis",o,d))),g=He((e=>Xy(e,"yAxis",l,d))),b=$i(),x=p(r),w=p(i);if(!h||!b||null==y||null==v||null==m||null==g)return null;var O=((e,t,r,n,i,a,o,l,c)=>{var{x:s,y:f,width:d,height:p}=i;if(r){var{y:h}=c,y=e.y.apply(h,{position:a});if(u(y))return null;if("discard"===c.ifOverflow&&!e.y.isInRange(y))return null;var v=[{x:s+d,y},{x:s,y}];return"left"===l?v.reverse():v}if(t){var{x:m}=c,g=e.x.apply(m,{position:a});if(u(g))return null;if("discard"===c.ifOverflow&&!e.x.isInRange(g))return null;var b=[{x:g,y:f+p},{x:g,y:f}];return"top"===o?b.reverse():b}if(n){var{segment:x}=c,w=x.map((t=>e.apply(t,{position:a})));return"discard"===c.ifOverflow&&w.some((t=>!e.isInRange(t)))?null:w}return null})(ZA({x:m,y:g}),x,w,a&&2===a.length,b,e.position,y.orientation,v.orientation,e);if(!O)return null;var[{x:P,y:E},{x:A,y:j}]=O,S=uj(uj({clipPath:"hidden"===f?"url(#".concat(h,")"):void 0},z(e,!0)),{},{x1:P,y1:E,x2:A,y2:j});return t.createElement(V,{className:n("recharts-reference-line",s)},((e,r)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement("line",dj({},r,{className:"recharts-reference-line-line"})))(c,S),Db.renderCallByParent(e,(e=>{var{x1:t,y1:r,x2:n,y2:i}=e;return YA({x:t,y:r},{x:n,y:i})})({x1:P,y1:E,x2:A,y2:j})))}function yj(e){return t.createElement(t.Fragment,null,t.createElement(pj,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x:e.x,y:e.y}),t.createElement(hj,e))}class vj extends t.Component{render(){return t.createElement(yj,this.props)}}function mj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function gj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mj(Object(r),!0).forEach((function(t){bj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function bj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xj(){return xj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xj.apply(null,arguments)}fj(vj,"displayName","ReferenceLine"),fj(vj,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function wj(e){var r=Ue();return(0,t.useEffect)((()=>(r(QA(e)),()=>{r(ej(e))}))),null}function Oj(e){var{x:r,y:i,r:a}=e,o=cj(),l=((e,t,r,n,i)=>{var a=p(e),o=p(t),l=Wi(),c=He((e=>Xy(e,"xAxis",r,l))),s=He((e=>Xy(e,"yAxis",n,l)));if(!a||!o||null==c||null==s)return null;var u=ZA({x:c,y:s}),f=u.apply({x:e,y:t},{bandAware:!0});return"discard"!==i||u.isInRange(f)?f:null})(r,i,e.xAxisId,e.yAxisId,e.ifOverflow);if(!l)return null;var{x:c,y:s}=l,{shape:u,className:f,ifOverflow:d}=e,h=gj(gj({clipPath:"hidden"===d?"url(#".concat(o,")"):void 0},z(e,!0)),{},{cx:c,cy:s});return t.createElement(V,{className:n("recharts-reference-dot",f)},((e,r)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Jb,xj({},r,{cx:r.cx,cy:r.cy,className:"recharts-reference-dot-dot"})))(u,h),Db.renderCallByParent(e,{x:c-a,y:s-a,width:2*a,height:2*a}))}function Pj(e){var{x:r,y:n,r:i,ifOverflow:a,yAxisId:o,xAxisId:l}=e;return t.createElement(t.Fragment,null,t.createElement(wj,{y:n,x:r,r:i,yAxisId:o,xAxisId:l,ifOverflow:a}),t.createElement(Oj,e))}class Ej extends t.Component{render(){return t.createElement(Pj,this.props)}}function Aj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Aj(Object(r),!0).forEach((function(t){Sj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Aj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Sj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kj(){return kj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kj.apply(null,arguments)}bj(Ej,"displayName","ReferenceDot"),bj(Ej,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});function Mj(e){var r=Ue();return(0,t.useEffect)((()=>(r(tj(e)),()=>{r(rj(e))}))),null}function Tj(e){var{x1:r,x2:i,y1:a,y2:o,className:l,shape:c,xAxisId:s,yAxisId:u}=e,f=cj(),d=Wi(),h=He((e=>Xy(e,"xAxis",s,d))),y=He((e=>Xy(e,"yAxis",u,d)));if(null==h||null==!y)return null;var v=p(r),m=p(i),g=p(a),b=p(o);if(!(v||m||g||b||c))return null;var x=((e,t,r,n,i,a,o)=>{var{x1:l,x2:c,y1:s,y2:u}=o;if(null==i||null==a)return null;var f=ZA({x:i,y:a}),d={x:e?f.x.apply(l,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(s,{position:"start"}):f.y.rangeMin},p={x:t?f.x.apply(c,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return"discard"!==o.ifOverflow||f.isInRange(d)&&f.isInRange(p)?YA(d,p):null})(v,m,g,b,h,y,e);if(!x&&!c)return null;var w="hidden"===e.ifOverflow?"url(#".concat(f,")"):void 0;return t.createElement(V,{className:n("recharts-reference-area",l)},((e,r)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Yl,kj({},r,{className:"recharts-reference-area-rect"})))(c,jj(jj({clipPath:w},z(e,!0)),x)),Db.renderCallByParent(e,x))}function Dj(e){return t.createElement(t.Fragment,null,t.createElement(Mj,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2}),t.createElement(Tj,e))}class Cj extends t.Component{render(){return t.createElement(Dj,this.props)}}function Ij(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Nj(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t){if(void 0!==r&&!0!==r(e[i]))return;n.push(e[i])}return n}function _j(e,t,r){return function(e){var{width:t,height:r}=e,n=function(e){return(e%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),i=n*Math.PI/180,a=Math.atan(r/t),o=i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i);return Math.abs(o)}({width:e.width+t.width,height:e.height+t.height},r)}function Lj(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function Rj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Kj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Rj(Object(r),!0).forEach((function(t){zj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function zj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Bj(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:c,interval:u,tickFormatter:f,unit:p,angle:h}=e;if(!a||!a.length||!i)return[];if(d(u)||Oo.isSsr)return null!==(n=function(e,t){return Nj(e,t+1)}(a,d(u)?u:0))&&void 0!==n?n:[];var y=[],v="top"===c||"bottom"===c?"width":"height",m=p&&"width"===v?Yg(p,{fontSize:t,letterSpacing:r}):{width:0,height:0},g=(e,n)=>{var i="function"==typeof f?f(e.value,n):e.value;return"width"===v?_j(Yg(i,{fontSize:t,letterSpacing:r}),m,h):Yg(i,{fontSize:t,letterSpacing:r})[v]},b=a.length>=2?s(a[1].coordinate-a[0].coordinate):1,x=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,b,v);return"equidistantPreserveStart"===u?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,s=0,u=1,f=l,d=function(){var t=null==n?void 0:n[s];if(void 0===t)return{v:Nj(n,u)};var a,o=s,d=()=>(void 0===a&&(a=r(t,o)),a),p=t.coordinate,h=0===s||Lj(e,p,d,f,c);h||(s=0,f=l,u+=1),h&&(f=p+e*(d()/2+i),s+=u)};u<=o.length;)if(a=d())return a.v;return[]}(b,x,g,a,l):(y="preserveStart"===u||"preserveStartEnd"===u?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:s}=t;if(a){var u=n[l-1],f=r(u,l-1),d=e*(u.coordinate+e*f/2-s);o[l-1]=u=Kj(Kj({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),Lj(e,u.tickCoord,(()=>f),c,s)&&(s=u.tickCoord-e*(f/2+i),o[l-1]=Kj(Kj({},u),{},{isShow:!0}))}for(var p=a?l-1:l,h=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var u=e*(a.coordinate-e*l()/2-c);o[t]=a=Kj(Kj({},a),{},{tickCoord:u<0?a.coordinate-u*e:a.coordinate})}else o[t]=a=Kj(Kj({},a),{},{tickCoord:a.coordinate});Lj(e,a.tickCoord,l,c,s)&&(c=a.tickCoord+e*(l()/2+i),o[t]=Kj(Kj({},a),{},{isShow:!0}))},y=0;y<p;y++)h(y);return o}(b,x,g,a,l,"preserveStartEnd"===u):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,s=function(t){var n,s=a[t],u=()=>(void 0===n&&(n=r(s,t)),n);if(t===o-1){var f=e*(s.coordinate+e*u()/2-c);a[t]=s=Kj(Kj({},s),{},{tickCoord:f>0?s.coordinate-f*e:s.coordinate})}else a[t]=s=Kj(Kj({},s),{},{tickCoord:s.coordinate});Lj(e,s.tickCoord,u,l,c)&&(c=s.tickCoord-e*(u()/2+i),a[t]=Kj(Kj({},s),{},{isShow:!0}))},u=o-1;u>=0;u--)s(u);return a}(b,x,g,a,l),y.filter((e=>e.isShow)))}Sj(Cj,"displayName","ReferenceArea"),Sj(Cj,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});var Fj=["viewBox"],Wj=["viewBox"];function Uj(){return Uj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Uj.apply(null,arguments)}function Xj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Vj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Xj(Object(r),!0).forEach((function(t){Hj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function $j(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Hj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class qj extends t.Component{constructor(e){super(e),this.tickRefs=t.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=$j(e,Fj),i=this.props,{viewBox:a}=i,o=$j(i,Wj);return!Ij(r,a)||!Ij(n,o)||!Ij(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:s,height:u,orientation:f,tickSize:p,mirror:h,tickMargin:y}=this.props,v=h?-1:1,m=e.tickSize||p,g=d(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=c+ +!h*u)-v*m)-v*y,a=g;break;case"left":n=i=e.coordinate,a=(t=(r=l+ +!h*s)-v*m)-v*y,o=g;break;case"right":n=i=e.coordinate,a=(t=(r=l+ +h*s)+v*m)+v*y,o=g;break;default:t=r=e.coordinate,o=(n=(i=c+ +h*u)+v*m)+v*y,a=g}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:r,width:i,height:a,orientation:o,mirror:c,axisLine:s}=this.props,u=Vj(Vj(Vj({},z(this.props,!1)),z(s,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var f=+("top"===o&&!c||"bottom"===o&&c);u=Vj(Vj({},u),{},{x1:e,y1:r+f*a,x2:e+i,y2:r+f*a})}else{var d=+("left"===o&&!c||"right"===o&&c);u=Vj(Vj({},u),{},{x1:e+d*i,y1:r,x2:e+d*i,y2:r+a})}return t.createElement("line",Uj({},u,{className:n("recharts-cartesian-axis-line",l()(s,"className"))}))}static renderTickItem(e,r,i){var a,o=n(r.className,"recharts-cartesian-axis-tick-value");if(t.isValidElement(e))a=t.cloneElement(e,Vj(Vj({},r),{},{className:o}));else if("function"==typeof e)a=e(Vj(Vj({},r),{},{className:o}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=n(l,e.className)),a=t.createElement(mb,Uj({},r,{className:l}),i)}return a}renderTicks(e,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:a,stroke:o,tick:c,tickFormatter:s,unit:u,padding:f}=this.props,d=Bj(Vj(Vj({},this.props),{},{ticks:i}),e,r),p=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=C(this.props),v=z(c,!1),m=Vj(Vj({},y),{},{fill:"none"},z(a,!1)),g=d.map(((e,r)=>{var{line:i,tick:g}=this.getTickLineCoord(e),b=Vj(Vj(Vj(Vj({textAnchor:p,verticalAnchor:h},y),{},{stroke:"none",fill:o},v),g),{},{index:r,payload:e,visibleTicksCount:d.length,tickFormatter:s,padding:f});return t.createElement(V,Uj({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},M(this.props,e,r)),a&&t.createElement("line",Uj({},m,i,{className:n("recharts-cartesian-axis-tick-line",l()(a,"className"))})),c&&qj.renderTickItem(c,b,"".concat("function"==typeof s?s(e.value,r):e.value).concat(u||"")))}));return g.length>0?t.createElement("g",{className:"recharts-cartesian-axis-ticks"},g):null}render(){var{axisLine:e,width:r,height:i,className:a,hide:o}=this.props;if(o)return null;var{ticks:l}=this.props;return null!=r&&r<=0||null!=i&&i<=0?null:t.createElement(V,{className:n("recharts-cartesian-axis",a),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;n===this.state.fontSize&&i===this.state.letterSpacing||this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),Db.renderCallByParent(this.props))}}Hj(qj,"displayName","CartesianAxis"),Hj(qj,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var Yj=["x1","y1","x2","y2","key"],Gj=["offset"],Zj=["xAxisId","yAxisId"],Jj=["xAxisId","yAxisId"];function Qj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function eS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Qj(Object(r),!0).forEach((function(t){tS(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function tS(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rS(){return rS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rS.apply(null,arguments)}function nS(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var iS=e=>{var{fill:r}=e;if(!r||"none"===r)return null;var{fillOpacity:n,x:i,y:a,width:o,height:l,ry:c}=e;return t.createElement("rect",{x:i,y:a,ry:c,width:o,height:l,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function aS(e,r){var n;if(t.isValidElement(e))n=t.cloneElement(e,r);else if("function"==typeof e)n=e(r);else{var{x1:i,y1:a,x2:o,y2:l,key:c}=r,s=C(nS(r,Yj)),{offset:u}=s,f=nS(s,Gj);n=t.createElement("line",rS({},f,{x1:i,y1:a,x2:o,y2:l,fill:"none",key:c}))}return n}function oS(e){var{x:r,width:n,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=nS(e,Zj),s=a.map(((e,t)=>{var a=eS(eS({},c),{},{x1:r,y1:e,x2:r+n,y2:e,key:"line-".concat(t),index:t});return aS(i,a)}));return t.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function lS(e){var{y:r,height:n,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=nS(e,Jj),s=a.map(((e,t)=>{var a=eS(eS({},c),{},{x1:e,y1:r,x2:e,y2:r+n,key:"line-".concat(t),index:t});return aS(i,a)}));return t.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function cS(e){var{horizontalFill:r,fillOpacity:n,x:i,y:a,width:o,height:l,horizontalPoints:c,horizontal:s=!0}=e;if(!s||!r||!r.length)return null;var u=c.map((e=>Math.round(e+a-a))).sort(((e,t)=>e-t));a!==u[0]&&u.unshift(0);var f=u.map(((e,c)=>{var s=!u[c+1]?a+l-e:u[c+1]-e;if(s<=0)return null;var f=c%r.length;return t.createElement("rect",{key:"react-".concat(c),y:e,x:i,height:s,width:o,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})}));return t.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function sS(e){var{vertical:r=!0,verticalFill:n,fillOpacity:i,x:a,y:o,width:l,height:c,verticalPoints:s}=e;if(!r||!n||!n.length)return null;var u=s.map((e=>Math.round(e+a-a))).sort(((e,t)=>e-t));a!==u[0]&&u.unshift(0);var f=u.map(((e,r)=>{var s=!u[r+1]?a+l-e:u[r+1]-e;if(s<=0)return null;var f=r%n.length;return t.createElement("rect",{key:"react-".concat(r),x:e,y:o,width:s,height:c,stroke:"none",fill:n[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return t.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var uS=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return ui(Bj(eS(eS(eS({},qj.defaultProps),r),{},{ticks:fi(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},fS=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return ui(Bj(eS(eS(eS({},qj.defaultProps),r),{},{ticks:fi(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},dS={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function pS(e){var r=Yi(),n=Gi(),i=qi(),a=eS(eS({},pl(e,dS)),{},{x:d(e.x)?e.x:i.left,y:d(e.y)?e.y:i.top,width:d(e.width)?e.width:i.width,height:d(e.height)?e.height:i.height}),{xAxisId:o,yAxisId:l,x:c,y:s,width:u,height:f,syncWithTicks:p,horizontalValues:h,verticalValues:y}=a,v=Wi(),m=He((e=>ov(e,"xAxis",o,v))),g=He((e=>ov(e,"yAxis",l,v)));if(!d(u)||u<=0||!d(f)||f<=0||!d(c)||c!==+c||!d(s)||s!==+s)return null;var b=a.verticalCoordinatesGenerator||uS,x=a.horizontalCoordinatesGenerator||fS,{horizontalPoints:w,verticalPoints:O}=a;if(!(w&&w.length||"function"!=typeof x)){var P=h&&h.length,E=x({yAxis:g?eS(eS({},g),{},{ticks:P?h:g.ticks}):void 0,width:r,height:n,offset:i},!!P||p);Ng(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof E,"]")),Array.isArray(E)&&(w=E)}if(!(O&&O.length||"function"!=typeof b)){var A=y&&y.length,j=b({xAxis:m?eS(eS({},m),{},{ticks:A?y:m.ticks}):void 0,width:r,height:n,offset:i},!!A||p);Ng(Array.isArray(j),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(O=j)}return t.createElement("g",{className:"recharts-cartesian-grid"},t.createElement(iS,{fill:a.fill,fillOpacity:a.fillOpacity,x:a.x,y:a.y,width:a.width,height:a.height,ry:a.ry}),t.createElement(cS,rS({},a,{horizontalPoints:w})),t.createElement(sS,rS({},a,{verticalPoints:O})),t.createElement(oS,rS({},a,{offset:i,horizontalPoints:w,xAxis:m,yAxis:g})),t.createElement(lS,rS({},a,{offset:i,verticalPoints:O,xAxis:m,yAxis:g})))}pS.displayName="CartesianGrid";var hS=(e,t,r,n)=>fv(e,"xAxis",t,n),yS=(e,t,r,n)=>uv(e,"xAxis",t,n),vS=(e,t,r,n)=>fv(e,"yAxis",r,n),mS=(e,t,r,n)=>uv(e,"yAxis",r,n),gS=et([Ji,hS,vS,yS,mS],((e,t,r,n,i)=>si(e,"xAxis")?Pi(t,n,!1):Pi(r,i,!1)));function bS(e){return"line"===e.type}var xS=et([Nh,(e,t,r,n,i)=>i],((e,t)=>e.filter(bS).find((e=>e.id===t)))),wS=et([Ji,hS,vS,yS,mS,xS,gS,vp],((e,t,r,n,i,a,o,l)=>{var{chartData:c,dataStartIndex:s,dataEndIndex:u}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var f,{dataKey:d,data:p}=a;if(null!=(f=null!=p&&p.length>0?p:null==c?void 0:c.slice(s,u+1)))return function(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:c}=e;return c.map(((e,c)=>{var s=ci(e,o);if("horizontal"===t)return{x:mi({axis:r,ticks:i,bandSize:l,entry:e,index:c}),y:O(s)?null:n.scale(s),value:s,payload:e};var u=O(s)?null:r.scale(s),f=mi({axis:n,ticks:a,bandSize:l,entry:e,index:c});return null==u||null==f?null:{x:u,y:f,value:s,payload:e}})).filter(Boolean)}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:o,displayedData:f})}})),OS=["id"],PS=["type","layout","connectNulls","needClip"],ES=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId","id"];function AS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?AS(Object(r),!0).forEach((function(t){SS(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):AS(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function SS(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kS(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function MS(){return MS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},MS.apply(null,arguments)}var TS=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:Ai(r,t),payload:e}]};function DS(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:Ai(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:c}}}var CS=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function IS(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}function NS(e){var{clipPathId:r,points:i,props:a}=e,{dot:o,dataKey:l,needClip:c}=a;if(!function(e,t){return null!=e&&(!!t||1===e.length)}(i,o))return null;var{id:s}=a,u=kS(a,OS),f=K(o),d=C(u),p=z(o,!0),h=i.map(((e,r)=>{var a=jS(jS(jS({key:"dot-".concat(r),r:3},d),p),{},{index:r,cx:e.x,cy:e.y,dataKey:l,value:e.value,payload:e.payload,points:i});return function(e,r){var i;if(t.isValidElement(e))i=t.cloneElement(e,r);else if("function"==typeof e)i=e(r);else{var a=n("recharts-line-dot","boolean"!=typeof e?e.className:"");i=t.createElement(Jb,MS({},r,{className:a}))}return i}(o,a)})),y={clipPath:c?"url(#clipPath-".concat(f?"":"dots-").concat(r,")"):void 0};return t.createElement(V,MS({className:"recharts-line-dots",key:"dots"},y),h)}function _S(e){var{clipPathId:r,pathRef:n,points:i,strokeDasharray:a,props:o,showLabels:l}=e,{type:c,layout:s,connectNulls:u,needClip:f}=o,d=kS(o,PS),p=jS(jS({},z(d,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:f?"url(#clipPath-".concat(r,")"):void 0,points:i,type:c,layout:s,connectNulls:u,strokeDasharray:null!=a?a:o.strokeDasharray});return t.createElement(t.Fragment,null,(null==i?void 0:i.length)>1&&t.createElement(il,MS({},p,{pathRef:n})),t.createElement(NS,{points:i,clipPathId:r,props:o}),l&&Ub.renderCallByParent(o,i))}function LS(e){var{clipPathId:r,props:n,pathRef:i,previousPointsRef:a,longestAnimatedLengthRef:o}=e,{points:l,strokeDasharray:c,isAnimationActive:s,animationBegin:u,animationDuration:f,animationEasing:d,animateNewValues:p,width:h,height:y,onAnimationEnd:v,onAnimationStart:m}=n,g=a.current,x=Uw(n,"recharts-line-"),[w,O]=(0,t.useState)(!1),P=(0,t.useCallback)((()=>{"function"==typeof v&&v(),O(!1)}),[v]),E=(0,t.useCallback)((()=>{"function"==typeof m&&m(),O(!0)}),[m]),A=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(i.current),j=o.current;return t.createElement(oO,{begin:u,duration:f,isActive:s,easing:d,onAnimationEnd:P,onAnimationStart:E,key:x},(e=>{var s,u=b(j,A+j,e),f=Math.min(u,A);if(c){var d="".concat(c).split(/[,\s]+/gim).map((e=>parseFloat(e)));s=((e,t,r)=>{var n=r.reduce(((e,t)=>e+t));if(!n)return CS(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],c=0,s=0;c<r.length;s+=r[c],++c)if(s+r[c]>a){l=[...r.slice(0,c),a-s];break}var u=l.length%2==0?[0,o]:[o];return[...IS(r,i),...l,...u].map((e=>"".concat(e,"px"))).join(", ")})(f,A,d)}else s=CS(A,f);if(g){var v=g.length/l.length,m=1===e?l:l.map(((t,r)=>{var n=Math.floor(r*v);if(g[n]){var i=g[n];return jS(jS({},t),{},{x:b(i.x,t.x,e),y:b(i.y,t.y,e)})}return jS(jS({},t),{},p?{x:b(2*h,t.x,e),y:b(y/2,t.y,e)}:{x:t.x,y:t.y})}));return a.current=m,t.createElement(_S,{props:n,points:m,clipPathId:r,pathRef:i,showLabels:!w,strokeDasharray:s})}return e>0&&A>0&&(a.current=l,o.current=f),t.createElement(_S,{props:n,points:l,clipPathId:r,pathRef:i,showLabels:!w,strokeDasharray:s})}))}function RS(e){var{clipPathId:r,props:n}=e,{points:i,isAnimationActive:a}=n,o=(0,t.useRef)(null),l=(0,t.useRef)(0),c=(0,t.useRef)(null),s=o.current;return a&&i&&i.length&&s!==i?t.createElement(LS,{props:n,clipPathId:r,previousPointsRef:o,longestAnimatedLengthRef:l,pathRef:c}):t.createElement(_S,{props:n,points:i,clipPathId:r,pathRef:c,showLabels:!0})}var KS=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:ci(e.payload,t)});class zS extends t.Component{render(){var e,{hide:r,dot:i,points:a,className:o,xAxisId:l,yAxisId:c,top:s,left:u,width:f,height:d,id:p,needClip:h}=this.props;if(r)return null;var y=n("recharts-line",o),v=p,{r:m=3,strokeWidth:g=2}=null!==(e=z(i,!1))&&void 0!==e?e:{r:3,strokeWidth:2},b=K(i),x=2*m+g;return t.createElement(t.Fragment,null,t.createElement(V,{className:y},h&&t.createElement("defs",null,t.createElement(WP,{clipPathId:v,xAxisId:l,yAxisId:c}),!b&&t.createElement("clipPath",{id:"clipPath-dots-".concat(v)},t.createElement("rect",{x:u-x/2,y:s-x/2,width:f+x,height:d+x}))),t.createElement(RS,{props:this.props,clipPathId:v}),t.createElement(zP,{xAxisId:l,yAxisId:c,data:a,dataPointFormatter:KS,errorBarOffset:0},this.props.children)),t.createElement(zO,{activeDot:this.props.activeDot,points:a,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var BS={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!Oo.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function FS(e){var r=pl(e,BS),{activeDot:n,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:c,dot:s,hide:u,isAnimationActive:f,label:d,legendType:p,xAxisId:h,yAxisId:y,id:v}=r,m=kS(r,ES),{needClip:g}=FP(h,y),b=NO(),x=Qi(),w=Wi(),O=He((e=>wS(e,h,y,w,v)));if("horizontal"!==x&&"vertical"!==x||null==O||null==b)return null;var{height:P,width:E,x:A,y:j}=b;return t.createElement(zS,MS({},m,{id:v,connectNulls:c,dot:s,activeDot:n,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,isAnimationActive:f,hide:u,label:d,legendType:p,xAxisId:h,yAxisId:y,points:O,layout:x,height:P,width:E,left:A,top:j,needClip:g}))}function WS(e){var r=pl(e,BS),n=Wi();return t.createElement($w,{id:r.id,type:"line"},(e=>t.createElement(t.Fragment,null,t.createElement(Fw,{legendPayload:TS(r)}),t.createElement(Kw,{fn:DS,args:r}),t.createElement(eO,{type:"line",id:e,data:r.data,xAxisId:r.xAxisId,yAxisId:r.yAxisId,zAxisId:0,dataKey:r.dataKey,hide:r.hide,isPanorama:n}),t.createElement(FS,MS({},r,{id:e})))))}WS.displayName="Line";var US=(e,t,r,n)=>fv(e,"xAxis",t,n),XS=(e,t,r,n)=>uv(e,"xAxis",t,n),VS=(e,t,r,n)=>fv(e,"yAxis",r,n),$S=(e,t,r,n)=>uv(e,"yAxis",r,n),HS=et([Ji,US,VS,XS,$S],((e,t,r,n,i)=>si(e,"xAxis")?Pi(t,n,!1):Pi(r,i,!1))),qS=et([Nh,(e,t,r,n,i)=>i],((e,t)=>e.filter((e=>"area"===e.type)).find((e=>e.id===t)))),YS=et([Ji,US,VS,XS,$S,(e,t,r,n,i)=>{var a,o=qS(e,t,r,n,i);if(null!=o){var l,c=Ji(e);if(null!=(l=si(c,"xAxis")?Jh(e,"yAxis",r,n):Jh(e,"xAxis",t,n))){var{stackId:s}=o,u=hh(o);if(null!=s&&null!=u){var f=null===(a=l[s])||void 0===a?void 0:a.stackedData;return null==f?void 0:f.find((e=>e.key===u))}}}},vp,HS,qS],((e,t,r,n,i,a,o,l,c)=>{var{chartData:s,dataStartIndex:u,dataEndIndex:f}=o;if(null!=c&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=l){var d,{data:p}=c;if(null!=(d=p&&p.length>0?p:null==s?void 0:s.slice(u,f+1))){return function(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:i},stackedData:a,layout:o,chartBaseValue:l,xAxis:c,yAxis:s,displayedData:u,dataStartIndex:f,xAxisTicks:d,yAxisTicks:p,bandSize:h}=e,y=a&&a.length,v=vk(o,l,n,c,s),m="horizontal"===o,g=!1,b=u.map(((e,t)=>{var n;y?n=a[f+t]:(n=ci(e,i),Array.isArray(n)?g=!0:n=[v,n]);var o=null==n[1]||y&&!r&&null==ci(e,i);return m?{x:mi({axis:c,ticks:d,bandSize:h,entry:e,index:t}),y:o?null:s.scale(n[1]),value:n,payload:e}:{x:o?null:c.scale(n[1]),y:mi({axis:s,ticks:p,bandSize:h,entry:e,index:t}),value:n,payload:e}}));t=y||g?b.map((e=>{var t=Array.isArray(e.value)?e.value[0]:null;return m?{x:e.x,y:null!=t&&null!=e.y?s.scale(t):null,payload:e.payload}:{x:null!=t?c.scale(t):null,y:e.y,payload:e.payload}})):m?s.scale(v):c.scale(v);return{points:b,baseLine:t,isRange:g}}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataStartIndex:u,areaSettings:c,stackedData:a,displayedData:d,chartBaseValue:undefined,bandSize:l})}}})),GS=["id"],ZS=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function JS(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function QS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ek(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?QS(Object(r),!0).forEach((function(t){tk(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):QS(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function tk(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rk(){return rk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rk.apply(null,arguments)}function nk(e,t){return e&&"none"!==e?e:t}var ik=e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:nk(n,i),value:Ai(r,t),payload:e}]};function ak(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:Ai(o,t),hide:l,type:e.tooltipType,color:nk(n,a),unit:c}}}function ok(e){var{clipPathId:r,points:i,props:a}=e,{needClip:o,dot:l,dataKey:c}=a;if(!function(e,t){return null!=e&&(!!t||1===e.length)}(i,l))return null;var s=K(l),u=C(a),f=z(l,!0),d=i.map(((e,r)=>{var a=ek(ek(ek({key:"dot-".concat(r),r:3},u),f),{},{index:r,cx:e.x,cy:e.y,dataKey:c,value:e.value,payload:e.payload,points:i});return((e,r)=>{var i;if(t.isValidElement(e))i=t.cloneElement(e,r);else if("function"==typeof e)i=e(r);else{var a=n("recharts-area-dot","boolean"!=typeof e?e.className:"");i=t.createElement(Jb,rk({},r,{className:a}))}return i})(l,a)})),p={clipPath:o?"url(#clipPath-".concat(s?"":"dots-").concat(r,")"):void 0};return t.createElement(V,rk({className:"recharts-area-dots"},p),d)}function lk(e){var{points:r,baseLine:n,needClip:i,clipPathId:a,props:o,showLabels:l}=e,{layout:c,type:s,stroke:u,connectNulls:f,isRange:d}=o,{id:p}=o,h=JS(o,GS),y=C(h);return t.createElement(t.Fragment,null,(null==r?void 0:r.length)>1&&t.createElement(V,{clipPath:i?"url(#clipPath-".concat(a,")"):void 0},t.createElement(il,rk({},y,{id:p,points:r,connectNulls:f,type:s,baseLine:n,layout:c,stroke:"none",className:"recharts-area-area"})),"none"!==u&&t.createElement(il,rk({},y,{className:"recharts-area-curve",layout:c,type:s,connectNulls:f,fill:"none",points:r})),"none"!==u&&d&&t.createElement(il,rk({},y,{className:"recharts-area-curve",layout:c,type:s,connectNulls:f,fill:"none",points:n}))),t.createElement(ok,{points:r,props:h,clipPathId:a}),l&&Ub.renderCallByParent(h,r))}function ck(e){var{alpha:r,baseLine:n,points:i,strokeWidth:a}=e,o=i[0].y,l=i[i.length-1].y;if(!Ho(o)||!Ho(l))return null;var c=r*Math.abs(o-l),s=Math.max(...i.map((e=>e.x||0)));return d(n)?s=Math.max(n,s):n&&Array.isArray(n)&&n.length&&(s=Math.max(...n.map((e=>e.x||0)),s)),d(s)?t.createElement("rect",{x:0,y:o<l?o:o-c,width:s+(a?parseInt("".concat(a),10):1),height:Math.floor(c)}):null}function sk(e){var{alpha:r,baseLine:n,points:i,strokeWidth:a}=e,o=i[0].x,l=i[i.length-1].x;if(!Ho(o)||!Ho(l))return null;var c=r*Math.abs(o-l),s=Math.max(...i.map((e=>e.y||0)));return d(n)?s=Math.max(n,s):n&&Array.isArray(n)&&n.length&&(s=Math.max(...n.map((e=>e.y||0)),s)),d(s)?t.createElement("rect",{x:o<l?o:o-c,y:0,width:c,height:Math.floor(s+(a?parseInt("".concat(a),10):1))}):null}function uk(e){var{alpha:r,layout:n,points:i,baseLine:a,strokeWidth:o}=e;return"vertical"===n?t.createElement(ck,{alpha:r,points:i,baseLine:a,strokeWidth:o}):t.createElement(sk,{alpha:r,points:i,baseLine:a,strokeWidth:o})}function fk(e){var{needClip:r,clipPathId:n,props:i,previousPointsRef:a,previousBaselineRef:o}=e,{points:l,baseLine:c,isAnimationActive:s,animationBegin:f,animationDuration:p,animationEasing:h,onAnimationStart:y,onAnimationEnd:v}=i,m=Uw(i,"recharts-area-"),[g,x]=(0,t.useState)(!0),w=(0,t.useCallback)((()=>{"function"==typeof v&&v(),x(!1)}),[v]),P=(0,t.useCallback)((()=>{"function"==typeof y&&y(),x(!0)}),[y]),E=a.current,A=o.current;return t.createElement(oO,{begin:f,duration:p,isActive:s,easing:h,onAnimationEnd:w,onAnimationStart:P,key:m},(e=>{if(E){var s,f=E.length/l.length,p=1===e?l:l.map(((t,r)=>{var n=Math.floor(r*f);if(E[n]){var i=E[n];return ek(ek({},t),{},{x:b(i.x,t.x,e),y:b(i.y,t.y,e)})}return t}));return s=d(c)?b(A,c,e):O(c)||u(c)?b(A,0,e):c.map(((t,r)=>{var n=Math.floor(r*f);if(Array.isArray(A)&&A[n]){var i=A[n];return ek(ek({},t),{},{x:b(i.x,t.x,e),y:b(i.y,t.y,e)})}return t})),e>0&&(a.current=p,o.current=s),t.createElement(lk,{points:p,baseLine:s,needClip:r,clipPathId:n,props:i,showLabels:!g})}return e>0&&(a.current=l,o.current=c),t.createElement(V,null,t.createElement("defs",null,t.createElement("clipPath",{id:"animationClipPath-".concat(n)},t.createElement(uk,{alpha:e,points:l,baseLine:c,layout:i.layout,strokeWidth:i.strokeWidth}))),t.createElement(V,{clipPath:"url(#animationClipPath-".concat(n,")")},t.createElement(lk,{points:l,baseLine:c,needClip:r,clipPathId:n,props:i,showLabels:!0})))}))}function dk(e){var{needClip:r,clipPathId:n,props:i}=e,{points:a,baseLine:o,isAnimationActive:l}=i,c=(0,t.useRef)(null),s=(0,t.useRef)(),u=c.current,f=s.current;return l&&a&&a.length&&(u!==a||f!==o)?t.createElement(fk,{needClip:r,clipPathId:n,props:i,previousPointsRef:c,previousBaselineRef:s}):t.createElement(lk,{points:a,baseLine:o,needClip:r,clipPathId:n,props:i,showLabels:!0})}class pk extends t.PureComponent{render(){var e,{hide:r,dot:i,points:a,className:o,top:l,left:c,needClip:s,xAxisId:u,yAxisId:f,width:d,height:p,id:h,baseLine:y}=this.props;if(r)return null;var v=n("recharts-area",o),m=h,{r:g=3,strokeWidth:b=2}=null!==(e=z(i,!1))&&void 0!==e?e:{r:3,strokeWidth:2},x=K(i),w=2*g+b;return t.createElement(t.Fragment,null,t.createElement(V,{className:v},s&&t.createElement("defs",null,t.createElement(WP,{clipPathId:m,xAxisId:u,yAxisId:f}),!x&&t.createElement("clipPath",{id:"clipPath-dots-".concat(m)},t.createElement("rect",{x:c-w/2,y:l-w/2,width:d+w,height:p+w}))),t.createElement(dk,{needClip:s,clipPathId:m,props:this.props})),t.createElement(zO,{points:a,mainColor:nk(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(y)&&t.createElement(zO,{points:y,mainColor:nk(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var hk={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!Oo.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function yk(e){var r,n=pl(e,hk),{activeDot:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:c,dot:s,fill:u,fillOpacity:f,hide:d,isAnimationActive:p,legendType:h,stroke:y,xAxisId:v,yAxisId:m}=n,g=JS(n,ZS),b=Qi(),x=Wm(),{needClip:w}=FP(v,m),O=Wi(),{points:P,isRange:E,baseLine:A}=null!==(r=He((t=>YS(t,v,m,O,e.id))))&&void 0!==r?r:{},j=NO();if("horizontal"!==b&&"vertical"!==b||null==j)return null;if("AreaChart"!==x&&"ComposedChart"!==x)return null;var{height:S,width:k,x:M,y:T}=j;return P&&P.length?t.createElement(pk,rk({},g,{activeDot:i,animationBegin:a,animationDuration:o,animationEasing:l,baseLine:A,connectNulls:c,dot:s,fill:u,fillOpacity:f,height:S,hide:d,layout:b,isAnimationActive:p,isRange:E,legendType:h,needClip:w,points:P,stroke:y,width:k,left:M,top:T,xAxisId:v,yAxisId:m})):null}var vk=(e,t,r,n,i)=>{var a=null!=r?r:t;if(d(a))return a;var o="horizontal"===e?i:n,l=o.scale.domain();if("number"===o.type){var c=Math.max(l[0],l[1]),s=Math.min(l[0],l[1]);return"dataMin"===a?s:"dataMax"===a||c<0?c:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===a?l[0]:"dataMax"===a?l[1]:l[0]};function mk(e){var r=pl(e,hk),n=Wi();return t.createElement($w,{id:r.id,type:"area"},(e=>t.createElement(t.Fragment,null,t.createElement(Fw,{legendPayload:ik(r)}),t.createElement(Kw,{fn:ak,args:r}),t.createElement(eO,{type:"area",id:e,data:r.data,dataKey:r.dataKey,xAxisId:r.xAxisId,yAxisId:r.yAxisId,zAxisId:0,stackId:vi(r.stackId),hide:r.hide,barSize:void 0,baseValue:r.baseValue,isPanorama:n,connectNulls:r.connectNulls}),t.createElement(yk,rk({},r,{id:e})))))}function gk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function bk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gk(Object(r),!0).forEach((function(t){xk(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gk(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function xk(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}mk.displayName="Area";var wk=Gr({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=bk(bk({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:Ok,removeXAxis:Pk,addYAxis:Ek,removeYAxis:Ak,addZAxis:jk,removeZAxis:Sk,updateYAxisWidth:kk}=wk.actions,Mk=wk.reducer;function Tk(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Dk(e){var r=Ue();return(0,t.useEffect)((()=>(r(jk(e)),()=>{r(Sk(e))})),[e,r]),null}class Ck extends t.Component{render(){return t.createElement(Dk,{domain:this.props.domain,id:this.props.zAxisId,dataKey:this.props.dataKey,name:this.props.name,unit:this.props.unit,range:this.props.range,scale:this.props.scale,type:this.props.type,allowDuplicatedCategory:kh.allowDuplicatedCategory,allowDataOverflow:kh.allowDataOverflow,reversed:kh.reversed,includeHidden:kh.includeHidden})}}Tk(Ck,"displayName","ZAxis"),Tk(Ck,"defaultProps",{zAxisId:0,range:kh.range,scale:kh.scale,type:kh.type});var Ik=["option","isActive"];function Nk(){return Nk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Nk.apply(null,arguments)}function _k(e){var{option:r,isActive:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ik);return"string"==typeof r?t.createElement(Nw,Nk({option:t.createElement(De,Nk({type:r},i)),isActive:n,shapeType:"symbols"},i)):t.createElement(Nw,Nk({option:r,isActive:n,shapeType:"symbols"},i))}var Lk=et([Nh,(e,t,r,n,i)=>i],((e,t)=>e.filter((e=>"scatter"===e.type)).find((e=>e.id===t)))),Rk=et([(e,t,r,n,i,a,o)=>vp(e,0,0,o),(e,t,r,n,i,a,o)=>fv(e,"xAxis",t,o),(e,t,r,n,i,a,o)=>uv(e,"xAxis",t,o),(e,t,r,n,i,a,o)=>fv(e,"yAxis",r,o),(e,t,r,n,i,a,o)=>uv(e,"yAxis",r,o),(e,t,r,n)=>pv(e,"zAxis",n,!1),Lk,(e,t,r,n,i,a)=>a],((e,t,r,n,i,a,o,l)=>{var c,{chartData:s,dataStartIndex:u,dataEndIndex:f}=e;if(null!=o&&(null!=(c=null!=(null==o?void 0:o.data)&&o.data.length>0?o.data:null==s?void 0:s.slice(u,f+1))&&null!=t&&null!=n&&null!=r&&null!=i&&0!==(null==r?void 0:r.length)&&0!==(null==i?void 0:i.length)))return function(e){var{displayedData:t,xAxis:r,yAxis:n,zAxis:i,scatterSettings:a,xAxisTicks:o,yAxisTicks:l,cells:c}=e,s=O(r.dataKey)?a.dataKey:r.dataKey,u=O(n.dataKey)?a.dataKey:n.dataKey,f=i&&i.dataKey,d=i?i.range:Ck.defaultProps.range,p=d&&d[0],h=r.scale.bandwidth?r.scale.bandwidth():0,y=n.scale.bandwidth?n.scale.bandwidth():0;return t.map(((e,t)=>{var d=ci(e,s),v=ci(e,u),m=!O(f)&&ci(e,f)||"-",g=[{name:O(r.dataKey)?a.name:r.name||r.dataKey,unit:r.unit||"",value:d,payload:e,dataKey:s,type:a.tooltipType},{name:O(n.dataKey)?a.name:n.name||n.dataKey,unit:n.unit||"",value:v,payload:e,dataKey:u,type:a.tooltipType}];"-"!==m&&g.push({name:i.name||i.dataKey,unit:i.unit||"",value:m,payload:e,dataKey:f,type:a.tooltipType});var b=mi({axis:r,ticks:o,bandSize:h,entry:e,index:t,dataKey:s}),x=mi({axis:n,ticks:l,bandSize:y,entry:e,index:t,dataKey:u}),w="-"!==m?i.scale(m):p,P=Math.sqrt(Math.max(w,0)/Math.PI);return Xk(Xk({},e),{},{cx:b,cy:x,x:b-P,y:x-P,width:2*P,height:2*P,size:w,node:{x:d,y:v,z:m},tooltipPayload:g,tooltipPosition:{x:b,y:x},payload:e},c&&c[t]&&c[t].props)}))}({displayedData:c,xAxis:t,yAxis:n,zAxis:a,scatterSettings:o,xAxisTicks:r,yAxisTicks:i,cells:l})})),Kk=["onMouseEnter","onClick","onMouseLeave"],zk=["id"],Bk=["animationBegin","animationDuration","animationEasing","hide","isAnimationActive","legendType","lineJointType","lineType","shape","xAxisId","yAxisId","zAxisId"];function Fk(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Wk(){return Wk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wk.apply(null,arguments)}function Uk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Xk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Uk(Object(r),!0).forEach((function(t){Vk(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uk(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Vk(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var $k=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:Ai(r,t),payload:e}]};function Hk(e){var{points:r,props:n}=e,{line:i,lineType:a,lineJointType:o}=n;if(!i)return null;var l,c,s=C(n),u=z(i,!1);if("joint"===a)l=r.map((e=>({x:e.cx,y:e.cy})));else if("fitting"===a){var{xmin:f,xmax:d,a:p,b:h}=w(r),y=e=>p*e+h;l=[{x:f,y:y(f)},{x:d,y:y(d)}]}var v=Xk(Xk(Xk({},s),{},{fill:"none",stroke:s&&s.fill},u),{},{points:l});return c=t.isValidElement(i)?t.cloneElement(i,v):"function"==typeof i?i(v):t.createElement(il,Wk({},v,{type:o})),t.createElement(V,{className:"recharts-scatter-line",key:"recharts-scatter-line"},c)}function qk(e){var{points:r,showLabels:n,allOtherScatterProps:i}=e,{shape:a,activeShape:o,dataKey:l}=i,c=He(Mm),{onMouseEnter:s,onClick:u,onMouseLeave:f}=i,d=Fk(i,Kk),p=_w(s,i.dataKey),h=Lw(f),y=Rw(u,i.dataKey);if(null==r)return null;var{id:v}=i,m=Fk(i,zk),g=C(m);return t.createElement(t.Fragment,null,t.createElement(Hk,{points:r,props:m}),r.map(((e,r)=>{var n=o&&c===String(r),i=n?o:a,s=Xk(Xk(Xk({key:"symbol-".concat(r)},g),e),{},{[Ii]:r,[Ni]:String(l)});return t.createElement(V,Wk({className:"recharts-scatter-symbol"},M(d,e,r),{onMouseEnter:p(e,r),onMouseLeave:h(e,r),onClick:y(e,r),key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(r)}),t.createElement(_k,Wk({option:i,isActive:n},s)))})),n&&Ub.renderCallByParent(m,r))}function Yk(e){var{previousPointsRef:r,props:n}=e,{points:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c}=n,s=r.current,u=Uw(n,"recharts-scatter-"),[f,d]=(0,t.useState)(!1),p=(0,t.useCallback)((()=>{d(!1)}),[]),h=(0,t.useCallback)((()=>{d(!0)}),[]);return t.createElement(oO,{begin:o,duration:l,isActive:a,easing:c,onAnimationEnd:p,onAnimationStart:h,key:u},(e=>{var a=1===e?i:i.map(((t,r)=>{var n=s&&s[r];if(n){var i=g(n.cx,t.cx),a=g(n.cy,t.cy),o=g(n.size,t.size);return Xk(Xk({},t),{},{cx:i(e),cy:a(e),size:o(e)})}var l=g(0,t.size);return Xk(Xk({},t),{},{size:l(e)})}));return e>0&&(r.current=a),t.createElement(V,null,t.createElement(qk,{points:a,allOtherScatterProps:n,showLabels:!f}))}))}function Gk(e){var{points:r,isAnimationActive:n}=e,i=(0,t.useRef)(null),a=i.current;return n&&r&&r.length&&(!a||a!==r)?t.createElement(Yk,{props:e,previousPointsRef:i}):t.createElement(qk,{points:r,allOtherScatterProps:e,showLabels:!0})}function Zk(e){var{dataKey:t,points:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,tooltipType:c}=e;return{dataDefinedOnItem:null==r?void 0:r.map((e=>e.tooltipPayload)),positions:null==r?void 0:r.map((e=>e.tooltipPosition)),settings:{stroke:n,strokeWidth:i,fill:a,nameKey:void 0,dataKey:t,name:Ai(o,t),hide:l,type:c,color:a,unit:""}}}var Jk=(e,t,r)=>({x:e.cx,y:e.cy,value:"x"===r?+e.node.x:+e.node.y,errorVal:ci(e,t)});function Qk(e){var{hide:r,points:i,className:a,needClip:o,xAxisId:l,yAxisId:c,id:s,children:u}=e;if(r)return null;var f=n("recharts-scatter",a),d=s;return t.createElement(V,{className:f,clipPath:o?"url(#clipPath-".concat(d,")"):null,id:s},o&&t.createElement("defs",null,t.createElement(WP,{clipPathId:d,xAxisId:l,yAxisId:c})),t.createElement(zP,{xAxisId:l,yAxisId:c,data:i,dataPointFormatter:Jk,errorBarOffset:0},u),t.createElement(V,{key:"recharts-scatter-symbols"},t.createElement(Gk,e)))}var eM={xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!Oo.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"};function tM(e){var r=pl(e,eM),{animationBegin:n,animationDuration:i,animationEasing:a,hide:o,isAnimationActive:l,legendType:c,lineJointType:s,lineType:u,shape:f,xAxisId:d,yAxisId:p,zAxisId:h}=r,y=Fk(r,Bk),{needClip:v}=FP(d,p),m=(0,t.useMemo)((()=>R(e.children,zg)),[e.children]),g=Wi(),b=He((t=>Rk(t,d,p,h,e.id,m,g)));return null==v?null:t.createElement(t.Fragment,null,t.createElement(Kw,{fn:Zk,args:Xk(Xk({},e),{},{points:b})}),t.createElement(Qk,Wk({},y,{xAxisId:d,yAxisId:p,zAxisId:h,lineType:u,lineJointType:s,legendType:c,shape:f,hide:o,isAnimationActive:l,animationBegin:n,animationDuration:i,animationEasing:a,points:b,needClip:v})))}function rM(e){var r=pl(e,eM),n=Wi();return t.createElement($w,{id:r.id,type:"scatter"},(e=>t.createElement(t.Fragment,null,t.createElement(Fw,{legendPayload:$k(r)}),t.createElement(eO,{type:"scatter",id:e,data:r.data,xAxisId:r.xAxisId,yAxisId:r.yAxisId,zAxisId:r.zAxisId,dataKey:r.dataKey,hide:r.hide,name:r.name,tooltipType:r.tooltipType,isPanorama:n}),t.createElement(tM,Wk({},r,{id:e})))))}rM.displayName="Scatter";var nM=["children"],iM=["dangerouslySetInnerHTML","ticks"];function aM(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oM(){return oM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},oM.apply(null,arguments)}function lM(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function cM(e){var r=Ue(),n=(0,t.useMemo)((()=>{var{children:t}=e;return lM(e,nM)}),[e]),i=He((e=>Ah(e,n.id))),a=n===i;return(0,t.useEffect)((()=>(r(Ok(n)),()=>{r(Pk(n))})),[n,r]),a?e.children:null}var sM=e=>{var{xAxisId:r,className:i}=e,a=He(Bi),o=Wi(),l="xAxis",c=He((e=>Xy(e,l,r,o))),s=He((e=>cv(e,l,r,o))),u=He((e=>Zy(e,r))),f=He((e=>((e,t)=>{var r=Ki(e),n=Ah(e,t);if(null!=n){var i=Jy(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}})(e,r)));if(null==u||null==f)return null;var{dangerouslySetInnerHTML:d,ticks:p}=e,h=lM(e,iM);return t.createElement(qj,oM({},h,{scale:c,x:f.x,y:f.y,width:u.width,height:u.height,className:n("recharts-".concat(l," ").concat(l),i),viewBox:a,ticks:s}))},uM=e=>{var r,n,i,a,o;return t.createElement(cM,{interval:null!==(r=e.interval)&&void 0!==r?r:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(n=e.includeHidden)&&void 0!==n&&n,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter},t.createElement(sM,e))};class fM extends t.Component{render(){return t.createElement(uM,this.props)}}aM(fM,"displayName","XAxis"),aM(fM,"defaultProps",{allowDataOverflow:Eh.allowDataOverflow,allowDecimals:Eh.allowDecimals,allowDuplicatedCategory:Eh.allowDuplicatedCategory,height:Eh.height,hide:!1,mirror:Eh.mirror,orientation:Eh.orientation,padding:Eh.padding,reversed:Eh.reversed,scale:Eh.scale,tickCount:Eh.tickCount,type:Eh.type,xAxisId:0});var dM=["dangerouslySetInnerHTML","ticks"];function pM(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hM(){return hM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hM.apply(null,arguments)}function yM(e){var r=Ue();return(0,t.useEffect)((()=>(r(Ek(e)),()=>{r(Ak(e))})),[e,r]),null}var vM=e=>{var r,{yAxisId:i,className:a,width:o,label:l}=e,c=(0,t.useRef)(null),s=(0,t.useRef)(null),u=He(Bi),f=Wi(),d=Ue(),p="yAxis",h=He((e=>Xy(e,p,i,f))),y=He((e=>ev(e,i))),v=He((e=>((e,t)=>{var r=Ki(e),n=Sh(e,t);if(null!=n){var i=Qy(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}})(e,i))),m=He((e=>cv(e,p,i,f)));if((0,t.useLayoutEffect)((()=>{var e;if("auto"===o&&y&&!jb(l)&&!(0,t.isValidElement)(l)){var r=c.current,n=null==r||null===(e=r.tickRefs)||void 0===e?void 0:e.current,{tickSize:a,tickMargin:u}=r.props,f=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach((e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}}));var l=r?r.getBoundingClientRect().width:0,c=o+(i+a)+l+(r?n:0);return Math.round(c)}return 0})({ticks:n,label:s.current,labelGapWithTick:5,tickSize:a,tickMargin:u});Math.round(y.width)!==Math.round(f)&&d(kk({id:i,width:f}))}}),[c,null==c||null===(r=c.current)||void 0===r||null===(r=r.tickRefs)||void 0===r?void 0:r.current,null==y?void 0:y.width,y,d,l,i,o]),null==y||null==v)return null;var{dangerouslySetInnerHTML:g,ticks:b}=e,x=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,dM);return t.createElement(qj,hM({},x,{ref:c,labelRef:s,scale:h,x:v.x,y:v.y,width:y.width,height:y.height,className:n("recharts-".concat(p," ").concat(p),a),viewBox:u,ticks:m}))},mM=e=>{var r,n,i,a,o;return t.createElement(t.Fragment,null,t.createElement(yM,{interval:null!==(r=e.interval)&&void 0!==r?r:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(n=e.includeHidden)&&void 0!==n&&n,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter}),t.createElement(vM,e))},gM={allowDataOverflow:jh.allowDataOverflow,allowDecimals:jh.allowDecimals,allowDuplicatedCategory:jh.allowDuplicatedCategory,hide:!1,mirror:jh.mirror,orientation:jh.orientation,padding:jh.padding,reversed:jh.reversed,scale:jh.scale,tickCount:jh.tickCount,type:jh.type,width:jh.width,yAxisId:0};class bM extends t.Component{render(){return t.createElement(mM,this.props)}}pM(bM,"displayName","YAxis"),pM(bM,"defaultProps",gM);var xM={begin:0,duration:1e3,easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}};function wM(e){var r=pl(e,xM),{from:n,to:i,attributeName:a,isActive:o,canBegin:l,duration:c,easing:s,begin:u,onAnimationEnd:f,onAnimationStart:d,children:p}=r,h=Kl(a,r.animationManager),[y,v]=(0,t.useState)(o?n:i);return(0,t.useEffect)((()=>{o||v(i)}),[o,i]),(0,t.useEffect)((()=>{if(!o||!l)return rO;var e=h.subscribe(v);return h.start([d,u,i,c,f]),()=>{h.stop(),e&&e(),f()}}),[o,l,c,s,u,d,f,h,i]),p(o&&l?{transition:Al([a],c,s),[a]:y}:{[a]:y})}var OM=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function PM(){return PM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},PM.apply(null,arguments)}function EM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function AM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?EM(Object(r),!0).forEach((function(t){jM(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):EM(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function jM(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function SM(e){var{direction:r,width:n,dataKey:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,OM),u=C(s),{data:f,dataPointFormatter:d,xAxisId:p,yAxisId:h,errorBarOffset:y}=(0,t.useContext)(KP),v=(e=>{var t=Wi();return He((r=>fv(r,"xAxis",e,t)))})(p),m=(e=>{var t=Wi();return He((r=>fv(r,"yAxis",e,t)))})(h);if(null==(null==v?void 0:v.scale)||null==(null==m?void 0:m.scale)||null==f)return null;if("x"===r&&"number"!==v.type)return null;var g=f.map((e=>{var{x:s,y:f,value:p,errorVal:h}=d(e,i,r);if(!h||null==s||null==f)return null;var g,b,x=[];if(Array.isArray(h)?[g,b]=h:g=b=h,"x"===r){var{scale:w}=v,O=f+y,P=O+n,E=O-n,A=w(p-g),j=w(p+b);x.push({x1:j,y1:P,x2:j,y2:E}),x.push({x1:A,y1:O,x2:j,y2:O}),x.push({x1:A,y1:P,x2:A,y2:E})}else if("y"===r){var{scale:S}=m,k=s+y,M=k-n,T=k+n,D=S(p-g),C=S(p+b);x.push({x1:M,y1:C,x2:T,y2:C}),x.push({x1:k,y1:D,x2:k,y2:C}),x.push({x1:M,y1:D,x2:T,y2:D})}var I="x"===r?"scaleX":"scaleY",N="".concat(s+y,"px ").concat(f+y,"px");return t.createElement(V,PM({className:"recharts-errorBar",key:"bar-".concat(x.map((e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2))))},u),x.map((e=>{var r=a?{transformOrigin:N}:void 0;return t.createElement(wM,{from:"".concat(I,"(0)"),to:"".concat(I,"(1)"),attributeName:"transform",begin:o,easing:c,isActive:a,duration:l,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)},(n=>t.createElement("line",PM({},e,{style:AM(AM({},r),n)}))))})))}));return t.createElement(V,{className:"recharts-errorBars"},g)}var kM={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function MM(e){var r,n,i=(r=e.direction,n=Qi(),null!=r?r:null!=n&&"horizontal"===n?"y":"x"),{width:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s}=pl(e,kM);return t.createElement(t.Fragment,null,t.createElement(BP,{dataKey:e.dataKey,direction:i}),t.createElement(SM,PM({},e,{direction:i,width:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s})))}class TM extends t.Component{render(){return t.createElement(MM,this.props)}}jM(TM,"defaultProps",kM),jM(TM,"displayName","ErrorBar");var DM=a(9888);let CM=function(e){e()};const IM=()=>CM,NM=Symbol.for("react-redux-context"),_M="undefined"!=typeof globalThis?globalThis:{};function LM(){var e;if(!t.createContext)return{};const r=null!=(e=_M[NM])?e:_M[NM]=new Map;let n=r.get(t.createContext);return n||(n=t.createContext(null),r.set(t.createContext,n)),n}const RM=LM();let KM=null;a(4146);const zM={notify(){},get:()=>[]};function BM(e,t){let r,n=zM,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function l(){i++,r||(r=t?t.addNestedSub(o):e.subscribe(o),n=function(){const e=IM();let t=null,r=null;return{clear(){t=null,r=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let n=!0,i=r={callback:e,next:null,prev:r};return i.prev?i.prev.next=i:t=i,function(){n&&null!==t&&(n=!1,i.next?i.next.prev=i.prev:r=i.prev,i.prev?i.prev.next=i.next:t=i.next)}}}}())}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=zM)}const s={addNestedSub:function(e){l();const t=n.subscribe(e);let r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return s}const FM=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?t.useLayoutEffect:t.useEffect;let WM=null;const UM=function({store:e,context:r,children:n,serverState:i,stabilityCheck:a="once",noopCheck:o="once"}){const l=t.useMemo((()=>{const t=BM(e);return{store:e,subscription:t,getServerState:i?()=>i:void 0,stabilityCheck:a,noopCheck:o}}),[e,i,a,o]),c=t.useMemo((()=>e.getState()),[e]);FM((()=>{const{subscription:t}=l;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==e.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}}),[l,c]);const s=r||RM;return t.createElement(s.Provider,{value:l},n)};var XM;(e=>{KM=e})(Be.useSyncExternalStoreWithSelector),(e=>{WM=e})(DM.useSyncExternalStore),XM=$.unstable_batchedUpdates,CM=XM;var VM=et([(e,t)=>t,Ji,fh,yh,xm,Em,$m,Ki],((e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var c=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?ri({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(c){var u=((e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius)(c,t),f=((e,t,r,n,i)=>{var a,o=-1,l=null!==(a=null==t?void 0:t.length)&&void 0!==a?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&Math.abs(Math.abs(i[1]-i[0])-360)<=1e-6)for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,f=r[c].coordinate,d=c>=l-1?r[0].coordinate:r[c+1].coordinate,p=void 0;if(s(f-u)!==s(d-f)){var h=[];if(s(d-f)===s(i[1]-i[0])){p=d;var y=f+i[1]-i[0];h[0]=Math.min(y,(y+u)/2),h[1]=Math.max(y,(y+u)/2)}else{p=u;var v=d+i[1]-i[0];h[0]=Math.min(f,(v+f)/2),h[1]=Math.max(f,(v+f)/2)}var m=[Math.min(f,(p+f)/2),Math.max(f,(p+f)/2)];if(e>m[0]&&e<=m[1]||e>=h[0]&&e<=h[1]){({index:o}=r[c]);break}}else{var g=Math.min(u,d),b=Math.max(u,d);if(e>(g+f)/2&&e<=(b+f)/2){({index:o}=r[c]);break}}}else if(t)for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}return o})(u,o,a,n,i),d=((e,t,r,n)=>{var i=t.find((e=>e&&e.index===r));if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return oi(oi(oi({},n),Jn(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return oi(oi(oi({},n),Jn(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}})(t,a,f,c);return{activeIndex:String(f),activeCoordinate:d}}}})),$M=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},HM=Wr("mouseClick"),qM=In();qM.startListening({actionCreator:HM,effect:(e,t)=>{var r=e.payload,n=VM(t.getState(),$M(r));null!=(null==n?void 0:n.activeIndex)&&t.dispatch(Tv({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var YM=Wr("mouseMove"),GM=In();function ZM(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}GM.startListening({actionCreator:YM,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=gv(n,n.tooltip.settings.shared),a=VM(n,$M(r));"axis"===i&&(null!=(null==a?void 0:a.activeIndex)?t.dispatch(Mv({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(Sv()))}});var JM={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},QM=Gr({name:"rootProps",initialState:JM,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:JM.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),eT=QM.reducer,{updateOptions:tT}=QM.actions,rT=Gr({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:nT}=rT.actions,iT=rT.reducer,aT=Wr("keyDown"),oT=Wr("focus"),lT=In();lT.startListening({actionCreator:aT,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(Kv(n,Qv(r))),o=Em(r);if("Enter"!==i){var l=a+("ArrowRight"===i?1:-1)*("left-to-right"===hv(r)?1:-1);if(!(null==o||l>=o.length||l<0)){var c=Zm(r,"axis","hover",String(l));t.dispatch(Cv({active:!0,activeIndex:l.toString(),activeDataKey:void 0,activeCoordinate:c}))}}else{var s=Zm(r,"axis","hover",String(n.index));t.dispatch(Cv({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:s}))}}}}}),lT.startListening({actionCreator:oT,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=Zm(r,"axis","hover",String("0"));t.dispatch(Cv({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var cT=Wr("externalEvent"),sT=In();sT.startListening({actionCreator:cT,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:Nm(r),activeDataKey:Dm(r),activeIndex:Mm(r),activeLabel:Tm(r),activeTooltipIndex:Mm(r),isTooltipActive:_m(r)};e.payload.handler(n,e.payload.reactEvent)}}});var uT=et([Wv],(e=>e.tooltipItemPayloads)),fT=et([uT,Fv,(e,t,r)=>t,(e,t,r)=>r],((e,t,r,n)=>{var i=e.find((e=>e.settings.dataKey===n));if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}})),dT=Wr("touchMove"),pT=In();pT.startListening({actionCreator:dT,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=gv(n,n.tooltip.settings.shared);if("axis"===i){var a=VM(n,$M({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));null!=(null==a?void 0:a.activeIndex)&&t.dispatch(Mv({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],c=document.elementFromPoint(l.clientX,l.clientY);if(!c||!c.getAttribute)return;var s=c.getAttribute(Ii),u=null!==(o=c.getAttribute(Ni))&&void 0!==o?o:void 0,f=fT(t.getState(),s,u);t.dispatch(Av({activeDataKey:u,activeIndex:s,activeCoordinate:f}))}}});var hT=xr({brush:AA,cartesianAxis:Mk,chartData:Og,errorBars:NP,graphicalItems:Qw,layout:Fn,legend:Ya,options:yg,polarAxis:zx,polarOptions:iT,referenceElements:aj,rootProps:eT,tooltip:Iv}),yT=function(e){return qr({reducer:hT,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([qM.middleware,GM.middleware,lT.middleware,sT.middleware,pT.middleware]),devTools:{serialize:{replacer:ZM},name:"recharts-".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart")}})};function vT(e){var{preloadedState:r,children:n,reduxStoreName:i}=e,a=Wi(),o=(0,t.useRef)(null);if(a)return n;null==o.current&&(o.current=yT(r,i));var l=Fe;return t.createElement(UM,{context:l,store:o.current},n)}function mT(e){var{layout:r,width:n,height:i,margin:a}=e,o=Ue(),l=Wi();return(0,t.useEffect)((()=>{l||(o(Kn(r)),o(zn({width:n,height:i})),o(Rn(a)))}),[o,l,r,n,i,a]),null}function gT(e){var r=Ue();return(0,t.useEffect)((()=>{r(tT(e))}),[r,e]),null}var bT=["children"];function xT(){return xT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xT.apply(null,arguments)}var wT={width:"100%",height:"100%"},OT=(0,t.forwardRef)(((e,r)=>{var n=Yi(),i=Gi(),a=Po();if(!qo(n)||!qo(i))return null;var o,l,{children:c,otherAttributes:s,title:u,desc:f}=e;return o="number"==typeof s.tabIndex?s.tabIndex:a?0:void 0,l="string"==typeof s.role?s.role:a?"application":void 0,t.createElement(W,xT({},s,{title:u,desc:f,role:l,tabIndex:o,width:n,height:i,style:wT,ref:r}),c)})),PT=e=>{var{children:r}=e,n=He(Vi);if(!n)return null;var{width:i,height:a,y:o,x:l}=n;return t.createElement(W,{width:i,height:a,x:l,y:o},r)},ET=(0,t.forwardRef)(((e,r)=>{var{children:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,bT);return Wi()?t.createElement(PT,null,n):t.createElement(OT,xT({ref:r},i),n)}));function AT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?AT(Object(r),!0).forEach((function(t){ST(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):AT(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ST(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var kT=(0,t.forwardRef)(((e,r)=>{var{children:i,className:a,height:o,onClick:l,onContextMenu:c,onDoubleClick:s,onMouseDown:u,onMouseEnter:f,onMouseLeave:d,onMouseMove:p,onMouseUp:h,onTouchEnd:y,onTouchMove:v,onTouchStart:m,style:g,width:b}=e,x=Ue(),[w,O]=(0,t.useState)(null),[P,E]=(0,t.useState)(null);Eg();var A=function(){var e=Ue(),[r,n]=(0,t.useState)(null),i=He(ki);return(0,t.useEffect)((()=>{if(null!=r){var t=r.getBoundingClientRect().width/r.offsetWidth;Ho(t)&&t!==i&&e(Bn(t))}}),[r,e,i]),n}(),j=(0,t.useCallback)((e=>{A(e),"function"==typeof r&&r(e),O(e),E(e)}),[A,r,O,E]),S=(0,t.useCallback)((e=>{x(HM(e)),x(cT({handler:l,reactEvent:e}))}),[x,l]),k=(0,t.useCallback)((e=>{x(YM(e)),x(cT({handler:f,reactEvent:e}))}),[x,f]),M=(0,t.useCallback)((e=>{x(Sv()),x(cT({handler:d,reactEvent:e}))}),[x,d]),T=(0,t.useCallback)((e=>{x(YM(e)),x(cT({handler:p,reactEvent:e}))}),[x,p]),D=(0,t.useCallback)((()=>{x(oT())}),[x]),C=(0,t.useCallback)((e=>{x(aT(e.key))}),[x]),I=(0,t.useCallback)((e=>{x(cT({handler:c,reactEvent:e}))}),[x,c]),N=(0,t.useCallback)((e=>{x(cT({handler:s,reactEvent:e}))}),[x,s]),_=(0,t.useCallback)((e=>{x(cT({handler:u,reactEvent:e}))}),[x,u]),L=(0,t.useCallback)((e=>{x(cT({handler:h,reactEvent:e}))}),[x,h]),R=(0,t.useCallback)((e=>{x(cT({handler:m,reactEvent:e}))}),[x,m]),K=(0,t.useCallback)((e=>{x(dT(e)),x(cT({handler:v,reactEvent:e}))}),[x,v]),z=(0,t.useCallback)((e=>{x(cT({handler:y,reactEvent:e}))}),[x,y]);return t.createElement(cg.Provider,{value:w},t.createElement(H.Provider,{value:P},t.createElement("div",{className:n("recharts-wrapper",a),style:jT({position:"relative",cursor:"default",width:b,height:o},g),onClick:S,onContextMenu:I,onDoubleClick:N,onFocus:D,onKeyDown:C,onMouseDown:_,onMouseEnter:k,onMouseLeave:M,onMouseMove:T,onMouseUp:L,onTouchEnd:z,onTouchMove:K,onTouchStart:R,ref:j},i)))})),MT=["children","className","width","height","style","compact","title","desc"];var TT=(0,t.forwardRef)(((e,r)=>{var{children:n,className:i,width:a,height:o,style:l,compact:c,title:s,desc:u}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,MT),d=C(f);return c?t.createElement(ET,{otherAttributes:d,title:s,desc:u},n):t.createElement(kT,{className:i,style:l,width:a,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},t.createElement(ET,{otherAttributes:d,title:s,desc:u,ref:r},t.createElement(lj,null,n)))})),DT=["width","height"];function CT(){return CT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},CT.apply(null,arguments)}var IT={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},NT=(0,t.forwardRef)((function(e,r){var n,i=pl(e.categoricalChartProps,IT),{width:a,height:o}=i,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,DT);if(!qo(a)||!qo(o))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,categoricalChartProps:d}=e,p={chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,eventEmitter:void 0};return t.createElement(vT,{preloadedState:{options:p},reduxStoreName:null!==(n=d.id)&&void 0!==n?n:c},t.createElement(yA,{chartData:d.data}),t.createElement(mT,{width:a,height:o,layout:i.layout,margin:i.margin}),t.createElement(gT,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),t.createElement(TT,CT({},l,{width:a,height:o,ref:r})))})),_T=["axis"],LT=(0,t.forwardRef)(((e,r)=>t.createElement(NT,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:_T,tooltipPayloadSearcher:pg,categoricalChartProps:e,ref:r}))),RT=["axis","item"],KT=(0,t.forwardRef)(((e,r)=>t.createElement(NT,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:RT,tooltipPayloadSearcher:pg,categoricalChartProps:e,ref:r})));function zT(e){var r=Ue();return(0,t.useEffect)((()=>{r(nT(e))}),[r,e]),null}var BT=["width","height","layout"];function FT(){return FT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},FT.apply(null,arguments)}var WT={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},UT=(0,t.forwardRef)((function(e,r){var n,i=pl(e.categoricalChartProps,WT),{width:a,height:o,layout:l}=i,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,BT);if(!qo(a)||!qo(o))return null;var{chartName:s,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=e,p={chartName:s,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0};return t.createElement(vT,{preloadedState:{options:p},reduxStoreName:null!==(n=i.id)&&void 0!==n?n:s},t.createElement(yA,{chartData:i.data}),t.createElement(mT,{width:a,height:o,layout:l,margin:i.margin}),t.createElement(gT,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),t.createElement(zT,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),t.createElement(TT,FT({width:a,height:o},c,{ref:r})))})),XT=["item"],VT={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},$T=(0,t.forwardRef)(((e,r)=>{var n=pl(e,VT);return t.createElement(UT,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:XT,tooltipPayloadSearcher:pg,categoricalChartProps:n,ref:r})})),HT=a(1576),qT=a.n(HT),YT=["width","height","className","style","children","type"];function GT(){return GT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},GT.apply(null,arguments)}function ZT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function JT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ZT(Object(r),!0).forEach((function(t){QT(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ZT(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function QT(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var eD="value",tD=(e,t)=>l()(e,t),rD={chartName:"Treemap",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:tD,eventEmitter:void 0},nD=e=>{var t,{depth:r,node:n,index:i,dataKey:a,nameKey:o,nestedActiveTooltipIndex:l}=e,c=0===r?"":function(e){return"".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"","children[").concat(e,"]")}(i,l),{children:s}=n,f=r+1,d=s&&s.length?s.map(((e,t)=>nD({depth:f,node:e,index:t,dataKey:a,nameKey:o,nestedActiveTooltipIndex:c}))):null;return t=s&&s.length?d.reduce(((e,t)=>e+t[eD]),0):u(n[a])||n[a]<=0?0:n[a],JT(JT({},n),{},{children:d,name:ci(n,o,""),[eD]:t,depth:r,index:i,tooltipIndex:c})},iD=(e,t,r)=>{var n=t*t,i=e.area*e.area,{min:a,max:o}=e.reduce(((e,t)=>({min:Math.min(e.min,t.area),max:Math.max(e.max,t.area)})),{min:1/0,max:0});return i?Math.max(n*o*r/i,i/(n*a*r)):1/0},aD=(e,t,r,n)=>t===r.width?((e,t,r,n)=>{var i=t?Math.round(e.area/t):0;(n||i>r.height)&&(i=r.height);for(var a,o=r.x,l=0,c=e.length;l<c;l++)(a=e[l]).x=o,a.y=r.y,a.height=i,a.width=Math.min(i?Math.round(a.area/i):0,r.x+r.width-o),o+=a.width;return a.width+=r.x+r.width-o,JT(JT({},r),{},{y:r.y+i,height:r.height-i})})(e,t,r,n):((e,t,r,n)=>{var i=t?Math.round(e.area/t):0;(n||i>r.width)&&(i=r.width);for(var a,o=r.y,l=0,c=e.length;l<c;l++)(a=e[l]).x=r.x,a.y=o,a.width=i,a.height=Math.min(i?Math.round(a.area/i):0,r.y+r.height-o),o+=a.height;return a&&(a.height+=r.y+r.height-o),JT(JT({},r),{},{x:r.x+i,width:r.width-i})})(e,t,r,n),oD=(e,t)=>{var{children:r}=e;if(r&&r.length){var n,i,a=(e=>({x:e.x,y:e.y,width:e.width,height:e.height}))(e),o=[],l=1/0,c=Math.min(a.width,a.height),s=((e,t)=>{var r=t<0?0:t;return e.map((e=>{var t=e[eD]*r;return JT(JT({},e),{},{area:u(t)||t<=0?0:t})}))})(r,a.width*a.height/e[eD]),f=s.slice();for(o.area=0;f.length>0;)o.push(n=f[0]),o.area+=n.area,(i=iD(o,c,t))<=l?(f.shift(),l=i):(o.area-=o.pop().area,a=aD(o,c,a,!1),c=Math.min(a.width,a.height),o.length=o.area=0,l=1/0);return o.length&&(a=aD(o,c,a,!0),o.length=o.area=0),JT(JT({},e),{},{children:s.map((e=>oD(e,t)))})}return e},lD={isAnimationFinished:!1,formatRoot:null,currentRoot:null,nestIndex:[]};function cD(e){var{content:r,nodeProps:n,type:i,colorPanel:a,onMouseEnter:o,onMouseLeave:l,onClick:c}=e;if(t.isValidElement(r))return t.createElement(V,{onMouseEnter:o,onMouseLeave:l,onClick:c},t.cloneElement(r,n));if("function"==typeof r)return t.createElement(V,{onMouseEnter:o,onMouseLeave:l,onClick:c},r(n));var{x:s,y:u,width:f,height:d,index:p}=n,h=null;f>10&&d>10&&n.children&&"nest"===i&&(h=t.createElement(Gb,{points:[{x:s+2,y:u+d/2},{x:s+6,y:u+d/2+3},{x:s+2,y:u+d/2+6}]}));var y=null,v=Yg(n.name);f>20&&d>20&&v.width<f&&v.height<d&&(y=t.createElement("text",{x:s+8,y:u+d/2+7,fontSize:14},n.name));var m=a||Ci;return t.createElement("g",null,t.createElement(Yl,GT({fill:n.depth<2?m[p%m.length]:"rgba(255,255,255,0)",stroke:"#fff"},qT()(n,["children"]),{onMouseEnter:o,onMouseLeave:l,onClick:c,"data-recharts-item-index":n.tooltipIndex})),h,y)}function sD(e){var r=Ue(),n=e.nodeProps?{x:e.nodeProps.x+e.nodeProps.width/2,y:e.nodeProps.y+e.nodeProps.height/2}:null;return t.createElement(cD,GT({},e,{onMouseEnter:()=>{r(Av({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:n}))},onMouseLeave:()=>{},onClick:()=>{r(kv({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:n}))}}))}function uD(e){var{props:t,currentRoot:r}=e,{dataKey:n,nameKey:i,stroke:a,fill:o}=t;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:a,strokeWidth:void 0,fill:o,dataKey:n,nameKey:i,name:void 0,hide:!1,type:void 0,color:o,unit:""}}}var fD={top:0,right:0,bottom:0,left:0};class dD extends t.PureComponent{constructor(){super(...arguments),QT(this,"state",JT({},lD)),QT(this,"handleAnimationEnd",(()=>{var{onAnimationEnd:e}=this.props;this.setState({isAnimationFinished:!0}),"function"==typeof e&&e()})),QT(this,"handleAnimationStart",(()=>{var{onAnimationStart:e}=this.props;this.setState({isAnimationFinished:!1}),"function"==typeof e&&e()})),QT(this,"handleTouchMove",((e,t)=>{var r=t.touches[0],n=document.elementFromPoint(r.clientX,r.clientY);if(n&&n.getAttribute){var i=n.getAttribute("data-recharts-item-index"),a=tD(this.state.formatRoot,i);if(a){var{dataKey:o,dispatch:l}=this.props,c={x:a.x+a.width/2,y:a.y+a.height/2};l(Av({activeIndex:i,activeDataKey:o,activeCoordinate:c}))}}}))}static getDerivedStateFromProps(e,t){if(e.data!==t.prevData||e.type!==t.prevType||e.width!==t.prevWidth||e.height!==t.prevHeight||e.dataKey!==t.prevDataKey||e.aspectRatio!==t.prevAspectRatio){var r=nD({depth:0,node:{children:e.data,x:0,y:0,width:e.width,height:e.height},index:0,dataKey:e.dataKey,nameKey:e.nameKey}),n=oD(r,e.aspectRatio);return JT(JT({},t),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:e.aspectRatio,prevData:e.data,prevWidth:e.width,prevHeight:e.height,prevDataKey:e.dataKey,prevType:e.type})}return null}handleMouseEnter(e,t){t.persist();var{onMouseEnter:r}=this.props;r&&r(e,t)}handleMouseLeave(e,t){t.persist();var{onMouseLeave:r}=this.props;r&&r(e,t)}handleClick(e){var{onClick:t,type:r}=this.props;if("nest"===r&&e.children){var{width:n,height:i,dataKey:a,nameKey:o,aspectRatio:l}=this.props,c=nD({depth:0,node:JT(JT({},e),{},{x:0,y:0,width:n,height:i}),index:0,dataKey:a,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),s=oD(c,l),{nestIndex:u}=this.state;u.push(e),this.setState({formatRoot:s,currentRoot:c,nestIndex:u})}t&&t(e)}handleNestIndex(e,t){var{nestIndex:r}=this.state,{width:n,height:i,dataKey:a,nameKey:o,aspectRatio:l}=this.props,c=nD({depth:0,node:JT(JT({},e),{},{x:0,y:0,width:n,height:i}),index:0,dataKey:a,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),s=oD(c,l);r=r.slice(0,t+1),this.setState({formatRoot:s,currentRoot:e,nestIndex:r})}renderItem(e,r,n){var{isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l,isUpdateAnimationActive:c,type:s,colorPanel:u,dataKey:f}=this.props,{isAnimationFinished:d}=this.state,{width:p,height:h,x:y,y:v,depth:m}=r,g=parseInt("".concat((2*Math.random()-1)*p),10),b={};return(n||"nest"===s)&&(b={onMouseEnter:this.handleMouseEnter.bind(this,r),onMouseLeave:this.handleMouseLeave.bind(this,r),onClick:this.handleClick.bind(this,r)}),i?t.createElement(wM,{from:"translate(".concat(g,"px, ").concat(g,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:a,easing:l,isActive:i,duration:o,onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(n=>t.createElement(V,GT({},b,{style:n}),m>2&&!d?null:t.createElement(sD,{content:e,dataKey:f,nodeProps:JT(JT({},r),{},{isAnimationActive:i,isUpdateAnimationActive:!c,width:p,height:h,x:y,y:v}),type:s,colorPanel:u})))):t.createElement(V,b,t.createElement(sD,{content:e,dataKey:f,nodeProps:JT(JT({},r),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:p,height:h,x:y,y:v}),type:s,colorPanel:u}))}renderNode(e,r){var{content:n,type:i}=this.props,a=JT(JT(JT({},C(this.props)),r),{},{root:e}),o=!r.children||!r.children.length,{currentRoot:l}=this.state;return!(l.children||[]).filter((e=>e.depth===r.depth&&e.name===r.name)).length&&e.depth&&"nest"===i?null:t.createElement(V,{key:"recharts-treemap-node-".concat(a.x,"-").concat(a.y,"-").concat(a.name),className:"recharts-treemap-depth-".concat(r.depth)},this.renderItem(n,a,o),r.children&&r.children.length?r.children.map((e=>this.renderNode(r,e))):null)}renderAllNodes(){var{formatRoot:e}=this.state;return e?this.renderNode(e,e):null}renderNestIndex(){var{nameKey:e,nestIndexContent:r}=this.props,{nestIndex:n}=this.state;return t.createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},n.map(((n,i)=>{var a=l()(n,e,"root"),o=null;return t.isValidElement(r)&&(o=t.cloneElement(r,n,i)),o="function"==typeof r?r(n,i):a,t.createElement("div",{onClick:this.handleNestIndex.bind(this,n,i),key:"nest-index-".concat(y()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},o)})))}render(){var e=this.props,{width:r,height:n,className:i,style:a,children:o,type:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,YT),s=C(c);return t.createElement(cg.Provider,{value:this.state.tooltipPortal},t.createElement(Kw,{fn:uD,args:{props:this.props,currentRoot:this.state.currentRoot}}),t.createElement(kT,{className:i,style:a,width:r,height:n,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:this.handleTouchMove,onTouchEnd:void 0},t.createElement(W,GT({},s,{width:r,height:"nest"===l?n-30:n}),this.renderAllNodes(),o),"nest"===l&&this.renderNestIndex()))}}function pD(e){var r=Ue();return t.createElement(dD,GT({},e,{dispatch:r}))}function hD(e){var r,{width:n,height:i}=e;return qo(n)&&qo(i)?t.createElement(vT,{preloadedState:{options:rD},reduxStoreName:null!==(r=e.className)&&void 0!==r?r:"Treemap"},t.createElement(ea,{width:n,height:i}),t.createElement(ta,{margin:fD}),t.createElement(pD,e)):null}QT(dD,"displayName","Treemap"),QT(dD,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",nameKey:"name",type:"flat",isAnimationActive:!Oo.isSsr,isUpdateAnimationActive:!Oo.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var yD=a(2067),vD=a.n(yD),mD=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"],gD=["width","height","className","style","children"];function bD(){return bD=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bD.apply(null,arguments)}function xD(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function wD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function OD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wD(Object(r),!0).forEach((function(t){PD(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wD(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function PD(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ED=e=>e.y+e.dy/2,AD=e=>e&&e.value||0,jD=(e,t)=>t.reduce(((t,r)=>t+AD(e[r])),0),SD=(e,t,r)=>r.reduce(((r,n)=>{var i=t[n],a=e[i.source];return r+ED(a)*AD(t[n])}),0),kD=(e,t,r)=>r.reduce(((r,n)=>{var i=t[n],a=e[i.target];return r+ED(a)*AD(t[n])}),0),MD=(e,t)=>e.y-t.y,TD=(e,t)=>{for(var{targetNodes:r}=t,n=0,i=r.length;n<i;n++){var a=e[r[n]];a&&(a.depth=Math.max(t.depth+1,a.depth),TD(e,a))}},DD=function(e,t,r){for(var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],i=0,a=e.length;i<a;i++){var o=e[i],l=o.length;n&&o.sort(MD);for(var c=0,s=0;s<l;s++){var u=o[s],f=c-u.y;f>0&&(u.y+=f),c=u.y+u.dy+r}c=t+r;for(var d=l-1;d>=0;d--){var p=o[d],h=p.y+p.dy+r-c;if(!(h>0))break;p.y-=h,c=p.y}}},CD=(e,t,r,n)=>{for(var i=0,a=t.length;i<a;i++)for(var o=t[i],l=0,c=o.length;l<c;l++){var s=o[l];if(s.sourceLinks.length){var u=jD(r,s.sourceLinks),f=SD(e,r,s.sourceLinks)/u;s.y+=(f-ED(s))*n}}},ID=(e,t,r,n)=>{for(var i=t.length-1;i>=0;i--)for(var a=t[i],o=0,l=a.length;o<l;o++){var c=a[o];if(c.targetLinks.length){var s=jD(r,c.targetLinks),u=kD(e,r,c.targetLinks)/s;c.y+=(u-ED(c))*n}}},ND=e=>{var{data:t,width:r,height:n,iterations:i,nodeWidth:a,nodePadding:o,sort:l}=e,{links:c}=t,{tree:s}=((e,t,r)=>{for(var{nodes:n,links:i}=e,a=n.map(((e,t)=>{var r=((e,t)=>{for(var r=[],n=[],i=[],a=[],o=0,l=e.length;o<l;o++){var c=e[o];c.source===t&&(i.push(c.target),a.push(o)),c.target===t&&(r.push(c.source),n.push(o))}return{sourceNodes:r,sourceLinks:n,targetLinks:a,targetNodes:i}})(i,t);return OD(OD(OD({},e),r),{},{value:Math.max(jD(i,r.sourceLinks),jD(i,r.targetLinks)),depth:0})})),o=0,l=a.length;o<l;o++){var c=a[o];c.sourceNodes.length||TD(a,c)}var s=Dx()(a,(e=>e.depth)).depth;if(s>=1)for(var u=(t-r)/s,f=0,d=a.length;f<d;f++){var p=a[f];p.targetNodes.length||(p.depth=s),p.x=p.depth*u,p.dx=r}return{tree:a,maxDepth:s}})(t,r,a),u=(e=>{for(var t=[],r=0,n=e.length;r<n;r++){var i=e[r];t[i.depth]||(t[i.depth]=[]),t[i.depth].push(i)}return t})(s),f=((e,t,r,n)=>{for(var i=Math.min(...e.map((e=>(t-(e.length-1)*r)/vD()(e,AD)))),a=0,o=e.length;a<o;a++)for(var l=0,c=e[a].length;l<c;l++){var s=e[a][l];s.y=l,s.dy=s.value*i}return n.map((e=>OD(OD({},e),{},{dy:AD(e)*i})))})(u,n,o,c);DD(u,n,o,l);for(var d=1,p=1;p<=i;p++)ID(s,u,f,d*=.99),DD(u,n,o,l),CD(s,u,f,d),DD(u,n,o,l);return((e,t)=>{for(var r=0,n=e.length;r<n;r++){var i=e[r],a=0,o=0;i.targetLinks.sort(((r,n)=>e[t[r].target].y-e[t[n].target].y)),i.sourceLinks.sort(((r,n)=>e[t[r].source].y-e[t[n].source].y));for(var l=0,c=i.targetLinks.length;l<c;l++){var s=t[i.targetLinks[l]];s&&(s.sy=a,a+=s.dy)}for(var u=0,f=i.sourceLinks.length;u<f;u++){var d=t[i.sourceLinks[u]];d&&(d.ty=o,o+=d.dy)}}})(s,f),{nodes:s,links:f}},_D=(e,t)=>"node"===t?{x:+e.x+ +e.width/2,y:+e.y+ +e.height/2}:"sourceX"in e&&{x:(e.sourceX+e.targetX)/2,y:(e.sourceY+e.targetY)/2},LD={chartName:"Sankey",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:(e,t,r,n)=>{if(null!=t&&"string"==typeof t){var i=t.split("-"),[a,o]=i,c=l()(r,"".concat(a,"s[").concat(o,"]"));if(c){var s=((e,t,r)=>{var{payload:n}=e;if("node"===t)return{payload:n,name:ci(n,r,""),value:ci(n,"value")};if("source"in n&&n.source&&n.target){var i=ci(n.source,r,""),a=ci(n.target,r,"");return{payload:n,name:"".concat(i," - ").concat(a),value:ci(n,"value")}}return null})(c,a,n);return s}}},eventEmitter:void 0};function RD(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:i,fill:a,name:o,data:l}=e;return{dataDefinedOnItem:l,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,name:o,nameKey:r,color:a,unit:""}}}var KD={top:0,right:0,bottom:0,left:0};function zD(e){var{props:r,i:n,linkContent:i,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c}=e,s=_D(r,"link"),u="link-".concat(n),f=Ue(),d={onMouseEnter:e=>{f(Av({activeIndex:u,activeDataKey:c,activeCoordinate:s})),a(r,e)},onMouseLeave:e=>{f(jv()),o(r,e)},onClick:e=>{f(kv({activeIndex:u,activeDataKey:c,activeCoordinate:s})),l(r,e)}};return t.createElement(V,d,function(e,r){if(t.isValidElement(e))return t.cloneElement(e,r);if("function"==typeof e)return e(r);var{sourceX:n,sourceY:i,sourceControlX:a,targetX:o,targetY:l,targetControlX:c,linkWidth:s}=r,u=xD(r,mD);return t.createElement("path",bD({className:"recharts-sankey-link",d:"\n          M".concat(n,",").concat(i,"\n          C").concat(a,",").concat(i," ").concat(c,",").concat(l," ").concat(o,",").concat(l,"\n        "),fill:"none",stroke:"#333",strokeWidth:s,strokeOpacity:"0.2"},C(u)))}(i,r))}function BD(e){var{modifiedLinks:r,links:n,linkContent:i,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c}=e;return t.createElement(V,{className:"recharts-sankey-links",key:"recharts-sankey-links"},n.map(((e,n)=>{var s=r[n];return t.createElement(zD,{key:"link-".concat(e.source,"-").concat(e.target,"-").concat(e.value),props:s,linkContent:i,i:n,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c})})))}function FD(e){var{props:r,nodeContent:n,i,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c}=e,s=Ue(),u=_D(r,"node"),f="node-".concat(i),d={onMouseEnter:e=>{s(Av({activeIndex:f,activeDataKey:c,activeCoordinate:u})),a(r,e)},onMouseLeave:e=>{s(jv()),o(r,e)},onClick:e=>{s(kv({activeIndex:f,activeDataKey:c,activeCoordinate:u})),l(r,e)}};return t.createElement(V,d,function(e,r){return t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Yl,bD({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},C(r)))}(n,r))}function WD(e){var{modifiedNodes:r,nodeContent:n,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l}=e;return t.createElement(V,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},r.map(((e,r)=>t.createElement(FD,{props:e,nodeContent:n,i:r,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l}))))}class UD extends t.PureComponent{constructor(){super(...arguments),PD(this,"state",{nodes:[],links:[],modifiedLinks:[],modifiedNodes:[]})}static getDerivedStateFromProps(e,t){var{data:r,width:n,height:i,margin:a,iterations:o,nodeWidth:c,nodePadding:s,sort:u,linkCurvature:f}=e;if(r!==t.prevData||n!==t.prevWidth||i!==t.prevHeight||!Ij(a,t.prevMargin)||o!==t.prevIterations||c!==t.prevNodeWidth||s!==t.prevNodePadding||u!==t.sort){var d=n-(a&&a.left||0)-(a&&a.right||0),p=i-(a&&a.top||0)-(a&&a.bottom||0),{links:h,nodes:y}=ND({data:r,width:d,height:p,iterations:o,nodeWidth:c,nodePadding:s,sort:u}),v=l()(a,"top")||0,m=l()(a,"left")||0,g=h.map(((t,r)=>(e=>{var{link:t,nodes:r,left:n,top:i,i:a,linkContent:o,linkCurvature:l}=e,{sy:c,ty:s,dy:u}=t,f=r[t.source],d=r[t.target],p=f.x+f.dx+n,h=d.x+n,y=((e,t)=>{var r=+e,n=t-r;return e=>r+n*e})(p,h),v=y(l),m=y(1-l);return OD({sourceX:p,targetX:h,sourceY:f.y+c+u/2+i,targetY:d.y+s+u/2+i,sourceControlX:v,targetControlX:m,sourceRelativeY:c,targetRelativeY:s,linkWidth:u,index:a,payload:OD(OD({},t),{},{source:f,target:d})},z(o,!1))})({link:t,nodes:y,i:r,top:v,left:m,linkContent:e.link,linkCurvature:f}))),b=y.map(((t,r)=>(e=>{var{node:t,nodeContent:r,top:n,left:i,i:a}=e,{x:o,y:l,dx:c,dy:s}=t;return OD(OD({},z(r,!1)),{},{x:o+i,y:l+n,width:c,height:s,index:a,payload:t})})({node:t,nodeContent:e.node,i:r,top:v,left:m})));return OD(OD({},t),{},{nodes:y,links:h,modifiedLinks:g,modifiedNodes:b,prevData:r,prevWidth:o,prevHeight:i,prevMargin:a,prevNodePadding:s,prevNodeWidth:c,prevIterations:o,prevSort:u})}return null}handleMouseEnter(e,t,r){var{onMouseEnter:n}=this.props;n&&n(e,t,r)}handleMouseLeave(e,t,r){var{onMouseLeave:n}=this.props;n&&n(e,t,r)}handleClick(e,t,r){var{onClick:n}=this.props;n&&n(e,t,r)}render(){var e=this.props,{width:r,height:n,className:i,style:a,children:o}=e,l=xD(e,gD);if(!qo(r)||!qo(n))return null;var{links:c,modifiedNodes:s,modifiedLinks:u}=this.state,f=C(l);return t.createElement(vT,{preloadedState:{options:LD},reduxStoreName:null!=i?i:"Sankey"},t.createElement(Kw,{fn:RD,args:this.props}),t.createElement(vA,{computedData:{links:u,nodes:s}}),t.createElement(ea,{width:r,height:n}),t.createElement(ta,{margin:KD}),t.createElement(cg.Provider,{value:this.state.tooltipPortal},t.createElement(kT,{className:i,style:a,width:r,height:n,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},t.createElement(W,bD({},f,{width:r,height:n}),o,t.createElement(BD,{links:c,modifiedLinks:u,linkContent:this.props.link,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"link",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"link",t),onClick:(e,t)=>this.handleClick(e,"link",t)}),t.createElement(WD,{modifiedNodes:s,nodeContent:this.props.node,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"node",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"node",t),onClick:(e,t)=>this.handleClick(e,"node",t)})))))}}PD(UD,"displayName","Sankey"),PD(UD,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var XD=["axis"],VD={layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},$D=(0,t.forwardRef)(((e,r)=>{var n=pl(e,VD);return t.createElement(UT,{chartName:"RadarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:XD,tooltipPayloadSearcher:pg,categoricalChartProps:n,ref:r})})),HD=["item"],qD=(0,t.forwardRef)(((e,r)=>t.createElement(NT,{chartName:"ScatterChart",defaultTooltipEventType:"item",validateTooltipEventTypes:HD,tooltipPayloadSearcher:pg,categoricalChartProps:e,ref:r}))),YD=["axis"],GD=(0,t.forwardRef)(((e,r)=>t.createElement(NT,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:YD,tooltipPayloadSearcher:pg,categoricalChartProps:e,ref:r}))),ZD=["axis","item"],JD={layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},QD=(0,t.forwardRef)(((e,r)=>{var n=pl(e,JD);return t.createElement(UT,{chartName:"RadialBarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:ZD,tooltipPayloadSearcher:pg,categoricalChartProps:n,ref:r})})),eC=["axis"],tC=(0,t.forwardRef)(((e,r)=>t.createElement(NT,{chartName:"ComposedChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:eC,tooltipPayloadSearcher:pg,categoricalChartProps:e,ref:r})));function rC(){return rC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rC.apply(null,arguments)}function nC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function iC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nC(Object(r),!0).forEach((function(t){aC(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function aC(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var oC={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function lC(e){if(!e.children||0===e.children.length)return 1;var t=e.children.map((e=>lC(e)));return 1+Math.max(...t)}function cC(e){var t={};return e.forEach(((e,r)=>{t[r]=e})),t}function sC(e){var{dataKey:t,nameKey:r,data:n,stroke:i,fill:a,positions:o}=e;return{dataDefinedOnItem:n.children,positions:cC(o),settings:{stroke:i,strokeWidth:void 0,fill:a,nameKey:r,dataKey:t,name:r?void 0:t,hide:!1,type:void 0,color:a,unit:""}}}var uC={top:0,right:0,bottom:0,left:0},fC={options:{validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",chartName:"Sunburst",tooltipPayloadSearcher:(e,t)=>l()(e,t),eventEmitter:void 0}},dC=e=>{var{className:r,data:i,children:a,width:o,height:l,padding:c=2,dataKey:s="value",nameKey:u="name",ringPadding:f=2,innerRadius:d=50,fill:p="#333",stroke:h="#FFF",textOptions:y=oC,outerRadius:v=Math.min(o,l)/2,cx:m=o/2,cy:g=l/2,startAngle:b=0,endAngle:x=360,onClick:w,onMouseEnter:O,onMouseLeave:P}=e,E=Ue(),A=tu([0,i[s]],[0,x]),j=(v-d)/lC(i),S=[],k=new Map([]),[M,T]=(0,t.useState)(null);!function e(r,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,{radius:a,innerR:o,initialAngle:l,childColor:u,nestedActiveTooltipIndex:d}=n,v=l;r&&r.forEach(((r,n)=>{var l,b,x=1===i?"[".concat(n,"]"):function(e){return"".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"","children[").concat(e,"]")}(n,d),j=iC(iC({},r),{},{tooltipIndex:x}),M=A(r[s]),T=v,D=null!==(l=null!==(b=null==r?void 0:r.fill)&&void 0!==b?b:u)&&void 0!==l?l:p,{x:C,y:I}=Jn(0,0,o+a/2,-(T+M-M/2));v+=M,S.push(t.createElement("g",{key:"sunburst-sector-".concat(r.name,"-").concat(n)},t.createElement(tc,{onClick:()=>{return e=j,w&&w(e),void E(kv({activeIndex:e.tooltipIndex,activeDataKey:s,activeCoordinate:k.get(e.name)}));var e},onMouseEnter:e=>function(e,t){O&&O(e,t),E(Av({activeIndex:e.tooltipIndex,activeDataKey:s,activeCoordinate:k.get(e.name)}))}(j,e),onMouseLeave:e=>function(e,t){P&&P(e,t),E(jv())}(j,e),fill:D,stroke:h,strokeWidth:c,startAngle:T,endAngle:T+M,innerRadius:o,outerRadius:o+a,cx:m,cy:g}),t.createElement(mb,rC({},y,{alignmentBaseline:"middle",textAnchor:"middle",x:C+m,y:g-I}),r[s])));var{x:N,y:_}=Jn(m,g,o+a/2,T);return k.set(r.name,{x:N,y:_}),e(r.children,{radius:a,innerR:o+a+f,initialAngle:T,childColor:D,nestedActiveTooltipIndex:x},i+1)}))}(i.children,{radius:j,innerR:d,initialAngle:b});var D=n("recharts-sunburst",r);return t.createElement(cg.Provider,{value:M},t.createElement(kT,{className:r,width:o,height:l,ref:e=>{null==M&&null!=e&&T(e)},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},t.createElement(W,{width:o,height:l},t.createElement(V,{className:D},S),t.createElement(Kw,{fn:sC,args:{dataKey:s,data:i,stroke:h,fill:p,nameKey:u,positions:k}}),a)))},pC=e=>{var r;return t.createElement(vT,{preloadedState:fC,reduxStoreName:null!==(r=e.className)&&void 0!==r?r:"SunburstChart"},t.createElement(ea,{width:e.width,height:e.height}),t.createElement(ta,{margin:uC}),t.createElement(dC,e))};function hC(){return hC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hC.apply(null,arguments)}function yC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function vC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yC(Object(r),!0).forEach((function(t){mC(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function mC(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gC(e,t){var r="".concat(t.x||e.x),n=parseInt(r,10),i="".concat(t.y||e.y),a=parseInt(i,10),o="".concat((null==t?void 0:t.height)||(null==e?void 0:e.height)),l=parseInt(o,10);return vC(vC(vC({},t),Iw(e)),{},{height:l,x:n,y:a})}function bC(e){return t.createElement(Nw,hC({shapeType:"trapezoid",propTransformer:gC},e))}function xC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function wC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xC(Object(r),!0).forEach((function(t){OC(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function OC(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var PC=et([Ki,(e,t)=>t,yp],((e,t,r)=>{var n,{data:i,dataKey:a,nameKey:o,tooltipType:l,lastShapeType:c,reversed:s,customWidth:u,cells:f,presentationProps:d}=t,{chartData:p}=r;if(null!=i&&i.length>0?n=i:null!=p&&p.length>0&&(n=p),n&&n.length)n=n.map(((e,t)=>wC(wC(wC({payload:e},d),e),f&&f[t]&&f[t].props)));else{if(!f||!f.length)return{trapezoids:[],data:n};n=f.map((e=>wC(wC({},d),e.props)))}return function(e){var{dataKey:t,nameKey:r,displayedData:n,tooltipType:i,lastShapeType:a,reversed:o,offset:l,customWidth:c}=e,{left:s,top:u}=l,{realHeight:f,realWidth:d,offsetX:p,offsetY:h}=LC(c,l),y=Math.max.apply(null,n.map((e=>ci(e,t,0)))),v=n.length,m=f/v,g={x:l.left,y:l.top,width:l.width,height:l.height},b=n.map(((e,o)=>{var l,c=ci(e,t,0),f=ci(e,r,o),b=c;o!==v-1?(l=ci(n[o+1],t,0))instanceof Array&&([l]=l):c instanceof Array&&2===c.length?[b,l]=c:l="rectangle"===a?b:0;var x=(y-b)*d/(2*y)+u+25+p,w=m*o+s+h,O=b/y*d,P=l/y*d,E=[{name:f,value:b,payload:e,dataKey:t,type:i}],A={x:x+O/2,y:w+m/2};return kC(kC({x,y:w,width:Math.max(O,P),upperWidth:O,lowerWidth:P,height:m,name:f,val:b,tooltipPayload:E,tooltipPosition:A},qT()(e,["width"])),{},{payload:e,parentViewBox:g,labelViewBox:{x:x+(O-P)/4,y:w,width:Math.abs(O-P)/2+Math.min(O,P),height:m}})}));o&&(b=b.map(((e,t)=>{var r=e.y-t*m+(v-1-t)*m;return kC(kC({},e),{},{upperWidth:e.lowerWidth,lowerWidth:e.upperWidth,x:e.x-(e.lowerWidth-e.upperWidth)/2,y:e.y-t*m+(v-1-t)*m,tooltipPosition:kC(kC({},e.tooltipPosition),{},{y:r+m/2}),labelViewBox:kC(kC({},e.labelViewBox),{},{y:r})})})));return{trapezoids:b,data:n}}({dataKey:a,nameKey:o,displayedData:n,tooltipType:l,lastShapeType:c,reversed:s,offset:e,customWidth:u})})),EC=["onMouseEnter","onClick","onMouseLeave","shape","activeShape"],AC=["stroke","fill","legendType","hide","isAnimationActive","animationBegin","animationDuration","animationEasing","nameKey","lastShapeType"];function jC(){return jC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jC.apply(null,arguments)}function SC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function kC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?SC(Object(r),!0).forEach((function(t){MC(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):SC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function MC(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TC(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function DC(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,tooltipType:c,data:s}=e;return{dataDefinedOnItem:s,positions:e.trapezoids.map((e=>{var{tooltipPosition:t}=e;return t})),settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,name:o,nameKey:r,hide:l,type:c,color:a,unit:""}}}function CC(e){var{trapezoids:r,allOtherFunnelProps:n,showLabels:i}=e,a=He((e=>qm(e,"item",e.tooltip.settings.trigger,void 0))),{onMouseEnter:o,onClick:l,onMouseLeave:c,shape:s,activeShape:u}=n,f=TC(n,EC),d=_w(o,n.dataKey),p=Lw(c),h=Rw(l,n.dataKey);return t.createElement(t.Fragment,null,r.map(((e,r)=>{var n=u&&a===String(r),i=n?u:s,o=kC(kC({},e),{},{option:i,isActive:n,stroke:e.stroke});return t.createElement(V,jC({className:"recharts-funnel-trapezoid"},M(f,e,r),{onMouseEnter:d(e,r),onMouseLeave:p(e,r),onClick:h(e,r),key:"trapezoid-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.name,"-").concat(null==e?void 0:e.value)}),t.createElement(bC,o))})),i&&Ub.renderCallByParent(n,r))}var IC=0;function NC(e){var r,n,i,{previousTrapezoidsRef:a,props:o}=e,{trapezoids:l,isAnimationActive:c,animationBegin:s,animationDuration:u,animationEasing:f,onAnimationEnd:d,onAnimationStart:p}=o,h=a.current,[y,v]=(0,t.useState)(!0),m=(r=l,n=(0,t.useRef)(IC),(i=(0,t.useRef)(r)).current!==r&&(n.current+=1,IC=n.current,i.current=r),n.current),b=(0,t.useCallback)((()=>{"function"==typeof d&&d(),v(!1)}),[d]),x=(0,t.useCallback)((()=>{"function"==typeof p&&p(),v(!0)}),[p]);return t.createElement(oO,{begin:s,duration:u,isActive:c,easing:f,key:m,onAnimationStart:x,onAnimationEnd:b},(e=>{var r=1===e?l:l.map(((t,r)=>{var n=h&&h[r];if(n){var i=g(n.x,t.x),a=g(n.y,t.y),o=g(n.upperWidth,t.upperWidth),l=g(n.lowerWidth,t.lowerWidth),c=g(n.height,t.height);return kC(kC({},t),{},{x:i(e),y:a(e),upperWidth:o(e),lowerWidth:l(e),height:c(e)})}var s=g(t.x+t.upperWidth/2,t.x),u=g(t.y+t.height/2,t.y),f=g(0,t.upperWidth),d=g(0,t.lowerWidth),p=g(0,t.height);return kC(kC({},t),{},{x:s(e),y:u(e),upperWidth:f(e),lowerWidth:d(e),height:p(e)})}));return e>0&&(a.current=r),t.createElement(V,null,t.createElement(CC,{trapezoids:r,allOtherFunnelProps:o,showLabels:!y}))}))}function _C(e){var{trapezoids:r,isAnimationActive:n}=e,i=(0,t.useRef)(null),a=i.current;return n&&r&&r.length&&(!a||a!==r)?t.createElement(NC,{props:e,previousTrapezoidsRef:i}):t.createElement(CC,{trapezoids:r,allOtherFunnelProps:e,showLabels:!0})}var LC=(e,t)=>{var{width:r,height:n,left:i,right:a,top:o,bottom:l}=t,c=n,s=r;return d(e)?s=e:"string"==typeof e&&(s=s*parseFloat(e)/100),{realWidth:s-i-a-50,realHeight:c-l-o,offsetX:(r-s)/2,offsetY:(n-c)/2}};class RC extends t.PureComponent{render(){var{className:e}=this.props,r=n("recharts-trapezoids",e);return t.createElement(V,{className:r},t.createElement(_C,this.props))}}var KC={stroke:"#fff",fill:"#808080",legendType:"rect",hide:!1,isAnimationActive:!Oo.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"};function zC(e){var{height:r,width:n}=NO(),i=pl(e,KC),{stroke:a,fill:o,legendType:l,hide:c,isAnimationActive:s,animationBegin:u,animationDuration:f,animationEasing:d,nameKey:p,lastShapeType:h}=i,y=TC(i,AC),v=C(e),m=R(e.children,zg),g=(0,t.useMemo)((()=>({dataKey:e.dataKey,nameKey:p,data:e.data,tooltipType:e.tooltipType,lastShapeType:h,reversed:e.reversed,customWidth:e.width,cells:m,presentationProps:v})),[e.dataKey,p,e.data,e.tooltipType,h,e.reversed,e.width,m,v]),{trapezoids:b}=He((e=>PC(e,g)));return t.createElement(t.Fragment,null,t.createElement(Kw,{fn:DC,args:kC(kC({},e),{},{trapezoids:b})}),c?null:t.createElement(RC,jC({},y,{stroke:a,fill:o,nameKey:p,lastShapeType:h,animationBegin:u,animationDuration:f,animationEasing:d,isAnimationActive:s,hide:c,legendType:l,height:r,width:n,trapezoids:b})))}class BC extends t.PureComponent{render(){return t.createElement(zC,this.props)}}MC(BC,"displayName","Funnel"),MC(BC,"defaultProps",KC);var FC=["item"],WC=(0,t.forwardRef)(((e,r)=>t.createElement(NT,{chartName:"FunnelChart",defaultTooltipEventType:"item",validateTooltipEventTypes:FC,tooltipPayloadSearcher:pg,categoricalChartProps:e,ref:r})))})(),o})()));
//# sourceMappingURL=Recharts.js.map