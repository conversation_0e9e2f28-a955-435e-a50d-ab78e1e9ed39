/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicPointShadowFilter,o=e.BasicShadowFilter,i=e.Break,n=e.Const,l=e.Continue,s=e.DFGApprox,c=e.D_GGX,m=e.Discard,d=e.EPSILON,p=e.F_<PERSON>hlick,u=e.Fn,g=e.INFINITY,h=e.If,x=e.Loop,f=e.NodeAccess,b=e.NodeShaderStage,w=e.NodeType,_=e.NodeUpdateType,v=e.PCFShadowFilter,S=e.PCFSoftShadowFilter,T=e.PI,y=e.PI2,V=e.PointShadowFilter,M=e.Return,D=e.Schlick_to_F0,F=e.ScriptableNodeResources,I=e.Shader<PERSON>ode,C=e<PERSON>,P=e.Switch,N=e.TBNViewMatrix,R=e.VSMShadowFilter,B=e.V_GGX_SmithCorrelated,A=e.Var,k=e.VarIntent,G=e.abs,L=e.acesFilmicToneMapping,O=e.acos,j=e.add,U=e.addMethodChaining,W=e.addNodeElement,E=e.agxToneMapping,q=e.all,z=e.alphaT,Z=e.and,X=e.anisotropy,K=e.anisotropyB,Y=e.anisotropyT,H=e.any,J=e.append,Q=e.array,$=e.arrayBuffer,ee=e.asin,te=e.assign,re=e.atan,ae=e.atan2,oe=e.atomicAdd,ie=e.atomicAnd,ne=e.atomicFunc,le=e.atomicLoad,se=e.atomicMax,ce=e.atomicMin,me=e.atomicOr,de=e.atomicStore,pe=e.atomicSub,ue=e.atomicXor,ge=e.attenuationColor,he=e.attenuationDistance,xe=e.attribute,fe=e.attributeArray,be=e.backgroundBlurriness,we=e.backgroundIntensity,_e=e.backgroundRotation,ve=e.batch,Se=e.bentNormalView,Te=e.billboarding,ye=e.bitAnd,Ve=e.bitNot,Me=e.bitOr,De=e.bitXor,Fe=e.bitangentGeometry,Ie=e.bitangentLocal,Ce=e.bitangentView,Pe=e.bitangentWorld,Ne=e.bitcast,Re=e.blendBurn,Be=e.blendColor,Ae=e.blendDodge,ke=e.blendOverlay,Ge=e.blendScreen,Le=e.blur,Oe=e.bool,je=e.buffer,Ue=e.bufferAttribute,We=e.bumpMap,Ee=e.burn,qe=e.bvec2,ze=e.bvec3,Ze=e.bvec4,Xe=e.bypass,Ke=e.cache,Ye=e.call,He=e.cameraFar,Je=e.cameraIndex,Qe=e.cameraNear,$e=e.cameraNormalMatrix,et=e.cameraPosition,tt=e.cameraProjectionMatrix,rt=e.cameraProjectionMatrixInverse,at=e.cameraViewMatrix,ot=e.cameraWorldMatrix,it=e.cbrt,nt=e.cdl,lt=e.ceil,st=e.checker,ct=e.cineonToneMapping,mt=e.clamp,dt=e.clearcoat,pt=e.clearcoatNormalView,ut=e.clearcoatRoughness,gt=e.code,ht=e.color,xt=e.colorSpaceToWorking,ft=e.colorToDirection,bt=e.compute,wt=e.computeKernel,_t=e.computeSkinning,vt=e.context,St=e.convert,Tt=e.convertColorSpace,yt=e.convertToTexture,Vt=e.cos,Mt=e.cross,Dt=e.cubeTexture,Ft=e.cubeTextureBase,It=e.cubeToUV,Ct=e.dFdx,Pt=e.dFdy,Nt=e.dashSize,Rt=e.debug,Bt=e.decrement,At=e.decrementBefore,kt=e.defaultBuildStages,Gt=e.defaultShaderStages,Lt=e.defined,Ot=e.degrees,jt=e.deltaTime,Ut=e.densityFog,Wt=e.densityFogFactor,Et=e.depth,qt=e.depthPass,zt=e.determinant,Zt=e.difference,Xt=e.diffuseColor,Kt=e.directPointLight,Yt=e.directionToColor,Ht=e.directionToFaceDirection,Jt=e.dispersion,Qt=e.distance,$t=e.div,er=e.dodge,tr=e.dot,rr=e.drawIndex,ar=e.dynamicBufferAttribute,or=e.element,ir=e.emissive,nr=e.equal,lr=e.equals,sr=e.equirectUV,cr=e.exp,mr=e.exp2,dr=e.expression,pr=e.faceDirection,ur=e.faceForward,gr=e.faceforward,hr=e.float,xr=e.floor,fr=e.fog,br=e.fract,wr=e.frameGroup,_r=e.frameId,vr=e.frontFacing,Sr=e.fwidth,Tr=e.gain,yr=e.gapSize,Vr=e.getConstNodeType,Mr=e.getCurrentStack,Dr=e.getDirection,Fr=e.getDistanceAttenuation,Ir=e.getGeometryRoughness,Cr=e.getNormalFromDepth,Pr=e.getParallaxCorrectNormal,Nr=e.getRoughness,Rr=e.getScreenPosition,Br=e.getShIrradianceAt,Ar=e.getShadowMaterial,kr=e.getShadowRenderObjectFunction,Gr=e.getTextureIndex,Lr=e.getViewPosition,Or=e.globalId,jr=e.glsl,Ur=e.glslFn,Wr=e.grayscale,Er=e.greaterThan,qr=e.greaterThanEqual,zr=e.hash,Zr=e.highpModelNormalViewMatrix,Xr=e.highpModelViewMatrix,Kr=e.hue,Yr=e.increment,Hr=e.incrementBefore,Jr=e.instance,Qr=e.instanceIndex,$r=e.instancedArray,ea=e.instancedBufferAttribute,ta=e.instancedDynamicBufferAttribute,ra=e.instancedMesh,aa=e.int,oa=e.inverse,ia=e.inverseSqrt,na=e.inversesqrt,la=e.invocationLocalIndex,sa=e.invocationSubgroupIndex,ca=e.ior,ma=e.iridescence,da=e.iridescenceIOR,pa=e.iridescenceThickness,ua=e.ivec2,ga=e.ivec3,ha=e.ivec4,xa=e.js,fa=e.label,ba=e.length,wa=e.lengthSq,_a=e.lessThan,va=e.lessThanEqual,Sa=e.lightPosition,Ta=e.lightProjectionUV,ya=e.lightShadowMatrix,Va=e.lightTargetDirection,Ma=e.lightTargetPosition,Da=e.lightViewPosition,Fa=e.lightingContext,Ia=e.lights,Ca=e.linearDepth,Pa=e.linearToneMapping,Na=e.localId,Ra=e.log,Ba=e.log2,Aa=e.logarithmicDepthToViewZ,ka=e.luminance,Ga=e.mat2,La=e.mat3,Oa=e.mat4,ja=e.matcapUV,Ua=e.materialAO,Wa=e.materialAlphaTest,Ea=e.materialAnisotropy,qa=e.materialAnisotropyVector,za=e.materialAttenuationColor,Za=e.materialAttenuationDistance,Xa=e.materialClearcoat,Ka=e.materialClearcoatNormal,Ya=e.materialClearcoatRoughness,Ha=e.materialColor,Ja=e.materialDispersion,Qa=e.materialEmissive,$a=e.materialEnvIntensity,eo=e.materialEnvRotation,to=e.materialIOR,ro=e.materialIridescence,ao=e.materialIridescenceIOR,oo=e.materialIridescenceThickness,io=e.materialLightMap,no=e.materialLineDashOffset,lo=e.materialLineDashSize,so=e.materialLineGapSize,co=e.materialLineScale,mo=e.materialLineWidth,po=e.materialMetalness,uo=e.materialNormal,go=e.materialOpacity,ho=e.materialPointSize,xo=e.materialReference,fo=e.materialReflectivity,bo=e.materialRefractionRatio,wo=e.materialRotation,_o=e.materialRoughness,vo=e.materialSheen,So=e.materialSheenRoughness,To=e.materialShininess,yo=e.materialSpecular,Vo=e.materialSpecularColor,Mo=e.materialSpecularIntensity,Do=e.materialSpecularStrength,Fo=e.materialThickness,Io=e.materialTransmission,Co=e.max,Po=e.maxMipLevel,No=e.mediumpModelViewMatrix,Ro=e.metalness,Bo=e.min,Ao=e.mix,ko=e.mixElement,Go=e.mod,Lo=e.modInt,Oo=e.modelDirection,jo=e.modelNormalMatrix,Uo=e.modelPosition,Wo=e.modelRadius,Eo=e.modelScale,qo=e.modelViewMatrix,zo=e.modelViewPosition,Zo=e.modelViewProjection,Xo=e.modelWorldMatrix,Ko=e.modelWorldMatrixInverse,Yo=e.morphReference,Ho=e.mrt,Jo=e.mul,Qo=e.mx_aastep,$o=e.mx_add,ei=e.mx_atan2,ti=e.mx_cell_noise_float,ri=e.mx_contrast,ai=e.mx_divide,oi=e.mx_fractal_noise_float,ii=e.mx_fractal_noise_vec2,ni=e.mx_fractal_noise_vec3,li=e.mx_fractal_noise_vec4,si=e.mx_frame,ci=e.mx_heighttonormal,mi=e.mx_hsvtorgb,di=e.mx_ifequal,pi=e.mx_ifgreater,ui=e.mx_ifgreatereq,gi=e.mx_invert,hi=e.mx_modulo,xi=e.mx_multiply,fi=e.mx_noise_float,bi=e.mx_noise_vec3,wi=e.mx_noise_vec4,_i=e.mx_place2d,vi=e.mx_power,Si=e.mx_ramp4,Ti=e.mx_ramplr,yi=e.mx_ramptb,Vi=e.mx_rgbtohsv,Mi=e.mx_rotate2d,Di=e.mx_rotate3d,Fi=e.mx_safepower,Ii=e.mx_separate,Ci=e.mx_splitlr,Pi=e.mx_splittb,Ni=e.mx_srgb_texture_to_lin_rec709,Ri=e.mx_subtract,Bi=e.mx_timer,Ai=e.mx_transform_uv,ki=e.mx_unifiednoise2d,Gi=e.mx_unifiednoise3d,Li=e.mx_worley_noise_float,Oi=e.mx_worley_noise_vec2,ji=e.mx_worley_noise_vec3,Ui=e.negate,Wi=e.neutralToneMapping,Ei=e.nodeArray,qi=e.nodeImmutable,zi=e.nodeObject,Zi=e.nodeObjectIntent,Xi=e.nodeObjects,Ki=e.nodeProxy,Yi=e.nodeProxyIntent,Hi=e.normalFlat,Ji=e.normalGeometry,Qi=e.normalLocal,$i=e.normalMap,en=e.normalView,tn=e.normalViewGeometry,rn=e.normalWorld,an=e.normalWorldGeometry,on=e.normalize,nn=e.not,ln=e.notEqual,sn=e.numWorkgroups,cn=e.objectDirection,mn=e.objectGroup,dn=e.objectPosition,pn=e.objectRadius,un=e.objectScale,gn=e.objectViewPosition,hn=e.objectWorldMatrix,xn=e.OnObjectUpdate,fn=e.OnMaterialUpdate,bn=e.oneMinus,wn=e.or,_n=e.orthographicDepthToViewZ,vn=e.oscSawtooth,Sn=e.oscSine,Tn=e.oscSquare,yn=e.oscTriangle,Vn=e.output,Mn=e.outputStruct,Dn=e.overlay,Fn=e.overloadingFn,In=e.parabola,Cn=e.parallaxDirection,Pn=e.parallaxUV,Nn=e.parameter,Rn=e.pass,Bn=e.passTexture,An=e.pcurve,kn=e.perspectiveDepthToViewZ,Gn=e.pmremTexture,Ln=e.pointShadow,On=e.pointUV,jn=e.pointWidth,Un=e.positionGeometry,Wn=e.positionLocal,En=e.positionPrevious,qn=e.positionView,zn=e.positionViewDirection,Zn=e.positionWorld,Xn=e.positionWorldDirection,Kn=e.posterize,Yn=e.pow,Hn=e.pow2,Jn=e.pow3,Qn=e.pow4,$n=e.premultiplyAlpha,el=e.property,tl=e.radians,rl=e.rand,al=e.range,ol=e.rangeFog,il=e.rangeFogFactor,nl=e.reciprocal,ll=e.reference,sl=e.referenceBuffer,cl=e.reflect,ml=e.reflectVector,dl=e.reflectView,pl=e.reflector,ul=e.refract,gl=e.refractVector,hl=e.refractView,xl=e.reinhardToneMapping,fl=e.remap,bl=e.remapClamp,wl=e.renderGroup,_l=e.renderOutput,vl=e.rendererReference,Sl=e.rotate,Tl=e.rotateUV,yl=e.roughness,Vl=e.round,Ml=e.rtt,Dl=e.sRGBTransferEOTF,Fl=e.sRGBTransferOETF,Il=e.sample,Cl=e.sampler,Pl=e.samplerComparison,Nl=e.saturate,Rl=e.saturation,Bl=e.screen,Al=e.screenCoordinate,kl=e.screenSize,Gl=e.screenUV,Ll=e.scriptable,Ol=e.scriptableValue,jl=e.select,Ul=e.setCurrentStack,Wl=e.setName,El=e.shaderStages,ql=e.shadow,zl=e.shadowPositionWorld,Zl=e.shapeCircle,Xl=e.sharedUniformGroup,Kl=e.sheen,Yl=e.sheenRoughness,Hl=e.shiftLeft,Jl=e.shiftRight,Ql=e.shininess,$l=e.sign,es=e.sin,ts=e.sinc,rs=e.skinning,as=e.smoothstep,os=e.smoothstepElement,is=e.specularColor,ns=e.specularF90,ls=e.spherizeUV,ss=e.split,cs=e.spritesheetUV,ms=e.sqrt,ds=e.stack,ps=e.step,us=e.stepElement,gs=e.storage,hs=e.storageBarrier,xs=e.storageObject,fs=e.storageTexture,bs=e.string,ws=e.struct,_s=e.sub,vs=e.subBuild,Ss=e.subgroupIndex,Ts=e.subgroupSize,ys=e.tan,Vs=e.tangentGeometry,Ms=e.tangentLocal,Ds=e.tangentView,Fs=e.tangentWorld,Is=e.temp,Cs=e.texture,Ps=e.texture3D,Ns=e.textureBarrier,Rs=e.textureBicubic,Bs=e.textureBicubicLevel,As=e.textureCubeUV,ks=e.textureLoad,Gs=e.textureSize,Ls=e.textureStore,Os=e.thickness,js=e.time,Us=e.timerDelta,Ws=e.timerGlobal,Es=e.timerLocal,qs=e.toneMapping,zs=e.toneMappingExposure,Zs=e.toonOutlinePass,Xs=e.transformDirection,Ks=e.transformNormal,Ys=e.transformNormalToView,Hs=e.transformedClearcoatNormalView,Js=e.transformedNormalView,Qs=e.transformedNormalWorld,$s=e.transmission,ec=e.transpose,tc=e.triNoise3D,rc=e.triplanarTexture,ac=e.triplanarTextures,oc=e.trunc,ic=e.uint,nc=e.uniform,lc=e.uniformArray,sc=e.uniformCubeTexture,cc=e.uniformGroup,mc=e.uniformTexture,dc=e.unpremultiplyAlpha,pc=e.userData,uc=e.uv,gc=e.uvec2,hc=e.uvec3,xc=e.uvec4,fc=e.varying,bc=e.varyingProperty,wc=e.vec2,_c=e.vec3,vc=e.vec4,Sc=e.vectorComponents,Tc=e.velocity,yc=e.vertexColor,Vc=e.vertexIndex,Mc=e.vertexStage,Dc=e.vibrance,Fc=e.viewZToLogarithmicDepth,Ic=e.viewZToOrthographicDepth,Cc=e.viewZToPerspectiveDepth,Pc=e.viewport,Nc=e.viewportCoordinate,Rc=e.viewportDepthTexture,Bc=e.viewportLinearDepth,Ac=e.viewportMipTexture,kc=e.viewportResolution,Gc=e.viewportSafeUV,Lc=e.viewportSharedTexture,Oc=e.viewportSize,jc=e.viewportTexture,Uc=e.viewportUV,Wc=e.wgsl,Ec=e.wgslFn,qc=e.workgroupArray,zc=e.workgroupBarrier,Zc=e.workgroupId,Xc=e.workingToColorSpace,Kc=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicPointShadowFilter,o as BasicShadowFilter,i as Break,n as Const,l as Continue,s as DFGApprox,c as D_GGX,m as Discard,d as EPSILON,p as F_Schlick,u as Fn,g as INFINITY,h as If,x as Loop,f as NodeAccess,b as NodeShaderStage,w as NodeType,_ as NodeUpdateType,fn as OnMaterialUpdate,xn as OnObjectUpdate,v as PCFShadowFilter,S as PCFSoftShadowFilter,T as PI,y as PI2,V as PointShadowFilter,M as Return,D as Schlick_to_F0,F as ScriptableNodeResources,I as ShaderNode,C as Stack,P as Switch,N as TBNViewMatrix,R as VSMShadowFilter,B as V_GGX_SmithCorrelated,A as Var,k as VarIntent,G as abs,L as acesFilmicToneMapping,O as acos,j as add,U as addMethodChaining,W as addNodeElement,E as agxToneMapping,q as all,z as alphaT,Z as and,X as anisotropy,K as anisotropyB,Y as anisotropyT,H as any,J as append,Q as array,$ as arrayBuffer,ee as asin,te as assign,re as atan,ae as atan2,oe as atomicAdd,ie as atomicAnd,ne as atomicFunc,le as atomicLoad,se as atomicMax,ce as atomicMin,me as atomicOr,de as atomicStore,pe as atomicSub,ue as atomicXor,ge as attenuationColor,he as attenuationDistance,xe as attribute,fe as attributeArray,be as backgroundBlurriness,we as backgroundIntensity,_e as backgroundRotation,ve as batch,Se as bentNormalView,Te as billboarding,ye as bitAnd,Ve as bitNot,Me as bitOr,De as bitXor,Fe as bitangentGeometry,Ie as bitangentLocal,Ce as bitangentView,Pe as bitangentWorld,Ne as bitcast,Re as blendBurn,Be as blendColor,Ae as blendDodge,ke as blendOverlay,Ge as blendScreen,Le as blur,Oe as bool,je as buffer,Ue as bufferAttribute,We as bumpMap,Ee as burn,qe as bvec2,ze as bvec3,Ze as bvec4,Xe as bypass,Ke as cache,Ye as call,He as cameraFar,Je as cameraIndex,Qe as cameraNear,$e as cameraNormalMatrix,et as cameraPosition,tt as cameraProjectionMatrix,rt as cameraProjectionMatrixInverse,at as cameraViewMatrix,ot as cameraWorldMatrix,it as cbrt,nt as cdl,lt as ceil,st as checker,ct as cineonToneMapping,mt as clamp,dt as clearcoat,pt as clearcoatNormalView,ut as clearcoatRoughness,gt as code,ht as color,xt as colorSpaceToWorking,ft as colorToDirection,bt as compute,wt as computeKernel,_t as computeSkinning,vt as context,St as convert,Tt as convertColorSpace,yt as convertToTexture,Vt as cos,Mt as cross,Dt as cubeTexture,Ft as cubeTextureBase,It as cubeToUV,Ct as dFdx,Pt as dFdy,Nt as dashSize,Rt as debug,Bt as decrement,At as decrementBefore,kt as defaultBuildStages,Gt as defaultShaderStages,Lt as defined,Ot as degrees,jt as deltaTime,Ut as densityFog,Wt as densityFogFactor,Et as depth,qt as depthPass,zt as determinant,Zt as difference,Xt as diffuseColor,Kt as directPointLight,Yt as directionToColor,Ht as directionToFaceDirection,Jt as dispersion,Qt as distance,$t as div,er as dodge,tr as dot,rr as drawIndex,ar as dynamicBufferAttribute,or as element,ir as emissive,nr as equal,lr as equals,sr as equirectUV,cr as exp,mr as exp2,dr as expression,pr as faceDirection,ur as faceForward,gr as faceforward,hr as float,xr as floor,fr as fog,br as fract,wr as frameGroup,_r as frameId,vr as frontFacing,Sr as fwidth,Tr as gain,yr as gapSize,Vr as getConstNodeType,Mr as getCurrentStack,Dr as getDirection,Fr as getDistanceAttenuation,Ir as getGeometryRoughness,Cr as getNormalFromDepth,Pr as getParallaxCorrectNormal,Nr as getRoughness,Rr as getScreenPosition,Br as getShIrradianceAt,Ar as getShadowMaterial,kr as getShadowRenderObjectFunction,Gr as getTextureIndex,Lr as getViewPosition,Or as globalId,jr as glsl,Ur as glslFn,Wr as grayscale,Er as greaterThan,qr as greaterThanEqual,zr as hash,Zr as highpModelNormalViewMatrix,Xr as highpModelViewMatrix,Kr as hue,Yr as increment,Hr as incrementBefore,Jr as instance,Qr as instanceIndex,$r as instancedArray,ea as instancedBufferAttribute,ta as instancedDynamicBufferAttribute,ra as instancedMesh,aa as int,oa as inverse,ia as inverseSqrt,na as inversesqrt,la as invocationLocalIndex,sa as invocationSubgroupIndex,ca as ior,ma as iridescence,da as iridescenceIOR,pa as iridescenceThickness,ua as ivec2,ga as ivec3,ha as ivec4,xa as js,fa as label,ba as length,wa as lengthSq,_a as lessThan,va as lessThanEqual,Sa as lightPosition,Ta as lightProjectionUV,ya as lightShadowMatrix,Va as lightTargetDirection,Ma as lightTargetPosition,Da as lightViewPosition,Fa as lightingContext,Ia as lights,Ca as linearDepth,Pa as linearToneMapping,Na as localId,Ra as log,Ba as log2,Aa as logarithmicDepthToViewZ,ka as luminance,Ga as mat2,La as mat3,Oa as mat4,ja as matcapUV,Ua as materialAO,Wa as materialAlphaTest,Ea as materialAnisotropy,qa as materialAnisotropyVector,za as materialAttenuationColor,Za as materialAttenuationDistance,Xa as materialClearcoat,Ka as materialClearcoatNormal,Ya as materialClearcoatRoughness,Ha as materialColor,Ja as materialDispersion,Qa as materialEmissive,$a as materialEnvIntensity,eo as materialEnvRotation,to as materialIOR,ro as materialIridescence,ao as materialIridescenceIOR,oo as materialIridescenceThickness,io as materialLightMap,no as materialLineDashOffset,lo as materialLineDashSize,so as materialLineGapSize,co as materialLineScale,mo as materialLineWidth,po as materialMetalness,uo as materialNormal,go as materialOpacity,ho as materialPointSize,xo as materialReference,fo as materialReflectivity,bo as materialRefractionRatio,wo as materialRotation,_o as materialRoughness,vo as materialSheen,So as materialSheenRoughness,To as materialShininess,yo as materialSpecular,Vo as materialSpecularColor,Mo as materialSpecularIntensity,Do as materialSpecularStrength,Fo as materialThickness,Io as materialTransmission,Co as max,Po as maxMipLevel,No as mediumpModelViewMatrix,Ro as metalness,Bo as min,Ao as mix,ko as mixElement,Go as mod,Lo as modInt,Oo as modelDirection,jo as modelNormalMatrix,Uo as modelPosition,Wo as modelRadius,Eo as modelScale,qo as modelViewMatrix,zo as modelViewPosition,Zo as modelViewProjection,Xo as modelWorldMatrix,Ko as modelWorldMatrixInverse,Yo as morphReference,Ho as mrt,Jo as mul,Qo as mx_aastep,$o as mx_add,ei as mx_atan2,ti as mx_cell_noise_float,ri as mx_contrast,ai as mx_divide,oi as mx_fractal_noise_float,ii as mx_fractal_noise_vec2,ni as mx_fractal_noise_vec3,li as mx_fractal_noise_vec4,si as mx_frame,ci as mx_heighttonormal,mi as mx_hsvtorgb,di as mx_ifequal,pi as mx_ifgreater,ui as mx_ifgreatereq,gi as mx_invert,hi as mx_modulo,xi as mx_multiply,fi as mx_noise_float,bi as mx_noise_vec3,wi as mx_noise_vec4,_i as mx_place2d,vi as mx_power,Si as mx_ramp4,Ti as mx_ramplr,yi as mx_ramptb,Vi as mx_rgbtohsv,Mi as mx_rotate2d,Di as mx_rotate3d,Fi as mx_safepower,Ii as mx_separate,Ci as mx_splitlr,Pi as mx_splittb,Ni as mx_srgb_texture_to_lin_rec709,Ri as mx_subtract,Bi as mx_timer,Ai as mx_transform_uv,ki as mx_unifiednoise2d,Gi as mx_unifiednoise3d,Li as mx_worley_noise_float,Oi as mx_worley_noise_vec2,ji as mx_worley_noise_vec3,Ui as negate,Wi as neutralToneMapping,Ei as nodeArray,qi as nodeImmutable,zi as nodeObject,Zi as nodeObjectIntent,Xi as nodeObjects,Ki as nodeProxy,Yi as nodeProxyIntent,Hi as normalFlat,Ji as normalGeometry,Qi as normalLocal,$i as normalMap,en as normalView,tn as normalViewGeometry,rn as normalWorld,an as normalWorldGeometry,on as normalize,nn as not,ln as notEqual,sn as numWorkgroups,cn as objectDirection,mn as objectGroup,dn as objectPosition,pn as objectRadius,un as objectScale,gn as objectViewPosition,hn as objectWorldMatrix,bn as oneMinus,wn as or,_n as orthographicDepthToViewZ,vn as oscSawtooth,Sn as oscSine,Tn as oscSquare,yn as oscTriangle,Vn as output,Mn as outputStruct,Dn as overlay,Fn as overloadingFn,In as parabola,Cn as parallaxDirection,Pn as parallaxUV,Nn as parameter,Rn as pass,Bn as passTexture,An as pcurve,kn as perspectiveDepthToViewZ,Gn as pmremTexture,Ln as pointShadow,On as pointUV,jn as pointWidth,Un as positionGeometry,Wn as positionLocal,En as positionPrevious,qn as positionView,zn as positionViewDirection,Zn as positionWorld,Xn as positionWorldDirection,Kn as posterize,Yn as pow,Hn as pow2,Jn as pow3,Qn as pow4,$n as premultiplyAlpha,el as property,tl as radians,rl as rand,al as range,ol as rangeFog,il as rangeFogFactor,nl as reciprocal,ll as reference,sl as referenceBuffer,cl as reflect,ml as reflectVector,dl as reflectView,pl as reflector,ul as refract,gl as refractVector,hl as refractView,xl as reinhardToneMapping,fl as remap,bl as remapClamp,wl as renderGroup,_l as renderOutput,vl as rendererReference,Sl as rotate,Tl as rotateUV,yl as roughness,Vl as round,Ml as rtt,Dl as sRGBTransferEOTF,Fl as sRGBTransferOETF,Il as sample,Cl as sampler,Pl as samplerComparison,Nl as saturate,Rl as saturation,Bl as screen,Al as screenCoordinate,kl as screenSize,Gl as screenUV,Ll as scriptable,Ol as scriptableValue,jl as select,Ul as setCurrentStack,Wl as setName,El as shaderStages,ql as shadow,zl as shadowPositionWorld,Zl as shapeCircle,Xl as sharedUniformGroup,Kl as sheen,Yl as sheenRoughness,Hl as shiftLeft,Jl as shiftRight,Ql as shininess,$l as sign,es as sin,ts as sinc,rs as skinning,as as smoothstep,os as smoothstepElement,is as specularColor,ns as specularF90,ls as spherizeUV,ss as split,cs as spritesheetUV,ms as sqrt,ds as stack,ps as step,us as stepElement,gs as storage,hs as storageBarrier,xs as storageObject,fs as storageTexture,bs as string,ws as struct,_s as sub,vs as subBuild,Ss as subgroupIndex,Ts as subgroupSize,ys as tan,Vs as tangentGeometry,Ms as tangentLocal,Ds as tangentView,Fs as tangentWorld,Is as temp,Cs as texture,Ps as texture3D,Ns as textureBarrier,Rs as textureBicubic,Bs as textureBicubicLevel,As as textureCubeUV,ks as textureLoad,Gs as textureSize,Ls as textureStore,Os as thickness,js as time,Us as timerDelta,Ws as timerGlobal,Es as timerLocal,qs as toneMapping,zs as toneMappingExposure,Zs as toonOutlinePass,Xs as transformDirection,Ks as transformNormal,Ys as transformNormalToView,Hs as transformedClearcoatNormalView,Js as transformedNormalView,Qs as transformedNormalWorld,$s as transmission,ec as transpose,tc as triNoise3D,rc as triplanarTexture,ac as triplanarTextures,oc as trunc,ic as uint,nc as uniform,lc as uniformArray,sc as uniformCubeTexture,cc as uniformGroup,mc as uniformTexture,dc as unpremultiplyAlpha,pc as userData,uc as uv,gc as uvec2,hc as uvec3,xc as uvec4,fc as varying,bc as varyingProperty,wc as vec2,_c as vec3,vc as vec4,Sc as vectorComponents,Tc as velocity,yc as vertexColor,Vc as vertexIndex,Mc as vertexStage,Dc as vibrance,Fc as viewZToLogarithmicDepth,Ic as viewZToOrthographicDepth,Cc as viewZToPerspectiveDepth,Pc as viewport,Nc as viewportCoordinate,Rc as viewportDepthTexture,Bc as viewportLinearDepth,Ac as viewportMipTexture,kc as viewportResolution,Gc as viewportSafeUV,Lc as viewportSharedTexture,Oc as viewportSize,jc as viewportTexture,Uc as viewportUV,Wc as wgsl,Ec as wgslFn,qc as workgroupArray,zc as workgroupBarrier,Zc as workgroupId,Xc as workingToColorSpace,Kc as xor};
