{"name": "agentforge-elite", "version": "1.0.0", "description": "Revolutionary AI Agents Orchestration Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh", "deploy": "vercel --prod", "seed": "tsx scripts/seed-db.ts"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.54.0", "@types/three": "^0.179.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.18.2", "lucide-react": "^0.439.0", "nanoid": "^5.1.5", "next": "^14.2.31", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "three": "^0.179.1", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^22.17.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-next": "14.2.15", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.9.2"}, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/agentforge-elite.git"}, "keywords": ["ai", "agents", "automation", "nextjs", "typescript", "react", "supabase", "websocket", "real-time"], "author": "Your Name", "license": "MIT"}