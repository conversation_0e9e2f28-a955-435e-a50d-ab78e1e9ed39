@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================
   CSS Custom Properties (Variables)
   ========================================== */

:root {
  /* Colors */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  --radius: 0.75rem;

  /* AgentForge Elite Custom Properties */
  --agent-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  --neural-gradient: linear-gradient(45deg, #00d4ff 0%, #090979 35%, #ff006e 70%, #8338ec 100%);
  --quantum-gradient: linear-gradient(135deg, #00ff88 0%, #0066ff 25%, #8b5cf6 50%, #ff6b00 75%, #ec4899 100%);
  --glass-primary: rgba(15, 23, 42, 0.85);
  --glass-secondary: rgba(30, 41, 59, 0.7);
  --glass-border: rgba(148, 163, 184, 0.1);
  --glass-border-hover: rgba(34, 197, 94, 0.3);
  --neon-cyan: #00d4ff;
  --neon-purple: #8b5cf6;
  --neon-green: #10b981;
  --neon-pink: #f472b6;
  --neon-orange: #f97316;
  --surface-dark: #0f172a;
  --surface-darker: #020617;
  --text-primary: #f8fafc;
  --text-secondary: rgba(248, 250, 252, 0.8);
  --text-tertiary: rgba(148, 163, 184, 0.9);

  /* Font families */
  --font-inter: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  --font-jetbrains-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  --font-space-grotesk: 'Space Grotesk', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
}

/* ==========================================
   Base Styles
   ========================================== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
  height: 100%;
  font-size: 16px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-inter);
  background: radial-gradient(ellipse at top, var(--surface-dark) 0%, var(--surface-darker) 70%);
  color: var(--text-primary);
  overflow-x: hidden;
  line-height: 1.6;
  min-height: 100vh;
  position: relative;
  font-feature-settings: 'rlig' 1, 'calt' 1;
  font-variation-settings: 'wght' 400;
}

/* ==========================================
   Custom Scrollbar
   ========================================== */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.8);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--agent-gradient);
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
  transform: scale(1.1);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #667eea rgba(15, 20, 25, 0.8);
}

/* ==========================================
   Typography
   ========================================== */

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-space-grotesk);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

h1 {
  font-size: clamp(2.5rem, 8vw, 6rem);
  font-weight: 900;
  letter-spacing: -0.04em;
}

h2 {
  font-size: clamp(2rem, 6vw, 4rem);
  font-weight: 800;
}

h3 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
}

p {
  line-height: 1.7;
  color: var(--text-secondary);
}

code {
  font-family: var(--font-jetbrains-mono);
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

pre {
  font-family: var(--font-jetbrains-mono);
  background: var(--glass-primary);
  border: 1px solid var(--glass-border);
  border-radius: 0.75rem;
  padding: 1rem;
  overflow-x: auto;
  line-height: 1.6;
}

/* ==========================================
   Focus and Accessibility
   ========================================== */

:focus {
  outline: 2px solid var(--neon-cyan);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* ==========================================
   Utility Classes
   ========================================== */

.text-gradient {
  background: var(--agent-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.neural-gradient {
  background: var(--neural-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.quantum-gradient {
  background: var(--quantum-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.glass-effect {
  background: var(--glass-primary);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--glass-border);
}

.glass-effect-secondary {
  background: var(--glass-secondary);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--glass-border);
}

.quantum-shadow {
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.15);
}

.neon-glow {
  box-shadow: 0 0 20px currentColor;
}

.neon-glow-lg {
  box-shadow: 0 0 40px currentColor;
}

/* ==========================================
   Component Animations
   ========================================== */

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes neural-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

@keyframes quantum-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ==========================================
   Animation Classes
   ========================================== */

.animate-neural-pulse {
  animation: neural-pulse 2s ease-in-out infinite;
}

.animate-quantum-spin {
  animation: quantum-spin 2s linear infinite;
}

/* ==========================================
   Layout Utilities
   ========================================== */

.container-fluid {
  width: 100%;
  max-width: 1800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-padding {
  padding: 10rem 2rem;
}

.content-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ==========================================
   Responsive Design
   ========================================== */

@media (max-width: 768px) {
  .container-fluid {
    padding: 0 1rem;
  }

  .section-padding {
    padding: 5rem 1rem;
  }

  h1 {
    font-size: clamp(2rem, 8vw, 3.5rem);
  }

  h2 {
    font-size: clamp(1.5rem, 6vw, 2.5rem);
  }

  h3 {
    font-size: clamp(1.25rem, 4vw, 2rem);
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }

  .container-fluid {
    padding: 0 0.5rem;
  }
}

/* ==========================================
   Reduced Motion Support
   ========================================== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}