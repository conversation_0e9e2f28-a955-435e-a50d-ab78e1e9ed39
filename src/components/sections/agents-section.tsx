'use client'

import * as React from 'react'
import { motion } from 'framer-motion'
import { Search, Filter, Grid } from 'lucide-react'

export function AgentsSection() {
  return (
    <section id="agents" className="py-20 bg-slate-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-black text-gradient mb-6">
            AI Agents Arsenal
          </h2>
          <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
            Discover and deploy the world's most powerful autonomous AI agents. 
            From coding wizards to research specialists, unleash AI's full potential.
          </p>
        </motion.div>

        {/* Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-col md:flex-row items-center justify-between gap-4 mb-12"
        >
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
              <input
                type="text"
                placeholder="Search agents..."
                className="pl-10 pr-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-cyan-400"
              />
            </div>
            <button className="flex items-center gap-2 px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white hover:border-cyan-400 transition-colors">
              <Filter className="h-4 w-4" />
              Filters
            </button>
          </div>
          <button className="flex items-center gap-2 px-4 py-2 bg-slate-800 border border-slate-700 rounded-lg text-white hover:border-cyan-400 transition-colors">
            <Grid className="h-4 w-4" />
            Grid View
          </button>
        </motion.div>

        {/* Agents Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {[
            {
              name: 'CodeMaster Pro',
              category: 'Coding',
              description: 'Advanced AI agent for code generation, debugging, and optimization.',
              icon: '💻',
              status: 'Active',
            },
            {
              name: 'Research Assistant',
              category: 'Research',
              description: 'Intelligent research agent for data analysis and insights.',
              icon: '🔬',
              status: 'Active',
            },
            {
              name: 'Content Creator',
              category: 'Creative',
              description: 'AI-powered content generation and creative writing assistant.',
              icon: '✍️',
              status: 'Active',
            },
            {
              name: 'Data Analyst',
              category: 'Analytics',
              description: 'Powerful data processing and visualization agent.',
              icon: '📊',
              status: 'Active',
            },
            {
              name: 'Task Automator',
              category: 'Automation',
              description: 'Workflow automation and task management specialist.',
              icon: '⚙️',
              status: 'Active',
            },
            {
              name: 'Customer Support',
              category: 'Support',
              description: 'Intelligent customer service and support agent.',
              icon: '🎧',
              status: 'Active',
            },
          ].map((agent, index) => (
            <motion.div
              key={agent.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
              className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 hover:border-cyan-400 rounded-xl p-6 transition-all duration-300 cursor-pointer"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="text-3xl">{agent.icon}</div>
                <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                  {agent.status}
                </span>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">{agent.name}</h3>
              <p className="text-slate-400 text-sm mb-4">{agent.description}</p>
              <div className="flex items-center justify-between">
                <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">
                  {agent.category}
                </span>
                <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors">
                  Deploy
                </button>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
