'use client'

import * as React from 'react'
import { motion } from 'framer-motion'
import { BarChart3, TrendingUp, Activity, Zap } from 'lucide-react'

export function AnalyticsSection() {
  return (
    <section className="py-20 bg-slate-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-black text-gradient mb-6">
            Advanced Analytics
          </h2>
          <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
            Deep insights into agent performance, resource utilization, and system optimization. 
            Data-driven intelligence for maximum efficiency.
          </p>
        </motion.div>

        {/* Metrics Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
        >
          {[
            {
              icon: <BarChart3 className="h-8 w-8" />,
              title: 'Tasks Completed',
              value: '12,847',
              change: '+23%',
              color: 'text-green-400',
            },
            {
              icon: <TrendingUp className="h-8 w-8" />,
              title: 'Efficiency Rate',
              value: '94.2%',
              change: '****%',
              color: 'text-blue-400',
            },
            {
              icon: <Activity className="h-8 w-8" />,
              title: 'Active Agents',
              value: '47',
              change: '+12',
              color: 'text-purple-400',
            },
            {
              icon: <Zap className="h-8 w-8" />,
              title: 'Response Time',
              value: '8.3ms',
              change: '-2.1ms',
              color: 'text-orange-400',
            },
          ].map((metric, index) => (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 hover:border-cyan-400 rounded-xl p-6 transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`p-2 rounded-lg bg-slate-700 ${metric.color}`}>
                  {metric.icon}
                </div>
                <span className={`text-sm font-medium ${metric.color}`}>
                  {metric.change}
                </span>
              </div>
              <div className="text-2xl font-bold text-white mb-1">
                {metric.value}
              </div>
              <div className="text-sm text-slate-400">
                {metric.title}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Performance Chart */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6"
          >
            <h3 className="text-xl font-bold text-white mb-6">Agent Performance</h3>
            <div className="h-64 flex items-end justify-between gap-2">
              {[65, 78, 82, 91, 88, 95, 92, 89, 94, 97, 93, 96].map((height, index) => (
                <motion.div
                  key={index}
                  initial={{ height: 0 }}
                  whileInView={{ height: `${height}%` }}
                  transition={{ duration: 0.8, delay: 0.8 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gradient-to-t from-blue-500 to-purple-500 rounded-t-sm flex-1 min-h-[4px]"
                />
              ))}
            </div>
            <div className="flex justify-between text-xs text-slate-400 mt-2">
              <span>Jan</span>
              <span>Feb</span>
              <span>Mar</span>
              <span>Apr</span>
              <span>May</span>
              <span>Jun</span>
            </div>
          </motion.div>

          {/* Resource Usage */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            viewport={{ once: true }}
            className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6"
          >
            <h3 className="text-xl font-bold text-white mb-6">Resource Utilization</h3>
            <div className="space-y-4">
              {[
                { name: 'CPU Usage', value: 68, color: 'bg-blue-500' },
                { name: 'Memory', value: 45, color: 'bg-green-500' },
                { name: 'Network', value: 32, color: 'bg-purple-500' },
                { name: 'Storage', value: 78, color: 'bg-orange-500' },
              ].map((resource, index) => (
                <div key={resource.name} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-300">{resource.name}</span>
                    <span className="text-slate-400">{resource.value}%</span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      whileInView={{ width: `${resource.value}%` }}
                      transition={{ duration: 1, delay: 1 + index * 0.2 }}
                      viewport={{ once: true }}
                      className={`h-2 ${resource.color} rounded-full`}
                    />
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Real-time Activity */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          viewport={{ once: true }}
          className="mt-12 bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6"
        >
          <h3 className="text-xl font-bold text-white mb-6">Real-time Activity Feed</h3>
          <div className="space-y-3">
            {[
              { time: '14:32:15', agent: 'CodeMaster', action: 'Completed React component generation', status: 'success' },
              { time: '14:31:48', agent: 'DataAnalyst', action: 'Processing customer analytics data', status: 'running' },
              { time: '14:31:22', agent: 'TaskAutomator', action: 'Deployed to production environment', status: 'success' },
              { time: '14:30:55', agent: 'ResearchBot', action: 'Gathering market research insights', status: 'running' },
              { time: '14:30:31', agent: 'ContentCreator', action: 'Generated blog post draft', status: 'success' },
            ].map((activity, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 1.2 + index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center gap-4 p-3 bg-slate-700/30 rounded-lg"
              >
                <div className="text-xs text-slate-400 font-mono">
                  {activity.time}
                </div>
                <div className={`w-2 h-2 rounded-full ${
                  activity.status === 'success' ? 'bg-green-400' : 'bg-blue-400 animate-pulse'
                }`} />
                <div className="flex-1">
                  <span className="text-cyan-400 font-medium">{activity.agent}</span>
                  <span className="text-slate-300 ml-2">{activity.action}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
