'use client'

import * as React from 'react'
import { motion } from 'framer-motion'
import { Users, MessageSquare, Share2, Zap } from 'lucide-react'

export function CollaborationSection() {
  return (
    <section className="py-20 bg-slate-800">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-black text-gradient mb-6">
            Seamless Collaboration
          </h2>
          <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
            AI agents that work together, learn from each other, and evolve as a unified intelligence network.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Collaboration Features */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {[
              {
                icon: <Users className="h-8 w-8" />,
                title: 'Multi-Agent Coordination',
                description: 'Agents automatically coordinate tasks, share resources, and optimize workflows together.',
              },
              {
                icon: <MessageSquare className="h-8 w-8" />,
                title: 'Inter-Agent Communication',
                description: 'Real-time messaging and data exchange between agents for seamless collaboration.',
              },
              {
                icon: <Share2 className="h-8 w-8" />,
                title: 'Knowledge Sharing',
                description: 'Agents learn from each other\'s experiences and share insights across the network.',
              },
              {
                icon: <Zap className="h-8 w-8" />,
                title: 'Dynamic Load Balancing',
                description: 'Intelligent task distribution based on agent capabilities and current workload.',
              },
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start gap-4"
              >
                <div className="flex-shrink-0 p-3 bg-slate-700 rounded-lg text-cyan-400">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                  <p className="text-slate-400">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Collaboration Visualization */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="bg-slate-900 rounded-xl border border-slate-700 p-8">
              <h3 className="text-xl font-bold text-white mb-6 text-center">Agent Network Activity</h3>
              
              {/* Network Visualization */}
              <div className="relative h-64 flex items-center justify-center">
                {/* Central Hub */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <Zap className="h-8 w-8 text-white" />
                  </div>
                </div>

                {/* Agent Nodes */}
                {[
                  { name: 'CodeMaster', position: 'top-4 left-1/2 transform -translate-x-1/2', color: 'bg-blue-500' },
                  { name: 'DataAnalyst', position: 'top-1/2 right-4 transform -translate-y-1/2', color: 'bg-green-500' },
                  { name: 'TaskAutomator', position: 'bottom-4 left-1/2 transform -translate-x-1/2', color: 'bg-purple-500' },
                  { name: 'ResearchBot', position: 'top-1/2 left-4 transform -translate-y-1/2', color: 'bg-orange-500' },
                ].map((agent, index) => (
                  <motion.div
                    key={agent.name}
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                    viewport={{ once: true }}
                    className={`absolute ${agent.position}`}
                  >
                    <div className={`w-12 h-12 ${agent.color} rounded-full flex items-center justify-center relative`}>
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                      <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-slate-400 whitespace-nowrap">
                        {agent.name}
                      </div>
                    </div>
                  </motion.div>
                ))}

                {/* Connection Lines */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none">
                  <defs>
                    <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.6" />
                      <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.6" />
                    </linearGradient>
                  </defs>
                  {/* Animated connection lines */}
                  <motion.line
                    x1="50%" y1="50%" x2="50%" y2="20%"
                    stroke="url(#connectionGradient)"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    whileInView={{ pathLength: 1 }}
                    transition={{ duration: 1, delay: 1 }}
                    viewport={{ once: true }}
                  />
                  <motion.line
                    x1="50%" y1="50%" x2="80%" y2="50%"
                    stroke="url(#connectionGradient)"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    whileInView={{ pathLength: 1 }}
                    transition={{ duration: 1, delay: 1.2 }}
                    viewport={{ once: true }}
                  />
                  <motion.line
                    x1="50%" y1="50%" x2="50%" y2="80%"
                    stroke="url(#connectionGradient)"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    whileInView={{ pathLength: 1 }}
                    transition={{ duration: 1, delay: 1.4 }}
                    viewport={{ once: true }}
                  />
                  <motion.line
                    x1="50%" y1="50%" x2="20%" y2="50%"
                    stroke="url(#connectionGradient)"
                    strokeWidth="2"
                    initial={{ pathLength: 0 }}
                    whileInView={{ pathLength: 1 }}
                    transition={{ duration: 1, delay: 1.6 }}
                    viewport={{ once: true }}
                  />
                </svg>
              </div>

              {/* Activity Feed */}
              <div className="mt-6 space-y-2 text-sm">
                <div className="flex items-center gap-2 text-slate-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>CodeMaster shared code template with TaskAutomator</span>
                </div>
                <div className="flex items-center gap-2 text-slate-400">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                  <span>DataAnalyst processing shared dataset from ResearchBot</span>
                </div>
                <div className="flex items-center gap-2 text-slate-400">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
                  <span>Network optimization completed - 23% efficiency gain</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
