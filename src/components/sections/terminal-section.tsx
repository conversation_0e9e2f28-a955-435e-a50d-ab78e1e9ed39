'use client'

import * as React from 'react'
import { motion } from 'framer-motion'
import { Terminal, Play, Pause, Square } from 'lucide-react'

export function TerminalSection() {
  return (
    <section className="py-20 bg-slate-900">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-black text-gradient mb-6">
            AI-Powered Terminal
          </h2>
          <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
            Execute commands with AI assistance. Natural language processing meets powerful automation.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-slate-800 rounded-xl border border-slate-700 overflow-hidden">
            {/* Terminal Header */}
            <div className="flex items-center justify-between px-4 py-3 bg-slate-900 border-b border-slate-700">
              <div className="flex items-center gap-3">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <Terminal className="h-4 w-4 text-slate-400" />
                <span className="text-slate-400 text-sm">AgentForge Terminal</span>
              </div>
              <div className="flex items-center gap-2">
                <button className="p-1 text-slate-400 hover:text-green-400 transition-colors">
                  <Play className="h-4 w-4" />
                </button>
                <button className="p-1 text-slate-400 hover:text-yellow-400 transition-colors">
                  <Pause className="h-4 w-4" />
                </button>
                <button className="p-1 text-slate-400 hover:text-red-400 transition-colors">
                  <Square className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Terminal Content */}
            <div className="p-6 font-mono text-sm">
              <div className="space-y-2">
                <div className="text-green-400">
                  $ agentforge --help
                </div>
                <div className="text-slate-300 ml-4">
                  AgentForge Elite - AI Agents Orchestration Platform
                  <br />
                  <br />
                  Commands:
                  <br />
                  &nbsp;&nbsp;deploy &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Deploy an AI agent
                  <br />
                  &nbsp;&nbsp;list &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;List all active agents
                  <br />
                  &nbsp;&nbsp;monitor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Monitor agent performance
                  <br />
                  &nbsp;&nbsp;configure &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configure agent settings
                </div>
                <div className="text-green-400">
                  $ agentforge deploy --name CodeMaster --type coding
                </div>
                <div className="text-blue-400 ml-4">
                  🚀 Deploying CodeMaster agent...
                  <br />
                  ✓ Environment validated
                  <br />
                  ✓ Dependencies installed
                  <br />
                  ✓ Agent initialized
                  <br />
                  ✓ Health check passed
                  <br />
                  <span className="text-green-400">✓ CodeMaster deployed successfully!</span>
                </div>
                <div className="text-green-400">
                  $ agentforge list
                </div>
                <div className="text-slate-300 ml-4">
                  Active Agents:
                  <br />
                  &nbsp;&nbsp;• CodeMaster &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[RUNNING] &nbsp;&nbsp;CPU: 15% &nbsp;&nbsp;Memory: 256MB
                  <br />
                  &nbsp;&nbsp;• DataAnalyst &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[RUNNING] &nbsp;&nbsp;CPU: 8% &nbsp;&nbsp;&nbsp;Memory: 128MB
                  <br />
                  &nbsp;&nbsp;• TaskAutomator &nbsp;&nbsp;&nbsp;[RUNNING] &nbsp;&nbsp;CPU: 12% &nbsp;&nbsp;Memory: 192MB
                </div>
                <div className="text-green-400 flex items-center">
                  $ <span className="animate-pulse ml-1">|</span>
                </div>
              </div>
            </div>
          </div>

          {/* Terminal Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            {[
              {
                title: 'Natural Language',
                description: 'Use plain English to control your AI agents',
                icon: '🗣️',
              },
              {
                title: 'Real-time Feedback',
                description: 'Get instant updates on agent status and performance',
                icon: '⚡',
              },
              {
                title: 'Advanced Scripting',
                description: 'Create complex automation workflows with ease',
                icon: '📜',
              },
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 bg-slate-800/50 rounded-lg border border-slate-700"
              >
                <div className="text-3xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-bold text-white mb-2">{feature.title}</h3>
                <p className="text-slate-400 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
