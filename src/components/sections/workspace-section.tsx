'use client'

import * as React from 'react'
import { motion } from 'framer-motion'
import { Monitor, Code, Terminal, Settings } from 'lucide-react'

export function WorkspaceSection() {
  return (
    <section id="workspace" className="py-20 bg-slate-800">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-black text-gradient mb-6">
            Intelligent Workspace
          </h2>
          <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
            A unified environment where AI agents collaborate, code, and create. 
            Experience the future of intelligent development.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Workspace Preview */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-slate-900 rounded-xl border border-slate-700 overflow-hidden"
          >
            <div className="flex items-center gap-2 px-4 py-3 bg-slate-800 border-b border-slate-700">
              <div className="flex gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <span className="text-slate-400 text-sm ml-4">AgentForge Workspace</span>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Code className="h-5 w-5 text-blue-400" />
                  <span className="text-slate-300">CodeMaster Pro is generating React components...</span>
                </div>
                <div className="flex items-center gap-3">
                  <Terminal className="h-5 w-5 text-green-400" />
                  <span className="text-slate-300">Task Automator is running deployment scripts...</span>
                </div>
                <div className="flex items-center gap-3">
                  <Monitor className="h-5 w-5 text-purple-400" />
                  <span className="text-slate-300">Data Analyst is processing metrics...</span>
                </div>
              </div>
              <div className="mt-6 bg-slate-800 rounded-lg p-4">
                <div className="text-green-400 text-sm font-mono">
                  $ agent deploy --name CodeMaster --env production
                  <br />
                  ✓ Agent deployed successfully
                  <br />
                  ✓ Health check passed
                  <br />
                  ✓ Ready to receive tasks
                </div>
              </div>
            </div>
          </motion.div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {[
              {
                icon: <Code className="h-8 w-8" />,
                title: 'Collaborative Coding',
                description: 'AI agents work together to write, review, and optimize code in real-time.',
              },
              {
                icon: <Terminal className="h-8 w-8" />,
                title: 'Intelligent Terminal',
                description: 'Command-line interface with AI assistance for complex operations.',
              },
              {
                icon: <Monitor className="h-8 w-8" />,
                title: 'Real-time Monitoring',
                description: 'Track agent performance, resource usage, and task completion.',
              },
              {
                icon: <Settings className="h-8 w-8" />,
                title: 'Advanced Configuration',
                description: 'Fine-tune agent behavior and workflow parameters.',
              },
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start gap-4"
              >
                <div className="flex-shrink-0 p-3 bg-slate-700 rounded-lg text-cyan-400">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                  <p className="text-slate-400">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}
